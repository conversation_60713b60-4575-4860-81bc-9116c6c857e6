<?php

function get_product_info_fields($product_id) {
    return db_query(
        "SELECT * FROM product_info_fields WHERE product_id = :pid ORDER BY sort_order ASC",
        [':pid' => $product_id],
        false, true
    );
}

function add_product_info_field($product_id, $icon, $text, $sort_order = null) {
    
    $icon = trim($icon);
    $text = trim($text);

    
    if (empty($icon) && empty($text)) {
        return false;
    }

    

    
    $existing_field = db_query(
        "SELECT id FROM product_info_fields WHERE product_id = :pid AND icon = :icon AND text = :text LIMIT 1",
        [':pid' => $product_id, ':icon' => $icon, ':text' => $text],
        true
    );

    
    if ($existing_field && isset($existing_field['id'])) {
        return $existing_field['id'];
    }

    
    $similar_field = db_query(
        "SELECT id FROM product_info_fields WHERE product_id = :pid AND icon = :icon AND text LIKE :text_pattern LIMIT 1",
        [':pid' => $product_id, ':icon' => $icon, ':text_pattern' => $text . '%'],
        true
    );

    if ($similar_field && isset($similar_field['id'])) {
        return $similar_field['id'];
    }

    
    $similar_field2 = db_query(
        "SELECT id FROM product_info_fields WHERE product_id = :pid AND icon = :icon AND LOWER(TRIM(text)) = LOWER(:text) LIMIT 1",
        [':pid' => $product_id, ':icon' => $icon, ':text' => $text],
        true
    );

    if ($similar_field2 && isset($similar_field2['id'])) {
        return $similar_field2['id'];
    }

    if ($sort_order === null) {
        
        $max_sort = db_query(
            "SELECT IFNULL(MAX(sort_order), -1) as max_sort FROM product_info_fields WHERE product_id = :pid",
            [':pid' => $product_id],
            true
        );
        $sort_order = ($max_sort && isset($max_sort['max_sort'])) ? (int)$max_sort['max_sort'] + 1 : 0;
    }

    $result = db_query(
        "INSERT INTO product_info_fields (product_id, icon, text, sort_order) VALUES (:pid, :icon, :text, :sort)",
        [
            ':pid' => $product_id,
            ':icon' => $icon,
            ':text' => $text,
            ':sort' => $sort_order
        ]
    );

    if ($result) {
        $pdo = get_db_connection();
        return $pdo->lastInsertId();
    }

    return false;
}

function update_product_info_field($field_id, $icon, $text, $sort_order = null) {
    $params = [
        ':id' => $field_id,
        ':icon' => $icon,
        ':text' => $text
    ];

    $sql = "UPDATE product_info_fields SET icon = :icon, text = :text";

    if ($sort_order !== null) {
        $sql .= ", sort_order = :sort";
        $params[':sort'] = $sort_order;
    }

    $sql .= " WHERE id = :id";

    return db_query($sql, $params) !== false;
}

function delete_product_info_field($field_id) {
    return db_query(
        "DELETE FROM product_info_fields WHERE id = :id",
        [':id' => $field_id]
    ) !== false;
}

function reorder_product_info_fields($field_ids) {
    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        foreach ($field_ids as $index => $field_id) {
            db_query(
                "UPDATE product_info_fields SET sort_order = :sort WHERE id = :id",
                [':sort' => $index, ':id' => $field_id]
            );
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}
