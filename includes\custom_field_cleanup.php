<?php

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/db.php';
require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/custom_field_functions.php';

function cleanup_duplicate_custom_fields(): array
{
    $result = [
        'duplicates_found' => 0,
        'fields_merged' => 0,
        'errors' => []
    ];
    
    try {
        $pdo = get_db_connection();
        
        
        $pdo->beginTransaction();
        
        
        $find_duplicates_sql = "
            SELECT name, COUNT(*) as count, GROUP_CONCAT(id) as ids
            FROM custom_fields
            GROUP BY name
            HAVING count > 1
        ";
        
        $stmt = $pdo->query($find_duplicates_sql);
        $duplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result['duplicates_found'] = count($duplicates);
        
        if (empty($duplicates)) {
            
            $pdo->commit();
            return $result;
        }
        
        
        foreach ($duplicates as $duplicate) {
            $name = $duplicate['name'];
            $ids = explode(',', $duplicate['ids']);
            
            
            $primary_id = (int)$ids[0];
            $duplicate_ids = array_slice($ids, 1);
            
            
            $update_product_fields_sql = "
                UPDATE product_custom_fields
                SET custom_field_id = :primary_id
                WHERE custom_field_id IN (" . implode(',', array_map(function($id) { return ':dup_' . $id; }, $duplicate_ids)) . ")
            ";
            
            $update_stmt = $pdo->prepare($update_product_fields_sql);
            $update_stmt->bindParam(':primary_id', $primary_id, PDO::PARAM_INT);
            
            foreach ($duplicate_ids as $index => $dup_id) {
                $update_stmt->bindValue(':dup_' . $dup_id, $dup_id, PDO::PARAM_INT);
            }
            
            $update_stmt->execute();
            
            
            $update_order_fields_sql = "
                UPDATE order_item_custom_fields
                SET custom_field_id = :primary_id
                WHERE custom_field_id IN (" . implode(',', array_map(function($id) { return ':dup_' . $id; }, $duplicate_ids)) . ")
            ";
            
            $update_stmt = $pdo->prepare($update_order_fields_sql);
            $update_stmt->bindParam(':primary_id', $primary_id, PDO::PARAM_INT);
            
            foreach ($duplicate_ids as $index => $dup_id) {
                $update_stmt->bindValue(':dup_' . $dup_id, $dup_id, PDO::PARAM_INT);
            }
            
            $update_stmt->execute();
            
            
            $delete_sql = "
                DELETE FROM custom_fields
                WHERE id IN (" . implode(',', array_map(function($id) { return ':dup_' . $id; }, $duplicate_ids)) . ")
            ";
            
            $delete_stmt = $pdo->prepare($delete_sql);
            
            foreach ($duplicate_ids as $index => $dup_id) {
                $delete_stmt->bindValue(':dup_' . $dup_id, $dup_id, PDO::PARAM_INT);
            }
            
            $delete_stmt->execute();
            
            $result['fields_merged']++;
        }
        
        
        $pdo->commit();
        
    } catch (PDOException $e) {
        
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $error_message = "Error cleaning up duplicate custom fields: " . $e->getMessage();
        $result['errors'][] = $error_message;
    }
    
    return $result;
}

function add_unique_constraint_to_custom_fields(): bool
{
    try {
        $pdo = get_db_connection();
        
        
        $pdo->beginTransaction();
        
        
        $create_new_table_sql = "
            CREATE TABLE custom_fields_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                field_type_id INTEGER NOT NULL,
                min_chars INTEGER DEFAULT 0,
                max_chars INTEGER DEFAULT 255,
                price_modifier REAL DEFAULT 0.0,
                is_required INTEGER NOT NULL DEFAULT 0,
                is_active INTEGER NOT NULL DEFAULT 1,
                config_json TEXT,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                FOREIGN KEY (field_type_id) REFERENCES custom_field_types(id) ON DELETE RESTRICT
            )
        ";
        
        $pdo->exec($create_new_table_sql);
        
        
        $copy_data_sql = "
            INSERT INTO custom_fields_new
            SELECT * FROM custom_fields
        ";
        
        $pdo->exec($copy_data_sql);
        
        
        $drop_old_table_sql = "DROP TABLE custom_fields";
        $pdo->exec($drop_old_table_sql);
        
        
        $rename_table_sql = "ALTER TABLE custom_fields_new RENAME TO custom_fields";
        $pdo->exec($rename_table_sql);
        
        
        $pdo->commit();
        
        return true;
        
    } catch (PDOException $e) {
        
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}
