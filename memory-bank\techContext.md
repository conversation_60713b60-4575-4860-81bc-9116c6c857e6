# Memory Bank File: techContext.md

## Core Technologies
*   **Backend Language:** PHP (Version 8.2+ adherence preferred).
*   **Coding Standards:** PHP-FIG PSR-12.
*   **Database:** SQLite. The database file (e.g., `../dbjcs2112ew.sqlite`) is located outside the web root for security.
*   **Frontend Technologies:** HTML, CSS. JavaScript usage is generally for light enhancements and simple confirmations, avoiding complex client-side frameworks or heavy AJAX for core interactions as per user preference (e.g., coupon application AJAX is an exception, followed by page refresh).
*   **Configuration:** `config.php` file containing critical settings like `CSRF_SECRET`, `DB_PATH`.

## Development Environment & Setup
*   Standard PHP development environment (e.g., LAMP/LEMP, XAMPP, Docker).
*   Access to `config.php` for critical constants.
*   File/folder permissions allowing PHP to write to `logs/` and any designated backup/upload directories. Secure storage for digital files outside web root.

## Technical Constraints & Considerations
*   **Strictly Cookieless:** No browser cookies are allowed for any functionality. All session-like behavior or user tracking must use alternative server-side or database-driven methods (e.g., `sessions` table, `order_visits` table).
*   **Security:** High priority. Implementations must consider XSS, CSRF, SQL injection, data exposure, file inclusion, and other common web vulnerabilities. Use of `CSRF_SECRET` for cryptographic operations (e.g., data encryption, token generation).
*   **`eval()` Usage**: The "CODE" blog post type uses `eval()` to execute stored PHP/HTML/JS. This introduces a **critical security risk** (Remote Code Execution). This feature must be handled with extreme caution, and content should be rigorously vetted. It is strongly advised against for environments exposed to untrusted users.
*   **SQLite Limitations:** Be mindful of SQLite's write concurrency. Design operations to be efficient. Database maintenance (VACUUM, ANALYZE) is important.
*   **Performance:** While simplicity is preferred, performance implications of database queries (especially for tracking and frequent updates) should be considered.
*   **Maintainability:** Code should be clean, well-structured, and adhere to PSR-12. Unnecessary comments and debug code (`error_log`, `console.log`) must be removed from production/committed code. Automation for cleanup tasks is preferred.
*   **Error Reporting:** `error_log` statements are for debugging only. Comprehensive server-side logging to `/logs/error.log`.

## Dependencies
*   **`CSRF_SECRET`:** Constant in `config.php`, used as salt for encryption and CSRF tokens.
*   **`DB_PATH`:** Constant in `config.php`, defining the path to the SQLite database file.
*   **PHP Version:** PHP 8.2+.

## Tool Usage Patterns
*   **Automated Scripts:** User preference for creating automated scripts for code cleanup tasks.
*   **Version Control:** Assumed (e.g., Git).

## Database Interaction
*   PHP scripts interact with the SQLite database via functions typically in `db.php` and model-specific files in `/includes/`.
*   All queries use prepared statements.
*   File `db.php` should be kept up-to-date with any core schema interaction logic or connection details. Database schema changes should be documented.