<?php
// This is a test script to debug file type associations

// Include necessary files
require_once __DIR__ . '/includes/init.php';
require_once __DIR__ . '/includes/digital_files_functions.php';

// Set content type to JSON
header('Content-Type: application/json');

// Get file ID from query string
$file_id = isset($_GET['file_id']) ? (int)$_GET['file_id'] : 0;

if ($file_id <= 0) {
    echo json_encode(['error' => 'Invalid file ID']);
    exit;
}

try {
    // Get file details
    $file = get_digital_file_by_id($file_id);
    
    if (!$file) {
        echo json_encode(['error' => 'File not found']);
        exit;
    }
    
    // Get file type IDs
    $file_type_ids = get_digital_file_file_type_ids($file_id);
    
    // Get file types
    $file_types = [];
    if (!empty($file_type_ids)) {
        foreach ($file_type_ids as $type_id) {
            $type = db_query("SELECT * FROM digital_product_file_types WHERE id = :id", [':id' => $type_id], true);
            if ($type) {
                $file_types[] = $type;
            }
        }
    }
    
    // Get digital products using this file
    $products = db_query(
        "SELECT p.id, p.name_pt FROM products p
         JOIN digital_products dp ON p.id = dp.product_id
         WHERE dp.digital_file_id = :file_id",
        [':file_id' => $file_id],
        false,
        true
    );
    
    // Get product file type associations
    $product_file_type_ids = [];
    if (is_array($products) && !empty($products)) {
        foreach ($products as $product) {
            $product_id = (int)$product['id'];
            $digital_product = db_query(
                "SELECT id FROM digital_products WHERE product_id = :product_id",
                [':product_id' => $product_id],
                true
            );
            
            if ($digital_product) {
                $digital_product_id = (int)$digital_product['id'];
                $product_file_types = get_digital_product_file_type_ids($digital_product_id);
                $product_file_type_ids[$product_id] = $product_file_types;
            }
        }
    }
    
    // Return all data
    echo json_encode([
        'success' => true,
        'file' => $file,
        'file_type_ids' => $file_type_ids,
        'file_types' => $file_types,
        'products' => $products,
        'product_file_type_ids' => $product_file_type_ids
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo json_encode([
        'error' => 'Exception: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
