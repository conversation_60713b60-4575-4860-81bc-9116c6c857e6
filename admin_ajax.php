<?php

ob_start();

ini_set('display_errors', 0);
error_reporting(E_ALL);

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/security.php';
require_once __DIR__ . '/includes/vat_functions.php';
require_once __DIR__ . '/includes/sitemap_functions.php'; 
require_once __DIR__ . '/includes/session.php'; 

$current_session_id = start_cookieless_session();
if (!$current_session_id) {
    
    send_json_response(false, 'Falha crítica ao iniciar a sessão.');
}

function send_json_response($success, $message = '', $data = []) {
    
    while (ob_get_level() > 0) {
        ob_end_clean();
    }

    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

$is_admin_logged_in = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
if (!$is_admin_logged_in) {
    send_json_response(false, 'Não tem permissão para aceder a esta área.');
}

$submitted_csrf = $_POST['csrf_token'] ?? '';
if (!validate_csrf_token($submitted_csrf)) {
    send_json_response(false, 'Erro de segurança (CSRF). Tente novamente.');
}

$action = $_POST['action'] ?? '';

switch ($action) {
    case 'create_vat_rate':
        $rate = filter_var($_POST['rate'] ?? 0, FILTER_VALIDATE_FLOAT);
        $description = trim($_POST['description'] ?? '');
        $is_default = isset($_POST['is_default']) && $_POST['is_default'] == '1';

        
        if ($rate === false || $rate < 0 || $rate > 100) {
            send_json_response(false, 'Taxa de IVA inválida. Deve ser um número entre 0 e 100.');
        }

        if (empty($description)) {
            send_json_response(false, 'Descrição da taxa de IVA é obrigatória.');
        }

        
        $result = vat_create_rate($rate, $description, $is_default);

        
        if (is_array($result) && isset($result['success'])) {
            send_json_response(
                $result['success'],
                $result['message'] ?? ($result['success'] ? 'Taxa de IVA criada com sucesso.' : 'Erro ao criar taxa de IVA.'),
                isset($result['vat_rate']) ? ['vat_rate' => $result['vat_rate']] : []
            );
        } else {
            
            if ($result) {
                send_json_response(true, 'Taxa de IVA criada com sucesso.');
            } else {
                send_json_response(false, 'Erro ao criar taxa de IVA.');
            }
        }
        break;

    case 'update_vat_rate':
        $id = filter_var($_POST['id'] ?? 0, FILTER_VALIDATE_INT);
        $rate = filter_var($_POST['rate'] ?? 0, FILTER_VALIDATE_FLOAT);
        $description = trim($_POST['description'] ?? '');
        $is_default = isset($_POST['is_default']) && $_POST['is_default'] == '1';

        
        if ($id <= 0) {
            send_json_response(false, 'ID da taxa de IVA inválido.');
        }

        if ($rate === false || $rate < 0 || $rate > 100) {
            send_json_response(false, 'Taxa de IVA inválida. Deve ser um número entre 0 e 100.');
        }

        if (empty($description)) {
            send_json_response(false, 'Descrição da taxa de IVA é obrigatória.');
        }

        
        $result = vat_update_rate($id, $rate, $description, $is_default);

        
        if (is_array($result) && isset($result['success'])) {
            send_json_response(
                $result['success'],
                $result['message'] ?? ($result['success'] ? 'Taxa de IVA atualizada com sucesso.' : 'Erro ao atualizar taxa de IVA.'),
                isset($result['vat_rate']) ? ['vat_rate' => $result['vat_rate']] : []
            );
        } else {
            
            if ($result) {
                send_json_response(true, 'Taxa de IVA atualizada com sucesso.');
            } else {
                send_json_response(false, 'Erro ao atualizar taxa de IVA.');
            }
        }
        break;

    case 'set_default_vat_rate':
        $id = filter_var($_POST['id'] ?? 0, FILTER_VALIDATE_INT);

        
        if ($id <= 0) {
            send_json_response(false, 'ID da taxa de IVA inválido.');
        }

        
        $result = vat_set_default_rate($id);

        
        if (is_array($result) && isset($result['success'])) {
            send_json_response(
                $result['success'],
                $result['message'] ?? ($result['success'] ? 'Taxa de IVA definida como padrão com sucesso.' : 'Erro ao definir taxa de IVA como padrão.')
            );
        } else {
            
            if ($result) {
                send_json_response(true, 'Taxa de IVA definida como padrão com sucesso.');
            } else {
                send_json_response(false, 'Erro ao definir taxa de IVA como padrão.');
            }
        }
        break;

    case 'delete_vat_rate':
        $id = filter_var($_POST['id'] ?? 0, FILTER_VALIDATE_INT);

        
        if ($id <= 0) {
            send_json_response(false, 'ID da taxa de IVA inválido.');
        }

        
        $result = vat_delete_rate($id);

        
        if (is_array($result) && isset($result['success'])) {
            send_json_response(
                $result['success'],
                $result['message'] ?? ($result['success'] ? 'Taxa de IVA eliminada com sucesso.' : 'Erro ao eliminar taxa de IVA.')
            );
        } else {
            
            if ($result) {
                send_json_response(true, 'Taxa de IVA eliminada com sucesso.');
            } else {
                send_json_response(false, 'Erro ao eliminar taxa de IVA. Não é possível eliminar a taxa predefinida ou uma taxa que está a ser utilizada por produtos.');
            }
        }
        break;

    case 'get_vat_rates':
        
        $vat_rates = get_vat_rates();

        if ($vat_rates) {
            send_json_response(true, 'Taxas de IVA obtidas com sucesso.', ['vat_rates' => $vat_rates]);
        } else {
            send_json_response(false, 'Erro ao obter taxas de IVA.');
        }
        break;

    case 'generate_sitemap':
        $sitemap_id = filter_var($_POST['id'] ?? 0, FILTER_VALIDATE_INT);
        if ($sitemap_id <= 0) {
            send_json_response(false, 'ID do sitemap inválido.');
        }
        $result = generate_sitemap($sitemap_id); 
        send_json_response(
            $result['success'],
            $result['message'] ?? ($result['success'] ? 'Sitemap gerado com sucesso.' : 'Erro ao gerar sitemap.'),
            
            isset($result['data']) ? $result['data'] : []
        );
        break;

    case 'generate_all_sitemaps':
        $result = generate_all_active_sitemaps(); 
        send_json_response(
            $result['success'],
            $result['message'] ?? ($result['success'] ? 'Todos os sitemaps ativos foram gerados.' : 'Erro ao gerar todos os sitemaps ativos.'),
            
            isset($result['details']) ? ['details' => $result['details']] : []
        );
        break;

    case 'get_sitemap_details':
        $sitemap_id = filter_var($_POST['id'] ?? 0, FILTER_VALIDATE_INT);
        if ($sitemap_id <= 0) {
            send_json_response(false, 'ID do sitemap inválido para carregar detalhes.');
        }
        $sitemap_data = get_sitemap_config($sitemap_id); 
        if ($sitemap_data) {
            send_json_response(true, 'Dados do sitemap carregados.', ['sitemapData' => $sitemap_data]);
        } else {
            send_json_response(false, 'Sitemap não encontrado ou erro ao carregar dados.');
        }
        break;

    default:
        send_json_response(false, 'Ação desconhecida.');
        break;
}
