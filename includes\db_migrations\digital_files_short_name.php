<?php

require_once __DIR__ . '/../../config.php';
require_once __DIR__ . '/../db.php';

function add_short_name_to_digital_files(): bool
{
    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        
        $stmt = $pdo->query("PRAGMA table_info(digital_files)");
        $digital_files_columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);
        
        if (!in_array('short_name', $digital_files_columns)) {
            $pdo->exec("ALTER TABLE digital_files ADD COLUMN short_name TEXT");
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function check_and_add_short_name_column(PDO $pdo): void
{
    try {
        
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_files'");
        $table_exists = $stmt->fetch();

        if ($table_exists) {
            
            $stmt = $pdo->query("PRAGMA table_info(digital_files)");
            $digital_files_columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);
            
            if (!in_array('short_name', $digital_files_columns)) {
                $pdo->exec("ALTER TABLE digital_files ADD COLUMN short_name TEXT");
            }
        }
    } catch (PDOException $e) {
    }
}
