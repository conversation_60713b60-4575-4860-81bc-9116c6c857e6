<?php

$is_editing = ($action === 'edit' && isset($item_id));
$form_title = $is_editing ? "Editar Link" : "Criar Novo Link";
$link_data = null;

if ($is_editing) {
    
    $link_data = get_placeholder_link_by_id($item_id);
    if (!$link_data) {
        echo '<div class="alert alert-danger">Link não encontrado.</div>';
        return; 
    }
    $form_title .= " (#" . $link_data['id'] . ")";
}

$form_data = $_SESSION['form_data'] ?? null;
if ($form_data) {
    
    unset($_SESSION['form_data']);
} else if ($is_editing) {
    
    $form_data = $link_data;
}

$selected_placeholder_id = $selected_placeholder_id ?? null;
?>

<h1><?= $form_title ?></h1>
<hr>

<form method="POST" action="admin.php?section=placeholder_links&action=<?= $is_editing ? 'edit&id=' . $item_id : 'new' ?>&<?= get_session_id_param() ?>" class="ajax-form" id="placeholder-link-form" data-section="placeholder_links">
    <?= csrf_input_field() ?>
    <!-- Add hidden field for session ID to ensure it's included in the form data -->
    <input type="hidden" name="sid" value="<?= htmlspecialchars(get_current_session_id()) ?>">
    <!-- Add hidden field for action to ensure it's included in the form data -->
    <input type="hidden" name="action" value="<?= $is_editing ? 'edit' : 'new' ?>">
    <?php if ($is_editing): ?>
    <input type="hidden" name="id" value="<?= htmlspecialchars($item_id) ?>">
    <?php endif; ?>

    <div class="card mb-4">
        <div class="card-header">Detalhes do Link</div>
        <div class="card-body">
            <div class="mb-3">
                <label for="title" class="form-label">Título do Link *</label>
                <input type="text" class="form-control" id="title" name="title" value="<?= sanitize_input($form_data['title'] ?? '') ?>" required>
                <div class="form-text">Este título será exibido como texto do link no rodapé.</div>
            </div>
            <div class="mb-3">
                <label for="url" class="form-label">URL *</label>
                <input type="url" class="form-control" id="url" name="url" value="<?= sanitize_input($form_data['url'] ?? '') ?>" required>
                <div class="form-text">URL completo para onde o link irá direcionar (ex: https://www.example.com).</div>
            </div>
            <div class="mb-3">
                <label for="placeholder_id" class="form-label">Placeholder *</label>
                <select class="form-select" id="placeholder_id" name="placeholder_id" required>
                    <option value="">-- Selecione um Placeholder --</option>
                    <?php
                    $current_placeholder_id = $form_data['placeholder_id'] ?? $selected_placeholder_id ?? null;
                    if (!empty($placeholders)) {
                        foreach ($placeholders as $placeholder) {
                            $selected = ($placeholder['id'] == $current_placeholder_id) ? 'selected' : '';
                            echo '<option value="' . $placeholder['id'] . '" ' . $selected . '>' . htmlspecialchars($placeholder['name']) . '</option>';
                        }
                    }
                    ?>
                </select>
                <div class="form-text">Selecione o placeholder onde este link será exibido.</div>
            </div>
            <div class="mb-3">
                <label for="target" class="form-label">Destino do Link</label>
                <select class="form-select" id="target" name="target">
                    <option value="_blank" <?= (($form_data['target'] ?? '_blank') === '_blank') ? 'selected' : '' ?>>Nova Janela (_blank)</option>
                    <option value="_self" <?= (($form_data['target'] ?? '_blank') === '_self') ? 'selected' : '' ?>>Mesma Janela (_self)</option>
                </select>
                <div class="form-text">Escolha se o link abre em uma nova janela ou na mesma janela.</div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <button type="submit" class="btn btn-primary">Guardar Link</button>
        <a href="admin.php?section=placeholder_links&<?= get_session_id_param() ?>" class="btn btn-secondary">Cancelar</a>
    </div>
</form>
