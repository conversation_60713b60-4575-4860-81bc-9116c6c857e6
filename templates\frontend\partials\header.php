<?php

ob_start();

$header_categories_with_pages = get_header_categories_with_pages();

$header_pages = get_pages_for_location('header'); 
$blog_categories = get_blog_categories(true); 
$product_categories = getAllCategories(); 

?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title><?= sanitize_input($page_title ?? get_setting('store_name', 'Minha Loja')) ?></title>
    <meta name="description" content="<?= sanitize_input($page_description ?? get_setting('store_description', '')) ?>">

    <!-- SEO Meta Tags -->
    <?php if (isset($seo_keywords) && !empty($seo_keywords)): ?>
    <meta name="keywords" content="<?= sanitize_input($seo_keywords) ?>">
    <?php endif; ?>

    <!-- Open Graph / Facebook Meta Tags -->
    <?php if (isset($og_title)): ?><meta property="og:title" content="<?= sanitize_input($og_title) ?>"><?php endif; ?>
    <?php if (isset($og_description)): ?><meta property="og:description" content="<?= sanitize_input($og_description) ?>"><?php endif; ?>
    <?php if (isset($og_image)): ?><meta property="og:image" content="<?= sanitize_input($og_image) ?>"><?php endif; ?>
    <meta property="og:type" content="<?= sanitize_input($og_type ?? 'website') ?>">
    <meta property="og:url" content="<?= sanitize_input((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]") ?>">
    <meta property="og:site_name" content="<?= sanitize_input(get_setting('store_name', 'Minha Loja')) ?>">

    <!-- Twitter Card Meta Tags -->
    <?php if (isset($twitter_card)): ?><meta name="twitter:card" content="<?= sanitize_input($twitter_card) ?>"><?php endif; ?>
    <?php if (isset($twitter_title)): ?><meta name="twitter:title" content="<?= sanitize_input($twitter_title) ?>"><?php endif; ?>
    <?php if (isset($twitter_description)): ?><meta name="twitter:description" content="<?= sanitize_input($twitter_description) ?>"><?php endif; ?>
    <?php if (isset($twitter_image)): ?><meta name="twitter:image" content="<?= sanitize_input($twitter_image) ?>"><?php endif; ?>

    <!-- Sitemap References -->
    <?php
    
    $sitemaps = [];
    if (function_exists('get_sitemap_configs')) {
        $sitemaps = array_filter(get_sitemap_configs(true), function($config) {
            return $config['type'] === 'sitemap';
        });
    }

    
    if (!empty($sitemaps)) {
        $site_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://$_SERVER[HTTP_HOST]" . dirname($_SERVER['SCRIPT_NAME']);
        $site_url = rtrim($site_url, '/') . '/';

        foreach ($sitemaps as $sitemap) {
            $sitemap_path = $sitemap['output_path'];

            
            if (preg_match('/^[A-Z]:\\\\|^\//', $sitemap_path)) {
                
                $filename = basename($sitemap_path);
                $sitemap_url = $site_url . $filename;
            } else {
                
                $sitemap_url = $site_url . ltrim($sitemap_path, '/\\');
            }

            echo '<link rel="sitemap" type="application/xml" href="' . sanitize_input($sitemap_url) . '">' . PHP_EOL;
        }
    }
    ?>
    <!-- End SEO / Social Meta -->

    <!-- Tailwind CSS with Typography Plugin -->
    <script src="https://cdn.tailwindcss.com/3.4.16?plugins=typography"></script>
    <script>
        tailwind.config = {
            theme: {
                screens: {
                    'xs': '480px',
                    'sm': '640px',
                    'md': '768px',
                    'lg': '1024px',
                    'xl': '1280px',
                    '2xl': '1536px',
                },
                extend: {
                    colors: { primary: '#6366f1', secondary: '#4f46e5' },
                    borderRadius: { 'none': '0px', 'sm': '4px', DEFAULT: '8px', 'md': '12px', 'lg': '16px', 'xl': '20px', '2xl': '24px', '3xl': '32px', 'full': '9999px', 'button': '8px' },
                    typography: {
                        DEFAULT: {
                            css: {
                                color: '#e0e0e0',
                                a: { color: '#6366f1' },
                                strong: { color: '#ffffff' },
                                h1: { color: '#ffffff' },
                                h2: { color: '#ffffff' },
                                h3: { color: '#ffffff' },
                                h4: { color: '#ffffff' },
                                h5: { color: '#ffffff' },
                                h6: { color: '#ffffff' },
                                blockquote: { color: '#d1d5db' },
                                'ul > li::before': { backgroundColor: '#6366f1' },
                                'ol > li::before': { color: '#9ca3af' }
                            }
                        }
                    }
                }
            }
        }
    </script>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">

    <!-- Custom Styles from Template -->
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; } /* Ensure Remix Icons work */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #121212;
            color: #e0e0e0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        main {
             flex: 1; /* Push footer down */
        }
        .product-card:hover .quick-view { opacity: 1; }
        .product-card:hover img { transform: scale(1.05); }
        .product-image { transition: transform 0.3s ease; }
        .quick-view { opacity: 0; transition: opacity 0.3s ease; }
        .cart-preview { display: none; position: absolute; right: 0; top: 100%; width: 320px; z-index: 50; }
        input[type="number"]::-webkit-inner-spin-button,
        input[type="number"]::-webkit-outer-spin-button { -webkit-appearance: none; margin: 0; }
        .custom-scrollbar::-webkit-scrollbar { width: 6px; }
        .custom-scrollbar::-webkit-scrollbar-track { background: #1e1e1e; }
        .custom-scrollbar::-webkit-scrollbar-thumb { background: #3d3d3d; border-radius: 8px; }
        .lightbox { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.9); z-index: 100; justify-content: center; align-items: center; }
        .custom-checkbox { position: relative; display: inline-block; width: 18px; height: 18px; margin-right: 8px; }
        .custom-checkbox input { opacity: 0; width: 0; height: 0; }
        .checkmark { position: absolute; top: 0; left: 0; height: 18px; width: 18px; background-color: #2a2a2a; border-radius: 4px; transition: all 0.2s ease; }
        .custom-checkbox input:checked ~ .checkmark { background-color: #6366f1; }
        .checkmark:after { content: ""; position: absolute; display: none; }
        .custom-checkbox input:checked ~ .checkmark:after { display: block; }
        .custom-checkbox .checkmark:after { left: 6px; top: 3px; width: 5px; height: 10px; border: solid white; border-width: 0 2px 2px 0; transform: rotate(45deg); }
        .custom-radio { position: relative; display: inline-block; width: 18px; height: 18px; margin-right: 8px; }
        .custom-radio input { opacity: 0; width: 0; height: 0; }
        .radio-mark { position: absolute; top: 0; left: 0; height: 18px; width: 18px; background-color: #2a2a2a; border-radius: 50%; transition: all 0.2s ease; }
        .custom-radio input:checked ~ .radio-mark { background-color: #2a2a2a; border: 1px solid #6366f1; }
        .radio-mark:after { content: ""; position: absolute; display: none; }
        .custom-radio input:checked ~ .radio-mark:after { display: block; }
        .custom-radio .radio-mark:after { top: 4px; left: 4px; width: 8px; height: 8px; border-radius: 50%; background: #6366f1; }
        .switch { position: relative; display: inline-block; width: 40px; height: 22px; }
        .switch input { opacity: 0; width: 0; height: 0; }
        .slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #2a2a2a; transition: .4s; border-radius: 34px; }
        .slider:before { position: absolute; content: ""; height: 16px; width: 16px; left: 3px; bottom: 3px; background-color: white; transition: .4s; border-radius: 50%; }
        input:checked + .slider { background-color: #6366f1; }
        input:checked + .slider:before { transform: translateX(18px); }
        .toast { position: fixed; bottom: 24px; right: 24px; z-index: 100; transform: translateY(100px); opacity: 0; transition: all 0.3s ease; }
        .toast.show { transform: translateY(0); opacity: 1; }
        /* Cart count badge styling */
        .cart-count-badge {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-size: 0.65rem !important;
            line-height: 0 !important;
            padding: 0 !important;
            font-weight: bold !important;
        }
        /* Add specific styles for quantity buttons if needed from checkout.html */
        .quantity-input { width: 40px; text-align: center; border: none; background: transparent; color: #e0e0e0; }
        .quantity-btn { width: 28px; height: 28px; display: flex; align-items: center; justify-content: center; background-color: #2a2a2a; border-radius: 4px; color: #e0e0e0; cursor: pointer; transition: all 0.2s ease; }
        .quantity-btn:hover { background-color: #3d3d3d; }

        /* Enhanced styling for product descriptions */
        .prose ul { list-style-type: disc; padding-left: 1.5rem; margin-top: 0.75rem; margin-bottom: 0.75rem; }
        .prose ol { list-style-type: decimal; padding-left: 1.5rem; margin-top: 0.75rem; margin-bottom: 0.75rem; }
        .prose li { margin-top: 0.25rem; margin-bottom: 0.25rem; }
        .prose li::marker { color: #6366f1; }
        .prose p { margin-top: 0.75rem; margin-bottom: 0.75rem; }
        .prose h1, .prose h2, .prose h3, .prose h4 { color: #ffffff; margin-top: 1.5rem; margin-bottom: 0.75rem; }
        .prose table { border-collapse: collapse; width: 100%; margin-top: 1rem; margin-bottom: 1rem; }
        .prose table th, .prose table td { border: 1px solid #374151; padding: 0.5rem; }
        .prose table th { background-color: #1f2937; }
    </style>

    <!-- Pass Session ID, Base URL, and Store Logo to JavaScript -->
    <script>
        window.eshopSessionId = '<?= sanitize_input($current_session_id ?? '') ?>';
        window.eshopSessionParam = '<?= sanitize_input(SESSION_PARAM_NAME ?? 'sid') ?>';
        window.eshopBaseUrl = '<?= sanitize_input(BASE_URL ?? '') ?>';

        <?php
        
        $logo_path = get_setting('store_logo_path');
        $logo_full_path = !empty($logo_path) ? PROJECT_ROOT . '/' . $logo_path : null;
        if ($logo_full_path && file_exists($logo_full_path)) {
            $logo_url = BASE_URL . '/' . $logo_path . '?' . filemtime($logo_full_path); 
            echo 'window.storeLogo = "' . sanitize_input($logo_url) . '";';
        }
        ?>
    </script>

</head>
<body class="bg-gray-900 text-gray-200"> <!-- Adjusted body classes slightly -->

<!-- Header -->
<header class="sticky top-0 z-50 bg-[#121212] border-b border-gray-800">
    <div class="container mx-auto px-4 py-3 flex items-center justify-between">
        <a href="<?= add_session_param_to_url(BASE_URL . '/index.php') ?>" class="flex items-center text-2xl font-['Pacifico'] text-white">
            <?php
            $logo_path = get_setting('store_logo_path');
            $logo_full_path = !empty($logo_path) ? PROJECT_ROOT . '/' . $logo_path : null;
            if ($logo_full_path && file_exists($logo_full_path)) {
                $logo_url = BASE_URL . '/' . $logo_path . '?' . filemtime($logo_full_path); 
                echo '<img src="' . sanitize_input($logo_url) . '" alt="' . sanitize_input(get_setting('store_name')) . ' Logo" class="inline-block h-8 w-auto mr-2">'; 
            }
            ?>
            <span><?= sanitize_input(get_setting('store_name', 'Minha Loja')) ?></span>
        </a>
        <nav class="hidden md:flex items-center space-x-6">

            <?php
            
            if (!empty($header_categories_with_pages)) {
                
                foreach ($header_categories_with_pages as $category_id => $category_data) {
                    
                    if (!empty($category_data['pages'])) {
            ?>
                        <div class="relative group">
                            <button class="text-gray-300 hover:text-white transition inline-flex items-center">
                                <?= htmlspecialchars($category_data['name']) ?>
                                <i class="ri-arrow-down-s-line ml-1 text-xs"></i>
                            </button>
                            <div class="absolute left-0 w-48 bg-gray-800 rounded-md shadow-lg z-50 hidden group-hover:block">
                                <div class="py-1">
                                    <?php foreach ($category_data['pages'] as $page) :
                                        
                                        $is_current_page = (isset($_GET['page']) && $_GET['page'] === $page['slug']);
                                        $link_class = $is_current_page ? 'bg-gray-700 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white';
                                    ?>
                                        <a href="<?= get_page_url($page['slug']) ?>" class="block px-4 py-2 text-sm <?= $link_class ?>">
                                            <?= htmlspecialchars($page['title']) ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
            <?php
                    } 
                } 
            } 

            
            if (!empty($header_pages)) {
                foreach ($header_pages as $page) {
                    $is_current_page = (isset($_GET['page']) && $_GET['page'] === $page['slug']);
                    $link_class = $is_current_page ? 'text-white font-semibold transition' : 'text-gray-300 hover:text-white transition';
                    echo '<a href="' . get_page_url($page['slug']) . '" class="' . $link_class . '">' . htmlspecialchars($page['title']) . '</a>';
                }
            }
            ?>
 
            <!-- Product Categories Dropdown -->
            <?php if (!empty($product_categories)): ?>
            <div class="relative group">
                <button class="text-gray-300 hover:text-white transition inline-flex items-center">
                    Produtos
                    <i class="ri-arrow-down-s-line ml-1 text-xs"></i>
                </button>
                <div class="absolute left-0 w-48 bg-gray-800 rounded-md shadow-lg z-50 hidden group-hover:block">
                    <div class="py-1">
                        <a href="<?= add_session_param_to_url(BASE_URL . '/index.php') ?>" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">Todos os Produtos</a>
                        <?php foreach ($product_categories as $p_category) :
                            $p_category_url = add_session_param_to_url(BASE_URL . '/index.php?category_id=' . $p_category['id']);
                            $is_current_p_category = (isset($_GET['category_id']) && (int)$_GET['category_id'] === (int)$p_category['id']);
                            $p_link_class = $is_current_p_category ? 'bg-gray-700 text-white' : 'text-gray-300 hover:bg-gray-700 hover:text-white';
                        ?>
                            <a href="<?= $p_category_url ?>" class="block px-4 py-2 text-sm <?= $p_link_class ?>">
                                <?= htmlspecialchars($p_category['name']) ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            <!-- End Product Categories Dropdown -->

            <!-- Blog Dropdown -->
            <?php if (!empty($blog_categories)): ?>
            <div class="relative group">
                <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=blog') ?>" class="text-gray-300 hover:text-white transition inline-flex items-center">
                    Blog
                    <i class="ri-arrow-down-s-line ml-1 text-xs"></i>
                </a>
                <div class="absolute left-0 w-48 bg-gray-800 rounded-md shadow-lg z-50 hidden group-hover:block">
                    <div class="py-1">
                         <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=blog') ?>" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">Todos os Posts</a>
                        <?php foreach ($blog_categories as $blog_cat) :
                            $blog_cat_url = add_session_param_to_url(BASE_URL . '/index.php?view=blog&category=' . $blog_cat['slug']);
                        ?>
                            <a href="<?= $blog_cat_url ?>" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">
                                <?= htmlspecialchars($blog_cat['name']) ?>
                            </a>
                        <?php endforeach; ?>
                         <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=blog_search') ?>" class="block px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 hover:text-white">Pesquisa</a>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            <!-- End Blog Dropdown -->

            <!-- Downloads Link -->
            <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download') ?>" class="text-gray-300 hover:text-white transition">
                Licenças & Downloads
            </a>
        </nav>
        <div class="flex items-center space-x-4">
            <!-- Search Form -->
            <form action="<?= add_session_param_to_url(BASE_URL . '/index.php') ?>" method="GET" class="relative">
                <input type="hidden" name="page" value="search"> <!-- Hidden input to identify search action -->
                <input type="text" name="q" placeholder="Pesquisar..." value="<?= isset($_GET['q']) ? htmlspecialchars($_GET['q']) : '' ?>" class="py-2 pl-10 pr-4 w-40 md:w-64 bg-gray-800 border-none rounded-full text-sm text-gray-300 focus:outline-none focus:ring-1 focus:ring-primary">
                <button type="submit" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center text-gray-400 hover:text-primary transition">
                    <i class="ri-search-line"></i>
                </button>
            </form>
            <!-- Cart Icon -->
            <div class="relative cart-container" id="cartContainer">
                 <!-- Link to Cart Page -->
                <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=cart') ?>" class="relative block p-2 bg-gray-800 rounded-full" id="cartLink">
                    <div class="w-5 h-5 flex items-center justify-center">
                        <i class="ri-shopping-cart-line text-gray-300"></i>
                    </div>
                     <!-- Cart item count badge -->
                    <span id="cart-item-count" class="cart-count-badge absolute -top-2 -right-2 bg-primary text-white text-xs w-5 h-5 flex items-center justify-center rounded-full" style="display: none;">0</span>
                </a>
                <!-- Cart Preview Dropdown (Static structure, needs JS for dynamic content) -->
                <div class="cart-preview bg-gray-900 border border-gray-800 rounded-lg shadow-xl p-4" id="cartPreview">
                    <h3 class="font-medium mb-3">Seu Carrinho (<span id="cart-preview-count">0</span>)</h3>
                    <div class="max-h-60 overflow-y-auto custom-scrollbar" id="cart-preview-items">
                        <!-- Cart items will be loaded here by JavaScript -->
                        <p class="text-sm text-gray-400 text-center py-4">Seu carrinho está vazio.</p>
                    </div>
                    <div class="mt-3 pt-3 border-t border-gray-800">
                        <div class="flex justify-between mb-2">
                            <span class="text-sm text-gray-400">Subtotal</span>
                            <span class="text-sm font-medium" id="cart-preview-subtotal">$0.00</span>
                        </div>
                        <div class="flex space-x-2">
                            <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=cart') ?>" class="flex-1 py-2 px-4 bg-gray-800 text-white text-center text-sm rounded-button whitespace-nowrap">Ver Carrinho</a>
                            <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=checkout') ?>" class="flex-1 py-2 px-4 bg-primary text-white text-center text-sm rounded-button whitespace-nowrap">Checkout</a>
                        </div>
                    </div>
                </div>
            </div>
            <button class="md:hidden p-2 bg-gray-800 rounded-full" id="mobileMenuToggle">
                <div class="w-5 h-5 flex items-center justify-center">
                    <i class="ri-menu-line text-gray-300"></i>
                </div>
            </button>
        </div>
    </div>
    <!-- Mobile Menu (Hidden by default, needs JS) -->
    <div class="md:hidden hidden bg-[#121212] border-t border-gray-800" id="mobileMenu">
         <a href="<?= add_session_param_to_url(BASE_URL . '/index.php') ?>" class="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800">Início</a>

         <?php
         
         if (!empty($header_categories_with_pages)) {
             foreach ($header_categories_with_pages as $category_id => $category_data) {
                  if (!empty($category_data['pages'])) {
         ?>
                     <div class="px-4 py-2 text-gray-500 font-semibold"><?= htmlspecialchars($category_data['name']) ?></div>
                     <?php foreach ($category_data['pages'] as $page) :
                         $is_current_page = (isset($_GET['page']) && $_GET['page'] === $page['slug']);
                         $link_class = $is_current_page ? 'text-white font-semibold bg-gray-800' : 'text-gray-300 hover:text-white hover:bg-gray-800';
                     ?>
                         <a href="<?= get_page_url($page['slug']) ?>" class="block pl-8 pr-4 py-2 <?= $link_class ?>">
                             <?= htmlspecialchars($page['title']) ?>
                         </a>
                     <?php endforeach; ?>
         <?php
                  } 
             } 
         } 

         
         if (!empty($header_pages)) {
             foreach ($header_pages as $page) {
                 $is_current_page = (isset($_GET['page']) && $_GET['page'] === $page['slug']);
                 $link_class = $is_current_page ? 'text-white font-semibold bg-gray-800' : 'text-gray-300 hover:text-white hover:bg-gray-800';
                 echo '<a href="' . get_page_url($page['slug']) . '" class="block px-4 py-2 ' . $link_class . '">' . htmlspecialchars($page['title']) . '</a>';
             }
         }
         ?>

         <!-- Mobile: Product Categories -->
         <?php if (!empty($product_categories)): ?>
             <div class="px-4 py-2 text-gray-500 font-semibold">Produtos</div>
             <a href="<?= add_session_param_to_url(BASE_URL . '/index.php') ?>" class="block pl-8 pr-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800">Todos os Produtos</a>
             <?php foreach ($product_categories as $p_category) :
                 $p_category_url = add_session_param_to_url(BASE_URL . '/index.php?category_id=' . $p_category['id']);
                 $is_current_p_category = (isset($_GET['category_id']) && (int)$_GET['category_id'] === (int)$p_category['id']);
                 $p_link_class = $is_current_p_category ? 'text-white font-semibold bg-gray-800' : 'text-gray-300 hover:text-white hover:bg-gray-800';
             ?>
                 <a href="<?= $p_category_url ?>" class="block pl-8 pr-4 py-2 <?= $p_link_class ?>">
                     <?= htmlspecialchars($p_category['name']) ?>
                 </a>
             <?php endforeach; ?>
         <?php endif; ?>
         <!-- End Mobile: Product Categories -->

         <!-- Mobile: Blog Links -->
         <?php if (!empty($blog_categories)): ?>
             <div class="px-4 py-2 text-gray-500 font-semibold">Blog</div>
             <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=blog') ?>" class="block pl-8 pr-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800">Todos os Posts</a>
             <?php foreach ($blog_categories as $blog_cat) :
                 $blog_cat_url = add_session_param_to_url(BASE_URL . '/index.php?view=blog&category=' . $blog_cat['slug']);
             ?>
                 <a href="<?= $blog_cat_url ?>" class="block pl-8 pr-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800">
                     <?= htmlspecialchars($blog_cat['name']) ?>
                 </a>
             <?php endforeach; ?>
             <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=blog_search') ?>" class="block pl-8 pr-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800">Pesquisa</a>
         <?php endif; ?>
         <!-- End Mobile Blog Links -->

         <!-- Mobile: Downloads Link -->
         <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download') ?>" class="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800">Downloads</a>

         <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?page=contact') ?>" class="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800">Contacto</a>
    </div>
</header>

<!-- Start of main content area (closed in footer.php) -->
<main class="container mx-auto px-4 py-8">
