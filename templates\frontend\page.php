<?php

if (!isset($page_content) || !$page_content) {
    echo "<h1 class='text-2xl font-semibold mb-4'>Erro</h1><p class='text-gray-400'>Página não encontrada ou não disponível.</p>";
    return;
}

$page_title = sanitize_input($page_content['title_pt']) . " - " . sanitize_input(get_setting('store_name', 'Minha Loja'));

?>

<div class="bg-gray-900 rounded-lg p-6 md:p-8">
    <?php if (isset($page_content['show_title']) && $page_content['show_title'] == 1): ?>
    <h1 class="text-3xl font-semibold mb-6 border-b border-gray-700 pb-3"><?= sanitize_input($page_content['title_pt']) ?></h1>
    <?php endif; ?>

    <div class="page-content prose prose-invert max-w-none prose-p:text-gray-300 prose-headings:text-gray-100 prose-a:text-primary hover:prose-a:text-indigo-400 prose-strong:text-gray-200">
        <?php
        
        
        echo $page_content['content_pt'];
       ?>
   </div>

   <!-- Keyword Cloud Section for Pages -->
   <?php
   
   if (!function_exists('get_all_page_seo_keywords')) {
       require_once __DIR__ . '/../../includes/product_functions.php';
   }

   $all_page_db_keywords = get_all_page_seo_keywords();
   $max_page_cloud_keywords = (int)get_setting('page_keyword_cloud_max_keywords', 15); 
   if ($max_page_cloud_keywords <= 0) $max_page_cloud_keywords = 15; 

   $display_page_keywords = [];

   if (!empty($all_page_db_keywords)) {
       if (count($all_page_db_keywords) > $max_page_cloud_keywords) {
           shuffle($all_page_db_keywords);
           $display_page_keywords = array_slice($all_page_db_keywords, 0, $max_page_cloud_keywords);
       } else {
           $display_page_keywords = $all_page_db_keywords;
       }
   }

   if (!empty($display_page_keywords)):
   ?>
   <div class="mt-8 pt-6 border-t border-gray-700">
       <h3 class="text-xl font-semibold text-gray-200 mb-4">Outros temas que pode ter interesse:</h3>
       <div id="page-keyword-cloud-canvas-container" class="w-full h-64 md:h-80 relative bg-gray-800/30 rounded-lg">
           <!-- Canvas for Three.js will be inserted here by JavaScript -->
       </div>
       <div id="page-keyword-cloud-fallback" class="flex flex-wrap gap-2 mt-4" style="display: none;">
           <?php foreach ($display_page_keywords as $keyword_item): ?>
               <a href="index.php?page=<?= urlencode(sanitize_input($keyword_item['slug'])) ?>&<?= get_session_id_param() ?>"
                  class="inline-block bg-gray-700 hover:bg-primary text-gray-300 hover:text-white text-xs font-medium px-3 py-1 rounded-full transition-colors"
                  title="Ir para a página sobre <?= sanitize_input($keyword_item['keyword']) ?>">
                   <?= sanitize_input($keyword_item['keyword']) ?>
               </a>
           <?php endforeach; ?>
       </div>
   </div>

   <!-- Three.js and Keyword Cloud Script for Pages -->
   <script type="importmap">
   {
       "imports": {
           "three": "https://unpkg.com/three@0.160.0/build/three.module.js",
           "three/addons/": "https://unpkg.com/three@0.160.0/examples/jsm/"
       }
   }
   </script>
   <script type="module">
       import * as THREE from 'three';

       const pageContainer = document.getElementById('page-keyword-cloud-canvas-container');
       const pageFallbackContainer = document.getElementById('page-keyword-cloud-fallback');
       <?php
       $js_page_keywords_array = array_map(function($item) { return $item['keyword']; }, $display_page_keywords);
       ?>
       const pageKeywords = <?= json_encode($js_page_keywords_array ?? [], JSON_UNESCAPED_UNICODE | JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP) ?>;

       if (pageContainer && pageKeywords && pageKeywords.length > 0) {
           let scene, camera, renderer;
           const texts = [];

           function initThreeJSPageCloud() {
               try {
                   scene = new THREE.Scene();
                   
                   const fov = 75;
                   const aspect = pageContainer.clientWidth / pageContainer.clientHeight;
                   const near = 0.1;
                   const far = 1000;
                   camera = new THREE.PerspectiveCamera(fov, aspect, near, far);
                   camera.position.z = 80; // Adjusted for potentially smaller container or fewer words

                   renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
                   renderer.setSize(pageContainer.clientWidth, pageContainer.clientHeight);
                   renderer.setPixelRatio(window.devicePixelRatio);
                   pageContainer.appendChild(renderer.domElement);

                   const loader = new THREE.FontLoader();
                   loader.load('https://unpkg.com/three@0.160.0/examples/fonts/helvetiker_regular.typeface.json', function (font) {
                       const material = new THREE.MeshBasicMaterial({
                           color: 0x9ca3af, // Tailwind gray-400
                           transparent: true,
                           opacity: 0.8,
                           side: THREE.DoubleSide
                       });

                       pageKeywords.forEach((text, index) => {
                           const shapes = font.generateShapes(text, 4); // Slightly smaller text size
                           const geometry = new THREE.ShapeGeometry(shapes);
                           geometry.computeBoundingBox();
                           const xMid = -0.5 * (geometry.boundingBox.max.x - geometry.boundingBox.min.x);
                           geometry.translate(xMid, 0, 0);
                           
                           const textMesh = new THREE.Mesh(geometry, material.clone());
                           const phi = Math.acos(-1 + (2 * index) / pageKeywords.length);
                           const theta = Math.sqrt(pageKeywords.length * Math.PI) * phi;
                           const radius = 50; // Smaller radius for page cloud
                           textMesh.position.setFromSphericalCoords(radius, phi, theta);
                           textMesh.lookAt(camera.position);
                           scene.add(textMesh);
                           texts.push(textMesh);
                       });

                       if (texts.length > 0) {
                           animatePageCloud();
                       } else {
                           console.warn("No text meshes generated for the page keyword cloud.");
                           if (pageFallbackContainer) pageFallbackContainer.style.display = 'flex';
                           if (pageContainer) pageContainer.style.display = 'none';
                       }
                   },
                   undefined,
                   function ( error ) {
                       console.error( 'An error happened during page font loading: ', error );
                       if (pageFallbackContainer) pageFallbackContainer.style.display = 'flex';
                       if (pageContainer) pageContainer.style.display = 'none';
                   });
                   
                   window.addEventListener('resize', onWindowResizePageCloud, false);

               } catch (error) {
                   console.error("Error initializing Three.js for page keyword cloud:", error);
                   if (pageContainer) pageContainer.style.display = 'none';
                   if (pageFallbackContainer) pageFallbackContainer.style.display = 'flex';
               }
           }

           function onWindowResizePageCloud() {
               if (camera && renderer && pageContainer) {
                   camera.aspect = pageContainer.clientWidth / pageContainer.clientHeight;
                   camera.updateProjectionMatrix();
                   renderer.setSize(pageContainer.clientWidth, pageContainer.clientHeight);
               }
           }

           function animatePageCloud() {
               requestAnimationFrame(animatePageCloud);
               texts.forEach(text => {
                   text.rotation.y += 0.0025; // Slightly faster rotation maybe
                   text.lookAt(camera.position);
               });
               scene.rotation.x += 0.0006;
               scene.rotation.y += 0.0012;
               if (renderer && scene && camera) {
                   renderer.render(scene, camera);
               }
           }

           initThreeJSPageCloud();

       } else {
           if (pageFallbackContainer) pageFallbackContainer.style.display = 'flex';
           console.warn('Page keyword cloud container or keywords not found, showing fallback.');
       }
   </script>
   <?php endif; ?>
   <!-- End Keyword Cloud Section for Pages -->

</div>
