<?php

$status = $_SESSION['contact_form_status'] ?? 'unknown';
$message = $_SESSION['contact_form_message'] ?? '';

unset($_SESSION['contact_form_status']);
unset($_SESSION['contact_form_message']);

$page_title = ($status === 'success') ? "Mensagem Enviada" : "Erro no Envio";

$redirect_delay = 5;
$redirect_url = BASE_URL . '/index.php?' . get_session_id_param();
?>

<div class="max-w-3xl mx-auto text-center mb-12">
    <h1 class="text-3xl font-semibold mb-4"><?= $page_title ?></h1>
    
    <?php if ($status === 'success'): ?>
        <div class="bg-green-800 border border-green-700 text-green-100 px-6 py-4 rounded-lg mb-6">
            <div class="flex items-center">
                <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-lg font-medium">Mensagem enviada com sucesso!</span>
            </div>
            <p class="mt-2"><?= !empty($message) ? htmlspecialchars($message) : 'Obrigado pelo seu contacto. Responderemos o mais brevemente possível.' ?></p>
        </div>
    <?php else: ?>
        <div class="bg-red-800 border border-red-700 text-red-100 px-6 py-4 rounded-lg mb-6">
            <div class="flex items-center">
                <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-lg font-medium">Ocorreu um erro!</span>
            </div>
            <p class="mt-2"><?= !empty($message) ? htmlspecialchars($message) : 'Não foi possível enviar a sua mensagem. Por favor, tente novamente mais tarde.' ?></p>
        </div>
    <?php endif; ?>
    
    <p class="text-gray-400">Será redirecionado para a página inicial em <span id="countdown"><?= $redirect_delay ?></span> segundos...</p>
    <div class="mt-4">
        <a href="<?= $redirect_url ?>" class="inline-block bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-button font-medium transition">
            Voltar à Página Inicial
        </a>
    </div>
</div>

<script>
    // Countdown timer for redirect
    let seconds = <?= $redirect_delay ?>;
    const countdownElement = document.getElementById('countdown');
    
    const countdownInterval = setInterval(() => {
        seconds--;
        if (countdownElement) {
            countdownElement.textContent = seconds;
        }
        
        if (seconds <= 0) {
            clearInterval(countdownInterval);
            window.location.href = '<?= $redirect_url ?>';
        }
    }, 1000);
</script>
