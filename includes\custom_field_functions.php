<?php

function get_custom_field_types(bool $active_only = true): array
{
    return db_query(
        "SELECT * FROM custom_field_types ORDER BY name",
        [],
        false, true
    );
}

function get_custom_field_type(int $type_id): array|false
{
    return db_query(
        "SELECT * FROM custom_field_types WHERE id = :id",
        [':id' => $type_id],
        true
    );
}

function get_custom_fields(bool $active_only = true): array
{
    $sql = "SELECT cf.*, cft.name as type_name, cft.slug as type_slug
            FROM custom_fields cf
            JOIN custom_field_types cft ON cf.field_type_id = cft.id";

    if ($active_only) {
        $sql .= " WHERE cf.is_active = 1";
    }

    $sql .= " ORDER BY cf.name";

    return db_query($sql, [], false, true);
}

function get_custom_field(int $field_id): array|false
{
    return db_query(
        "SELECT cf.*, cft.name as type_name, cft.slug as type_slug
         FROM custom_fields cf
         JOIN custom_field_types cft ON cf.field_type_id = cft.id
         WHERE cf.id = :id",
        [':id' => $field_id],
        true
    );
}

function create_custom_field(array $field_data): int|false
{
    
    if (empty($field_data['name']) || empty($field_data['field_type_id'])) {
        return false;
    }

    
    $config_json = null;
    if (isset($field_data['config']) && is_array($field_data['config'])) {
        $config_json = json_encode($field_data['config']);
    }

    $sql = "INSERT INTO custom_fields (
                name, description, field_type_id, min_chars, max_chars,
                price_modifier, is_required, is_active, config_json,
                created_at, updated_at
            ) VALUES (
                :name, :description, :field_type_id, :min_chars, :max_chars,
                :price_modifier, :is_required, :is_active, :config_json,
                datetime('now', 'localtime'), datetime('now', 'localtime')
            )";

    $params = [
        ':name' => $field_data['name'],
        ':description' => $field_data['description'] ?? null,
        ':field_type_id' => $field_data['field_type_id'],
        ':min_chars' => $field_data['min_chars'] ?? 0,
        ':max_chars' => $field_data['max_chars'] ?? 255,
        ':price_modifier' => $field_data['price_modifier'] ?? 0.0,
        ':is_required' => $field_data['is_required'] ?? 0,
        ':is_active' => $field_data['is_active'] ?? 1,
        ':config_json' => $config_json
    ];

    $result = db_query($sql, $params);

    if ($result) {
        $pdo = get_db_connection();
        return $pdo->lastInsertId();
    }

    return false;
}

function update_custom_field(int $field_id, array $field_data): bool
{
    
    if (empty($field_data['name']) || empty($field_data['field_type_id'])) {
        return false;
    }

    
    $config_json = null;
    if (isset($field_data['config']) && is_array($field_data['config'])) {
        $config_json = json_encode($field_data['config']);
    }

    $sql = "UPDATE custom_fields SET
                name = :name,
                description = :description,
                field_type_id = :field_type_id,
                min_chars = :min_chars,
                max_chars = :max_chars,
                price_modifier = :price_modifier,
                is_required = :is_required,
                is_active = :is_active,
                config_json = :config_json,
                updated_at = datetime('now', 'localtime')
            WHERE id = :id";

    $params = [
        ':id' => $field_id,
        ':name' => $field_data['name'],
        ':description' => $field_data['description'] ?? null,
        ':field_type_id' => $field_data['field_type_id'],
        ':min_chars' => $field_data['min_chars'] ?? 0,
        ':max_chars' => $field_data['max_chars'] ?? 255,
        ':price_modifier' => $field_data['price_modifier'] ?? 0.0,
        ':is_required' => $field_data['is_required'] ?? 0,
        ':is_active' => $field_data['is_active'] ?? 1,
        ':config_json' => $config_json
    ];

    $result = db_query($sql, $params);

    return $result !== false;
}

function delete_custom_field(int $field_id): bool
{
    if ($field_id <= 0) {
        return false;
    }

    try {
        $pdo = get_db_connection();

        
        $pdo->beginTransaction();

        
        $check_sql = "SELECT id FROM custom_fields WHERE id = :id";
        $check_stmt = $pdo->prepare($check_sql);
        $check_stmt->execute([':id' => $field_id]);

        if (!$check_stmt->fetch()) {
            $pdo->rollBack();
            return false;
        }

        
        $delete_assoc_sql = "DELETE FROM product_custom_fields WHERE custom_field_id = :id";
        $delete_assoc_stmt = $pdo->prepare($delete_assoc_sql);
        $assoc_deleted = $delete_assoc_stmt->execute([':id' => $field_id]);

        if (!$assoc_deleted) {
            $pdo->rollBack();
            return false;
        }

        
        $delete_order_assoc_sql = "DELETE FROM order_item_custom_fields WHERE custom_field_id = :id";
        $delete_order_assoc_stmt = $pdo->prepare($delete_order_assoc_sql);
        $order_assoc_deleted = $delete_order_assoc_stmt->execute([':id' => $field_id]);

        if (!$order_assoc_deleted) {
            $pdo->rollBack();
            return false;
        }

        
        $delete_sql = "DELETE FROM custom_fields WHERE id = :id";
        $delete_stmt = $pdo->prepare($delete_sql);
        $result = $delete_stmt->execute([':id' => $field_id]);
        $rows_affected = $delete_stmt->rowCount(); 

        if (!$result || $rows_affected === 0) {
            $pdo->rollBack();
            return false;
        }

        
        $pdo->commit();
        return true;

    } catch (PDOException $e) {
        
        if (isset($pdo) && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function get_custom_field_fonts(bool $active_only = true): array
{
    $sql = "SELECT * FROM custom_field_fonts";

    if ($active_only) {
        $sql .= " WHERE is_active = 1";
    }

    $sql .= " ORDER BY name";

    return db_query($sql, [], false, true);
}

function get_custom_field_font(int $font_id): array|false
{
    return db_query(
        "SELECT * FROM custom_field_fonts WHERE id = :id",
        [':id' => $font_id],
        true
    );
}

function create_custom_field_font(array $font_data): int|false
{
    
    if (empty($font_data['name'])) {
        return false;
    }

    $sql = "INSERT INTO custom_field_fonts (
                name, file_path, google_font_name, is_active,
                created_at, updated_at
            ) VALUES (
                :name, :file_path, :google_font_name, :is_active,
                datetime('now', 'localtime'), datetime('now', 'localtime')
            )";

    $params = [
        ':name' => $font_data['name'],
        ':file_path' => $font_data['file_path'] ?? null,
        ':google_font_name' => $font_data['google_font_name'] ?? null,
        ':is_active' => $font_data['is_active'] ?? 1
    ];

    $result = db_query($sql, $params);

    if ($result) {
        $pdo = get_db_connection();
        return $pdo->lastInsertId();
    }

    return false;
}

function update_custom_field_font(int $font_id, array $font_data): bool
{
    
    if (empty($font_data['name'])) {
        return false;
    }

    $sql = "UPDATE custom_field_fonts SET
                name = :name,
                file_path = :file_path,
                google_font_name = :google_font_name,
                is_active = :is_active,
                updated_at = datetime('now', 'localtime')
            WHERE id = :id";

    $params = [
        ':id' => $font_id,
        ':name' => $font_data['name'],
        ':file_path' => $font_data['file_path'] ?? null,
        ':google_font_name' => $font_data['google_font_name'] ?? null,
        ':is_active' => $font_data['is_active'] ?? 1
    ];

    $result = db_query($sql, $params);

    return $result !== false;
}

function delete_custom_field_font(int $font_id): bool
{
    return db_query(
        "DELETE FROM custom_field_fonts WHERE id = :id",
        [':id' => $font_id]
    ) !== false;
}

function duplicate_custom_field(int $field_id): int|false
{
    
    $original_field = get_custom_field($field_id);

    if (!$original_field) {
        return false;
    }

    
    $new_field_data = [
        'name' => $original_field['name'] . ' (Cópia)',
        'description' => $original_field['description'],
        'field_type_id' => $original_field['field_type_id'],
        'min_chars' => $original_field['min_chars'],
        'max_chars' => $original_field['max_chars'],
        'price_modifier' => $original_field['price_modifier'],
        'is_required' => $original_field['is_required'],
        'is_active' => $original_field['is_active']
    ];

    
    if (!empty($original_field['config_json'])) {
        $config = json_decode($original_field['config_json'], true);
        if (is_array($config)) {
            $new_field_data['config'] = $config;
        }
    }

    
    return create_custom_field($new_field_data);
}

function get_product_custom_fields(int $product_id): array
{
    return db_query(
        "SELECT pcf.*, cf.name, cf.description, cf.field_type_id, cf.min_chars, cf.max_chars,
                cf.price_modifier, cf.is_required, cf.config_json,
                cft.name as type_name, cft.slug as type_slug
         FROM product_custom_fields pcf
         JOIN custom_fields cf ON pcf.custom_field_id = cf.id
         JOIN custom_field_types cft ON cf.field_type_id = cft.id
         WHERE pcf.product_id = :pid AND cf.is_active = 1
         ORDER BY pcf.sort_order ASC",
        [':pid' => $product_id],
        false, true
    );
}

function add_product_custom_field(int $product_id, int $field_id, ?float $price_modifier_override = null, ?int $sort_order = null): int|false
{
    
    $existing = db_query(
        "SELECT id FROM product_custom_fields WHERE product_id = :pid AND custom_field_id = :fid",
        [':pid' => $product_id, ':fid' => $field_id],
        true
    );

    if ($existing) {
        return $existing['id'];
    }

    
    if ($sort_order === null) {
        $max_sort = db_query(
            "SELECT IFNULL(MAX(sort_order), -1) as max_sort FROM product_custom_fields WHERE product_id = :pid",
            [':pid' => $product_id],
            true
        );
        $sort_order = ($max_sort && isset($max_sort['max_sort'])) ? (int)$max_sort['max_sort'] + 1 : 0;
    }

    $sql = "INSERT INTO product_custom_fields (
                product_id, custom_field_id, sort_order, price_modifier_override, created_at
            ) VALUES (
                :pid, :fid, :sort, :price_override, datetime('now', 'localtime')
            )";

    $params = [
        ':pid' => $product_id,
        ':fid' => $field_id,
        ':sort' => $sort_order,
        ':price_override' => $price_modifier_override
    ];

    $result = db_query($sql, $params);

    if ($result) {
        $pdo = get_db_connection();
        return $pdo->lastInsertId();
    }

    return false;
}

function update_product_custom_field(int $association_id, ?float $price_modifier_override = null, ?int $sort_order = null): bool
{
    $updates = [];
    $params = [':id' => $association_id];

    if ($price_modifier_override !== null) {
        $updates[] = "price_modifier_override = :price_override";
        $params[':price_override'] = $price_modifier_override;
    }

    if ($sort_order !== null) {
        $updates[] = "sort_order = :sort";
        $params[':sort'] = $sort_order;
    }

    if (empty($updates)) {
        return true; 
    }

    $sql = "UPDATE product_custom_fields SET " . implode(", ", $updates) . " WHERE id = :id";

    $result = db_query($sql, $params);

    return $result !== false;
}

function remove_product_custom_field(int $product_id, int $field_id): bool
{
    return db_query(
        "DELETE FROM product_custom_fields WHERE product_id = :pid AND custom_field_id = :fid",
        [':pid' => $product_id, ':fid' => $field_id]
    ) !== false;
}

function save_product_custom_fields_ajax(int $product_id, array $custom_fields): bool
{
    global $pdo;

    try {
        
        $transaction_started_here = false;
        if (!$pdo->inTransaction()) {
            $pdo->beginTransaction();
            $transaction_started_here = true;
        }

        
        $delete_sql = "DELETE FROM product_custom_fields WHERE product_id = ?";
        $delete_stmt = $pdo->prepare($delete_sql);
        $delete_stmt->execute([$product_id]);

        
        $insert_sql = "INSERT INTO product_custom_fields
                      (product_id, custom_field_id, price_modifier_override, sort_order, created_at)
                      VALUES (?, ?, ?, ?, datetime('now', 'localtime'))";
        $insert_stmt = $pdo->prepare($insert_sql);

        $sort_order = 1;

        

        foreach ($custom_fields as $field_id => $field_data) {
            
            if (isset($field_data['use']) && $field_data['use'] == 1) {
                $field_id_int = filter_var($field_id, FILTER_VALIDATE_INT);
                $price_modifier_override = !empty($field_data['price_modifier_override']) ?
                    filter_var($field_data['price_modifier_override'], FILTER_VALIDATE_FLOAT) : null;

                if ($field_id_int > 0) {
                    $insert_stmt->execute([
                        $product_id,
                        $field_id_int,
                        $price_modifier_override,
                        $sort_order
                    ]);
                    $sort_order++;
                }
            }
        }

        
        if ($transaction_started_here) {
            $pdo->commit();
        }
        return true;
    } catch (Exception $e) {
        
        if ($transaction_started_here && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function get_cart_custom_field_display(array $custom_field): string
{
    $output = '';

    if ($custom_field['field_type'] === 'custom-text') {
        $output = $custom_field['field_name'] . ': ' . $custom_field['text_value'];

        
        if (!empty($custom_field['font_id'])) {
            $font = get_custom_field_font($custom_field['font_id']);
            if ($font) {
                $output .= ' (Fonte: ' . $font['name'] . ')';
            }
        }
    } elseif ($custom_field['field_type'] === 'file-upload') {
        $output = $custom_field['field_name'] . ': ' . $custom_field['file_name'];
    }

    
    if (!empty($custom_field['price_modifier']) && $custom_field['price_modifier'] > 0) {
        $output .= ' (+' . format_price($custom_field['price_modifier']) . ')';
    }

    return $output;
}

function save_order_item_custom_field(
    int $order_item_id,
    int $field_id,
    string $field_name,
    ?string $field_value = null,
    ?string $file_path = null,
    ?int $font_id = null,
    float $price_modifier = 0.0
): int|false {
    $sql = "INSERT INTO order_item_custom_fields (
                order_item_id, custom_field_id, field_name, field_value,
                file_path, font_id, price_modifier, created_at
            ) VALUES (
                :order_item_id, :field_id, :field_name, :field_value,
                :file_path, :font_id, :price_modifier, datetime('now', 'localtime')
            )";

    $params = [
        ':order_item_id' => $order_item_id,
        ':field_id' => $field_id,
        ':field_name' => $field_name,
        ':field_value' => $field_value,
        ':file_path' => $file_path,
        ':font_id' => $font_id,
        ':price_modifier' => $price_modifier
    ];

    $result = db_query($sql, $params);

    if ($result) {
        $pdo = get_db_connection();
        return $pdo->lastInsertId();
    }

    return false;
}

function get_order_item_custom_fields(int $order_item_id): array
{
    return db_query(
        "SELECT oicf.*, cf.name as field_name, cft.slug as field_type_slug,
                cff.name as font_name, cff.google_font_name, cff.file_path as font_file_path
         FROM order_item_custom_fields oicf
         LEFT JOIN custom_fields cf ON oicf.custom_field_id = cf.id
         LEFT JOIN custom_field_types cft ON cf.field_type_id = cft.id
         LEFT JOIN custom_field_fonts cff ON oicf.font_id = cff.id
         WHERE oicf.order_item_id = :order_item_id
         ORDER BY oicf.id ASC",
        [':order_item_id' => $order_item_id],
        false, true
    );
}

function handle_custom_field_file_upload(array $file, int $product_id, int $field_id): array|false
{
    
    $custom_field = get_custom_field($field_id);
    $config = [];

    if ($custom_field && !empty($custom_field['config_json'])) {
        $config = json_decode($custom_field['config_json'], true) ?: [];
    }

    
    $allowed_types = [
        'application/postscript',
        'image/svg+xml',
        'application/zip',
        'application/x-rar-compressed',
        'application/octet-stream',
        'image/jpeg',
        'image/png',
        'image/bmp'
    ];

    
    $allowed_extensions = ['eps', 'svg', 'cdr', 'ps', 'afdesign', 'zip', 'rar', 'jpg', 'jpeg', 'png', 'bmp'];

    
    if (!empty($config['custom_extensions'])) {
        $custom_exts = explode(',', $config['custom_extensions']);
        foreach ($custom_exts as $ext) {
            $ext = trim(strtolower($ext));
            if (!empty($ext) && !in_array($ext, $allowed_extensions)) {
                $allowed_extensions[] = $ext;
            }
        }
    }

    
    if (!empty($config['allowed_file_types'])) {
        $allowed_extensions = array_merge($allowed_extensions, $config['allowed_file_types']);
        $allowed_extensions = array_unique($allowed_extensions);
    }

    
    $max_file_size = (!empty($config['max_file_size']) ? (float)$config['max_file_size'] : 10) * 1024 * 1024; 

    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }

    
    if ($file['size'] > $max_file_size) {
        return false;
    }

    
    $file_info = pathinfo($file['name']);
    $extension = strtolower($file_info['extension']);

    if (!in_array($extension, $allowed_extensions)) {
        return false;
    }

    
    $upload_dir = PROJECT_ROOT . '/public/uploads/custom_fields/';
    if (!file_exists($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            return false;
        }
    }

    
    $safe_original_name = preg_replace('/[^a-zA-Z0-9_\-\.]/', '', $file_info['filename']);
    $unique_filename = 'custom_' . $product_id . '_' . $field_id . '_' . time() . '_' . $safe_original_name . '.' . $extension;
    $destination = $upload_dir . $unique_filename;

    
    if (!move_uploaded_file($file['tmp_name'], $destination)) {
        return false;
    }

    
    return [
        'original_name' => $file['name'],
        'filename' => $unique_filename,
        'file_path' => '/public/uploads/custom_fields/' . $unique_filename,
        'file_size' => $file['size'],
        'file_type' => $file['type'],
        'extension' => $extension
    ];
}
