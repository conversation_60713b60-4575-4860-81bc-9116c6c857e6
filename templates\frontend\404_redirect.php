<!DOCTYPE html>
<html lang="pt-PT">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Página Não Encontrada - Redirecionando</title>
    <style>
        /* Basic reset and full viewport */
        html, body {
            height: 100%;
            margin: 0;
            overflow: hidden; /* Prevent scrollbars */
            font-family: 'Inter', Arial, sans-serif; /* Using Inter font */
        }

        body {
            background-color: #000011; /* Dark space background */
            color: #fff;
            display: flex; /* Use flexbox for centering */
            /* Change direction to column to stack logo (in canvas) and text */
            flex-direction: column;
            align-items: center;
            justify-content: center; /* Center content vertically */
            text-align: center;
            padding-top: 5%; /* Add some padding to push content down slightly */
            box-sizing: border-box; /* Include padding in height calculation */
        }

        /* Canvas for three.js - positioned behind content */
        #three-canvas {
            position: fixed; /* Fixed position to cover viewport */
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0; /* Behind the content */
        }

        /* Container for the text content */
        .content-overlay {
            position: relative; /* Position relative to body */
            z-index: 1; /* Above the canvas */
            background-color: rgba(0, 0, 0, 0.7); /* Slightly darker background */
            padding: 30px 40px;
            border-radius: 15px; /* Rounded corners */
            max-width: 600px; /* Limit width */
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.3); /* Subtle cyan glow */
            /* Adjusted margin-top for spacing below the repositioned logo */
            margin-top: 80px; /* Increased this value */
        }

        /* Heading style */
        h1 {
            font-size: 2em;
            margin-bottom: 15px;
            color: #ffffff;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.5); /* Text glow */
        }

        /* Paragraph style */
        p {
            font-size: 1.1em;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        /* Link styling */
        a {
            color: #00ffff; /* Bright cyan */
            text-decoration: none; /* Remove underline */
            font-weight: bold;
            transition: color 0.3s ease, text-shadow 0.3s ease; /* Smooth transition */
        }

        a:hover {
            color: #ffffff;
            text-shadow: 0 0 15px #00ffff; /* Glow on hover */
        }

        /* Prominent countdown number */
        #countdown {
            font-weight: bold;
            font-size: 1.3em;
            color: #ffcc00; /* Yellowish color for countdown */
            display: inline-block; /* Allows transform */
            transition: transform 0.2s ease; /* Pop effect */
        }

        /* Simple animation for countdown update */
        #countdown.updated {
             transform: scale(1.2);
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            body {
                padding-top: 3%; /* Adjust padding for smaller screens */
            }
            h1 {
                font-size: 1.5em;
            }
            p {
                font-size: 1em;
            }
            .content-overlay {
                padding: 20px 25px;
                margin: 15px; /* Add margin on smaller screens */
                 /* Adjust margin-top for smaller screens */
                margin-top: 50px;
            }
        }

    </style>
</head>
<body>
    <canvas id="three-canvas"></canvas>

    <div class="content-overlay">
        <h1>Ups! Página Não Encontrada</h1>
        <p>O link que procurou pode ter sido movido ou já não existe.</p>
        <p>Será redirecionado para a página principal em <span id="countdown">10</span> segundos.</p>
        <p>Caso não seja redirecionado automaticamente, clique <a href="/index.php">aqui</a>.</p>
        <!-- Error message placeholder (will be added by JS if needed) -->
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/SVGLoader.js"></script>


    <script>
        // --- Countdown and Redirection Logic (Unchanged) ---
        let countdown = 10; // Start countdown from 10
        const countdownElement = document.getElementById('countdown');
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = Math.max(0, countdown);
            countdownElement.classList.add('updated');
            setTimeout(() => countdownElement.classList.remove('updated'), 200);

            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = '/index.php'; // Redirect URL
            }
        }, 1000);

        // --- three.js Scene Setup ---
        let scene, camera, renderer, logoGroup, stars;
        const clock = new THREE.Clock();

        function initThreeJS() {
            scene = new THREE.Scene();

            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            // Keep camera relatively centered, positioning logo will handle vertical placement
            camera.position.set(0, 0, 50);

            const canvas = document.getElementById('three-canvas');
            renderer = new THREE.WebGLRenderer({ canvas: canvas, antialias: true, alpha: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x000011, 1);

            // --- Starfield ---
            const starGeometry = new THREE.BufferGeometry();
            const starCount = 8000;
            const posArray = new Float32Array(starCount * 3);
            for (let i = 0; i < starCount * 3; i++) {
                posArray[i] = (Math.random() - 0.5) * 1500;
            }
            starGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
            const starMaterial = new THREE.PointsMaterial({
                size: 1.0, color: 0xffffff, transparent: true,
                opacity: 0.7, blending: THREE.AdditiveBlending
            });
            stars = new THREE.Points(starGeometry, starMaterial);
            stars.position.z = -200;
            scene.add(stars);

            // --- Load SVG Logo ---
            logoGroup = new THREE.Group();
            // ** No need to store center here, we'll store the base Y position later **
            scene.add(logoGroup);

            const loader = new THREE.SVGLoader();
            const logoUrl = '/public/assets/images/logo/store_logo.png'; // Relative path

            loader.load(
                logoUrl,
                function (data) {
                    const paths = data.paths;
                    const material = new THREE.MeshBasicMaterial({
                        color: 0xffffff, side: THREE.DoubleSide, depthWrite: false
                    });

                    for (let i = 0; i < paths.length; i++) {
                        const path = paths[i];
                        const shapes = THREE.SVGLoader.createShapes(path);
                        for (let j = 0; j < shapes.length; j++) {
                            const shape = shapes[j];
                            const geometry = new THREE.ShapeGeometry(shape);
                            const mesh = new THREE.Mesh(geometry, material);
                            logoGroup.add(mesh);
                        }
                    }

                    // --- Auto-center, scale, and POSITION the logo ---
                    const box = new THREE.Box3().setFromObject(logoGroup);
                    const size = new THREE.Vector3();
                    box.getSize(size);
                    const center = new THREE.Vector3();
                    box.getCenter(center);

                    const desiredHeight = 20; // Target visual height
                    const scale = desiredHeight / size.y;
                    logoGroup.scale.set(scale, -scale, scale); // Apply scale (flip Y)

                    // Recalculate center *after* scaling
                    const scaledBox = new THREE.Box3().setFromObject(logoGroup);
                    const scaledCenter = new THREE.Vector3();
                    scaledBox.getCenter(scaledCenter);

                    // 1. Center the group geometrically at the origin
                    logoGroup.position.sub(scaledCenter);

                    // 2. **Set the desired vertical base position**
                    //    Positive Y moves it up. Adjust this value as needed.
                    //    A value around 15-25 usually works well to place it above center.
                    const targetBaseY = 22; // <-- Tweak this value
                    logoGroup.position.y = targetBaseY;

                    // 3. Store this base position for the animation
                    logoGroup.userData.baseY = targetBaseY;

                    console.log("SVG Logo loaded, centered, scaled, and positioned.");
                },
                null, // onProgress (optional)
                function (error) {
                    console.error('Error loading SVG:', error);
                    const overlay = document.querySelector('.content-overlay');
                    if (!document.getElementById('logo-error-msg')) {
                        const errorMsg = document.createElement('p');
                        errorMsg.id = 'logo-error-msg';
                        errorMsg.textContent = 'Erro: Não foi possível carregar o logo.';
                        errorMsg.style.color = '#ffaaaa';
                        errorMsg.style.marginTop = '20px';
                        const firstP = overlay.querySelector('p');
                        if (firstP) overlay.insertBefore(errorMsg, firstP);
                        else overlay.appendChild(errorMsg);
                    }
                    console.warn("Could not load SVG logo from '" + logoUrl + "'. Check path/CORS.");
                }
            );

            window.addEventListener('resize', onWindowResize, false);
            animate();
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        function animate() {
            requestAnimationFrame(animate);
            const delta = clock.getDelta();
            const elapsed = clock.getElapsedTime();

            // --- Animate Starfield ---
            if (stars) {
                stars.position.z += delta * 30;
                if (stars.position.z > camera.position.z + 100) stars.position.z -= 800;
                stars.rotation.z += delta * 0.02;
            }

            // --- Animate logo group (gentle float/bob) ---
            // Check if logoGroup exists and has its baseY position stored
            if (logoGroup && logoGroup.userData.baseY !== undefined) {
                 const bobAmplitude = 0.7;
                 const bobFrequency = 1.2;
                 const bobOffset = Math.sin(elapsed * bobFrequency) * bobAmplitude;
                 // Animate relative to the stored base Y position
                 logoGroup.position.y = logoGroup.userData.baseY + bobOffset;

                 // Optional rotation (uncomment if desired)
                 // logoGroup.rotation.y += delta * 0.1;
            }

            renderer.render(scene, camera);
        }

        window.onload = initThreeJS;

    </script>
</body>
</html>