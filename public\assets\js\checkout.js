

document.addEventListener('DOMContentLoaded', function() {
    
    const decreaseButtons = document.querySelectorAll('.decrease-qty-btn');
    const increaseButtons = document.querySelectorAll('.increase-qty-btn');
    const quantityInputs = document.querySelectorAll('.quantity-input');

    
    const vatIdField = document.getElementById('customer_vat_id');
    if (vatIdField) {
        
        vatIdField.addEventListener('input', function(e) {
            
            this.value = this.value.replace(/\D/g, '');

            
            if (this.value.length > 9) {
                this.value = this.value.slice(0, 9);
            }
        });

        
        if (vatIdField.value) {
            vatIdField.value = vatIdField.value.replace(/^PT/i, '').replace(/\D/g, '');
        }
    }

    
    updateVatIdRequirement();

    
    function updateCartItemQuantity(cartKey, newQuantity) {
        
        const loadingToast = typeof window.showToast === 'function'
            ? window.showToast('Processando', 'Atualizando quantidade...', 'ri-loader-4-line animate-spin', 'border-blue-500')
            : null;

        
        const formData = new FormData();
        formData.append('action', 'update_cart_item');
        formData.append('cart_key', cartKey);
        formData.append('quantity', newQuantity);

        
        if (window.eshopSessionParam && window.eshopSessionId) {
            formData.append(window.eshopSessionParam, window.eshopSessionId);
        }

        
        fetch(`${window.eshopBaseUrl || ''}/includes/ajax_handler.php`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            
            if (loadingToast) {
                loadingToast.close();
            }

            if (data.success) {
                
                const input = document.querySelector(`.quantity-input[data-cart-key="${cartKey}"]`);
                if (input) {
                    input.value = newQuantity;
                }

                
                const totalItemElement = document.querySelector(`[data-cart-key="${cartKey}"] .total-item`);
                if (totalItemElement && data.item_price) {
                    totalItemElement.textContent = data.item_price;
                }

                
                updateVatIdRequirement();

                
                window.location.reload();
            } else {
                
                if (typeof window.showToast === 'function') {
                    window.showToast('Erro', data.error || 'Não foi possível atualizar a quantidade.', 'ri-error-warning-line', 'border-red-500');
                } else {
                    alert('Erro: ' + (data.error || 'Não foi possível atualizar a quantidade.'));
                }
            }
        })
        .catch(error => {
            console.error('Error updating quantity:', error);
            if (typeof window.showToast === 'function') {
                window.showToast('Erro', 'Ocorreu um problema de rede.', 'ri-error-warning-line', 'border-red-500');
            } else {
                alert('Erro: Ocorreu um problema de rede.');
            }
        });
    }

    
    decreaseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const cartKey = this.getAttribute('data-cart-key');
            const input = document.querySelector(`.quantity-input[data-cart-key="${cartKey}"]`);
            if (input) {
                const currentValue = parseInt(input.value);
                if (currentValue > 1) {
                    updateCartItemQuantity(cartKey, currentValue - 1);
                }
            }
        });
    });

    
    increaseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const cartKey = this.getAttribute('data-cart-key');
            const input = document.querySelector(`.quantity-input[data-cart-key="${cartKey}"]`);
            if (input) {
                const currentValue = parseInt(input.value);
                const maxValue = parseInt(input.getAttribute('max') || '9999');
                if (currentValue < maxValue) {
                    updateCartItemQuantity(cartKey, currentValue + 1);
                } else {
                    if (typeof window.showToast === 'function') {
                        window.showToast('Aviso', 'Quantidade máxima em stock atingida.', 'ri-error-warning-line', 'border-yellow-500');
                    } else {
                        alert('Aviso: Quantidade máxima em stock atingida.');
                    }
                }
            }
        });
    });

    
    quantityInputs.forEach(input => {
        input.addEventListener('change', function() {
            const cartKey = this.getAttribute('data-cart-key');
            const newValue = parseInt(this.value);
            const minValue = parseInt(this.getAttribute('min') || '1');
            const maxValue = parseInt(this.getAttribute('max') || '9999');

            
            if (isNaN(newValue) || newValue < minValue) {
                this.value = minValue;
                updateCartItemQuantity(cartKey, minValue);
            } else if (newValue > maxValue) {
                this.value = maxValue;
                updateCartItemQuantity(cartKey, maxValue);
                if (typeof window.showToast === 'function') {
                    window.showToast('Aviso', 'Quantidade máxima em stock atingida.', 'ri-error-warning-line', 'border-yellow-500');
                } else {
                    alert('Aviso: Quantidade máxima em stock atingida.');
                }
            } else {
                updateCartItemQuantity(cartKey, newValue);
            }
        });
    });

    
    

    

    function updateVatIdRequirement() {
        
        const vatIdField = document.getElementById('customer_vat_id');
        const vatIdRequiredIndicator = document.querySelector('.vat-id-required-indicator');

        if (!vatIdField || !vatIdRequiredIndicator) return;

        
        const thresholdText = vatIdRequiredIndicator.closest('label').querySelector('.text-xs.text-gray-500');
        if (!thresholdText) return;

        
        if (thresholdText.textContent.includes('facultativo')) {
            vatIdField.removeAttribute('required');
            vatIdRequiredIndicator.classList.add('hidden');
            return;
        }

        
        const thresholdMatch = thresholdText.textContent.match(/acima de ([^)]+)/);
        if (!thresholdMatch) return;

        
        const grandTotalElement = document.querySelector('.font-semibold.text-lg span');
        if (!grandTotalElement) return;

        
        const grandTotalText = grandTotalElement.textContent.trim();
        const numericValue = parseFloat(grandTotalText.replace(/[^0-9,\.]/g, '').replace(',', '.'));

        const thresholdValue = parseFloat(thresholdMatch[1].replace(/[^0-9,\.]/g, '').replace(',', '.'));

        
        if (numericValue >= thresholdValue) {
            vatIdField.setAttribute('required', 'required');
            vatIdRequiredIndicator.classList.remove('hidden');
        } else {
            vatIdField.removeAttribute('required');
            vatIdRequiredIndicator.classList.add('hidden');
        }
    }
});
