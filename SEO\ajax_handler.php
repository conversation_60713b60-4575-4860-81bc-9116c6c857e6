<?php
// AJAX Handler for Enhanced Backlink Pinging Tool
header('Content-Type: application/json');

// Enable error reporting for debugging
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Handle CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// If it's an OPTIONS request, end it here
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Configuration
$maxRetries = 3;
$retryDelay = 5;
$urlProcessDelay = 1000; // milliseconds
$pingSitesFile = __DIR__ . '/backlink_sites.php';

// Get the request body
$json_data = file_get_contents('php://input');
$data = json_decode($json_data, true);

if (!isset($data['action'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Missing action parameter']);
    exit;
}

// Include the backlink sites list
require_once($pingSitesFile);

// Route the request based on action
switch ($data['action']) {
    case 'fetch_urls':
        fetchUrls($data);
        break;
    case 'get_random_backlink_sites':
        getRandomBacklinkSites($data);
        break;
    case 'ping_url':
        pingUrl($data);
        break;
    case 'get_ping_sites':
        getPingSites();
        break;
    case 'save_ping_sites':
        savePingSites($data);
        break;
    default:
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid action']);
        break;
}

// Function to fetch URLs from feed and sitemap
function fetchUrls($data) {
    global $maxRetries, $retryDelay;
    
    if (!isset($data['feedUrl']) || !isset($data['sitemapUrl'])) {
        echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
        return;
    }

    $feedUrl = $data['feedUrl'];
    $sitemapUrl = $data['sitemapUrl'];
    $maxUrlsToProcess = isset($data['maxUrlsToProcess']) ? intval($data['maxUrlsToProcess']) : 50;
    
    // Validate max URLs
    if ($maxUrlsToProcess < 1) $maxUrlsToProcess = 1;
    // Remove the upper limit to allow processing all URLs
    // if ($maxUrlsToProcess > 100) $maxUrlsToProcess = 100;
    
    $feedUrls = [];
    $sitemapUrls = [];
    $errors = [];
    $skippedUrls = [];

    // Fetch feed URLs
    if (!empty($feedUrl)) {
        $feedContent = fetchContent($feedUrl, $maxRetries, $retryDelay);
        if ($feedContent === false) {
            $errors[] = "Error fetching feed content from $feedUrl";
        } else {
            $feedXml = @simplexml_load_string($feedContent);
            if ($feedXml === false) {
                $errors[] = "Error parsing XML from feed file";
            } else {
                if (isset($feedXml->channel->item)) {
                    foreach ($feedXml->channel->item as $item) {
                        // Extract URL from the <link> tag
                        if (isset($item->link)) {
                            $linkUrl = trim((string)$item->link);
                            if (!empty($linkUrl)) {
                                // Skip problematic URLs
                                if (strpos($linkUrl, 'cesta.html') !== false) {
                                    $skippedUrls[] = $linkUrl;
                                } else {
                                    $feedUrls[] = $linkUrl;
                                }
                            }
                        }
                        // Extract URL from the <guid> tag
                        if (isset($item->guid)) {
                            $guidUrl = trim((string)$item->guid);
                            if (!empty($guidUrl)) {
                                // Skip problematic URLs
                                if (strpos($guidUrl, 'cesta.html') !== false) {
                                    $skippedUrls[] = $guidUrl;
                                } else {
                                    $feedUrls[] = $guidUrl;
                                }
                            }
                        }
                        
                        // Extract URLs from description or content tags (might contain HTML with links)
                        $contentFields = ['description', 'content', 'content:encoded', 'summary'];
                        foreach ($contentFields as $field) {
                            if (isset($item->$field) || isset($item->children('content', true)->encoded)) {
                                // Get content from either regular field or namespaced field
                                $content = isset($item->$field) ? (string)$item->$field : (string)$item->children('content', true)->encoded;
                                if (!empty($content)) {
                                    // Extract URLs from href attributes
                                    preg_match_all('/href=["\'](https?:\/\/[^"\']+)["\']/', $content, $matches);
                                    if (isset($matches[1]) && !empty($matches[1])) {
                                        foreach ($matches[1] as $url) {
                                            $url = trim($url);
                                            if (!empty($url)) {
                                                // Skip problematic URLs
                                                if (strpos($url, 'cesta.html') !== false) {
                                                    $skippedUrls[] = $url;
                                                } else {
                                                    $feedUrls[] = $url;
                                                }
                                            }
                                        }
                                    }
                                    
                                    // Extract URLs that appear directly in the content (not in href attributes)
                                    preg_match_all('/(https?:\/\/[^\s"\'<>]+\.[^\s"\'<>]+)/', $content, $directMatches);
                                    if (isset($directMatches[1]) && !empty($directMatches[1])) {
                                        foreach ($directMatches[1] as $url) {
                                            $url = trim($url);
                                            // Remove trailing punctuation that might be part of the text
                                            $url = rtrim($url, '.,;:!?)]}');
                                            if (!empty($url) && filter_var($url, FILTER_VALIDATE_URL)) {
                                                // Skip problematic URLs
                                                if (strpos($url, 'cesta.html') !== false) {
                                                    $skippedUrls[] = $url;
                                                } else {
                                                    $feedUrls[] = $url;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        
                        // Extract URLs from any custom fields that might contain URLs
                        foreach ($item as $fieldName => $fieldValue) {
                            // Skip already processed standard fields
                            if (in_array($fieldName, ['link', 'guid', 'description', 'content', 'summary'])) {
                                continue;
                            }
                            
                            $fieldStr = trim((string)$fieldValue);
                            // Check if the field value looks like a URL
                            if (!empty($fieldStr)) {
                                // First check if it's a direct URL
                                if (filter_var($fieldStr, FILTER_VALIDATE_URL)) {
                                    // Skip problematic URLs
                                    if (strpos($fieldStr, 'cesta.html') !== false) {
                                        $skippedUrls[] = $fieldStr;
                                    } else {
                                        $feedUrls[] = $fieldStr;
                                    }
                                }
                                
                                // Also check if the field contains URLs within text
                                preg_match_all('/(https?:\/\/[^\s"\'<>]+\.[^\s"\'<>]+)/', $fieldStr, $matches);
                                if (isset($matches[1]) && !empty($matches[1])) {
                                    foreach ($matches[1] as $url) {
                                        $url = trim($url);
                                        // Remove trailing punctuation that might be part of the text
                                        $url = rtrim($url, '.,;:!?)]}');
                                        if (!empty($url) && filter_var($url, FILTER_VALIDATE_URL)) {
                                            // Skip problematic URLs
                                            if (strpos($url, 'cesta.html') !== false) {
                                                $skippedUrls[] = $url;
                                            } else {
                                                $feedUrls[] = $url;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Fetch sitemap URLs
    if (!empty($sitemapUrl)) {
        $sitemapContent = fetchContent($sitemapUrl, $maxRetries, $retryDelay);
        if ($sitemapContent === false) {
            $errors[] = "Error fetching sitemap content from $sitemapUrl";
        } else {
            $sitemapXml = @simplexml_load_string($sitemapContent);
            if ($sitemapXml === false) {
                $errors[] = "Error parsing XML from sitemap file";
            } else {
                // The sitemap structure: <urlset><url><loc>...</loc></url></urlset>
                foreach ($sitemapXml->url as $urlNode) {
                    if (isset($urlNode->loc)) {
                        $url = trim((string)$urlNode->loc);
                        if (!empty($url)) {
                            // Skip problematic URLs
                            if (strpos($url, 'cesta.html') !== false) {
                                $skippedUrls[] = $url;
                            } else {
                                $sitemapUrls[] = $url;
                            }
                        }
                    }
                }
            }
        }
    }

    // Merge URLs and remove duplicates
    $allUrls = array_unique(array_merge($feedUrls, $sitemapUrls));
    
    // Get the total number of URLs
    $totalUrls = count($allUrls);
    
    // By default, use all URLs (no limit)
    $limitedUrls = $allUrls;
    $limitApplied = false;
    
    // Only apply limit if explicitly requested and not set to total count
    if ($maxUrlsToProcess < $totalUrls && $maxUrlsToProcess > 0) {
        $limitedUrls = array_slice($allUrls, 0, $maxUrlsToProcess);
        $limitApplied = true;
    } else {
        // Use all URLs and update maxUrlsToProcess to match total
        $maxUrlsToProcess = $totalUrls;
    }

    if (empty($allUrls) && !empty($errors)) {
        echo json_encode(['success' => false, 'error' => implode('; ', $errors)]);
    } else {
        echo json_encode([
            'success' => true, 
            'urls' => array_values($limitedUrls),
            'feedCount' => count($feedUrls),
            'sitemapCount' => count($sitemapUrls),
            'totalCount' => $totalUrls,
            'processCount' => count($limitedUrls),
            'limitApplied' => $limitApplied,
            'maxUrlsToProcess' => $maxUrlsToProcess,
            'skippedUrls' => $skippedUrls,
            'skippedCount' => count($skippedUrls),
            'errors' => $errors
        ]);
    }
}

// Function to get random backlink sites
function getRandomBacklinkSites($data) {
    global $backlink_sites;
    
    $count = isset($data['count']) ? intval($data['count']) : 3;
    if ($count < 1) $count = 1;
    // Allow up to the maximum number of available backlink sites
    if ($count > count($backlink_sites)) $count = count($backlink_sites);
    
    // Get all available keys
    $keys = array_keys($backlink_sites);
    
    // If we have no backlink sites, return empty
    if (empty($keys)) {
        echo json_encode([
            'success' => true,
            'sites' => [],
            'count' => 0
        ]);
        return;
    }
    
    // Shuffle the keys for complete randomization
    shuffle($keys);
    $randomSites = [];
    
    // Use exactly the number requested - no more, no less
    $numSites = min(count($backlink_sites), $count);
    
    // Select random sites - exactly the number requested
    for ($i = 0; $i < $numSites; $i++) {
        $key = $keys[$i];
        $randomSites[$key] = $backlink_sites[$key];
    }
    
    echo json_encode([
        'success' => true,
        'sites' => $randomSites,
        'count' => count($randomSites)
    ]);
}

// Function to ping a URL to a backlink site
function pingUrl($data) {
    global $maxRetries, $retryDelay, $urlProcessDelay;
    
    if (!isset($data['url']) || !isset($data['backlinkSite']) || !isset($data['backlinkUrl'])) {
        echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
        return;
    }

    $url = $data['url'];
    $backlinkSite = $data['backlinkSite'];
    $backlinkUrl = $data['backlinkUrl'];
    
    // Skip problematic URLs
    if (strpos($url, 'cesta.html') !== false) {
        echo json_encode([
            'success' => false,
            'error' => 'Skipped known problematic URL',
            'skipped' => true
        ]);
        return;
    }
    
    // Prepare the ping request
    $pingUrl = $backlinkUrl;
    
    // Add URL parameter if not already in the ping URL
    if (strpos($pingUrl, '?') === false) {
        $pingUrl .= '?url=' . urlencode($url);
    } else {
        $pingUrl .= '&url=' . urlencode($url);
    }
    
    // Some ping services might need additional parameters
    $pingUrl .= '&title=' . urlencode('Website Update');
    
    // Send the ping request with error handling and retry logic
    $attempts = 0;
    $success = false;
    $httpCode = 0;
    $response = '';
    $curlError = '';
    $duration = 0;
    $isNonResponsive = false;
    
    while ($attempts < $maxRetries && !$success) {
        if ($attempts > 0) {
            // Add delay between retry attempts
            sleep($retryDelay);
        }
        
        $attempts++;
        
        $ch = curl_init($pingUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; BacklinkPinger/1.0)');
        
        $startTime = microtime(true);
        $response = curl_exec($ch);
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        // Check for 503 errors specifically
        if ($httpCode == 503) {
            continue; // Retry on 503 errors
        }
        
        // Check for timeout or connection errors
        if ($curlError && (strpos($curlError, 'timed out') !== false || 
                          strpos($curlError, 'connection') !== false)) {
            $isNonResponsive = true;
        }
        
        // Consider success if no curl error and http code is 2xx or 3xx
        if (empty($curlError) && ($httpCode >= 200 && $httpCode < 400)) {
            $success = true;
        }
    }
    
    // Add a small delay to avoid overwhelming servers
    usleep($urlProcessDelay * 1000); // Convert milliseconds to microseconds
    
    if ($success) {
        echo json_encode([
            'success' => true,
            'httpCode' => $httpCode,
            'response' => substr($response, 0, 255) . (strlen($response) > 255 ? '...' : ''),
            'duration' => $duration,
            'attempts' => $attempts
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => $curlError ? $curlError : "HTTP Code: $httpCode",
            'httpCode' => $httpCode,
            'duration' => $duration,
            'attempts' => $attempts,
            'isNonResponsive' => $isNonResponsive
        ]);
    }
}

// Function to get all ping sites
function getPingSites() {
    global $backlink_sites;
    
    echo json_encode([
        'success' => true,
        'sites' => $backlink_sites,
        'count' => count($backlink_sites)
    ]);
}

// Function to save ping sites
function savePingSites($data) {
    global $pingSitesFile;
    
    if (!isset($data['sites']) || !is_array($data['sites'])) {
        echo json_encode(['success' => false, 'error' => 'Missing or invalid sites parameter']);
        return;
    }
    
    $sites = $data['sites'];
    
    // Generate PHP code
    $code = "<?php\n// Backlink sites list for the backlink pinging tool\n\n\$backlink_sites = [\n";
    
    foreach ($sites as $name => $url) {
        $code .= "    '" . addslashes($name) . "' => '" . addslashes($url) . "',\n";
    }
    
    $code .= "];";
    
    // Write to file
    $result = file_put_contents($pingSitesFile, $code);
    
    if ($result === false) {
        echo json_encode(['success' => false, 'error' => 'Failed to write to file']);
    } else {
        echo json_encode([
            'success' => true,
            'message' => 'Ping sites saved successfully',
            'count' => count($sites)
        ]);
    }
}

// Function to fetch remote content with retry logic
function fetchContent($url, $maxRetries = 3, $retryDelay = 5) {
    $attempts = 0;
    $content = false;
    
    while ($attempts < $maxRetries && $content === false) {
        if ($attempts > 0) {
            sleep($retryDelay);
        }
        
        $attempts++;
        
        $content = @file_get_contents($url);
        if ($content === false) {
            // Fallback to cURL if file_get_contents fails
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; BacklinkPinger/1.0)');
            $content = curl_exec($ch);
            
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            if (curl_errno($ch) || $httpCode == 503) {
                $content = false;
            }
            
            curl_close($ch);
        }
    }
    
    return $content;
}
?>
