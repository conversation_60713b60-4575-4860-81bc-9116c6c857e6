<?php

require_once __DIR__ . '/../../config.php';
require_once __DIR__ . '/../db.php';

function create_digital_files_table(): bool
{
    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        
        $pdo->exec("CREATE TABLE IF NOT EXISTS digital_files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            original_filename TEXT NOT NULL,
            display_name TEXT, -- Added display name column
            file_path TEXT NOT NULL,
            file_size INTEGER NOT NULL,
            file_type TEXT,
            description TEXT,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
        )");

        
        $stmt = $pdo->query("PRAGMA table_info(digital_files)");
        $digital_files_columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);
        if (!in_array('display_name', $digital_files_columns)) {
            $pdo->exec("ALTER TABLE digital_files ADD COLUMN display_name TEXT");
        }

        
        $stmt = $pdo->query("PRAGMA table_info(digital_products)");
        $digital_products_columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);

        if (!in_array('digital_file_id', $digital_products_columns)) {
            $pdo->exec("ALTER TABLE digital_products ADD COLUMN digital_file_id INTEGER");
        }

        
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_digital_products_file_id ON digital_products (digital_file_id)");

        
        $stmt = $pdo->query("
            SELECT dp.id AS product_id, dp.file_path, p.name_pt AS product_name
            FROM digital_products dp
            LEFT JOIN products p ON dp.product_id = p.id
            WHERE dp.file_path IS NOT NULL AND dp.digital_file_id IS NULL
        ");
        $digital_products = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($digital_products as $product) {
            if (file_exists($product['file_path'])) {
                
                $original_filename = basename($product['file_path']);
                $file_size = filesize($product['file_path']);
                $file_type = mime_content_type($product['file_path']) ?: 'application/octet-stream';

                
                $display_name = !empty($product['product_name']) ? $product['product_name'] : $original_filename;

                $insert = $pdo->prepare("INSERT INTO digital_files (
                    original_filename, display_name, file_path, file_size, file_type, created_at, updated_at
                ) VALUES (
                    :original_filename, :display_name, :file_path, :file_size, :file_type,
                    datetime('now', 'localtime'), datetime('now', 'localtime')
                )");

                $insert->execute([
                    ':original_filename' => $original_filename,
                    ':display_name' => $display_name, 
                    ':file_path' => $product['file_path'],
                    ':file_size' => $file_size,
                    ':file_type' => $file_type
                ]);

                $file_id = $pdo->lastInsertId();

                
                $update = $pdo->prepare("UPDATE digital_products SET digital_file_id = :file_id WHERE id = :product_id");
                $update->execute([
                    ':file_id' => $file_id,
                    ':product_id' => $product['product_id']
                ]);
            }
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function check_and_create_digital_files_table(PDO $pdo): void
{
    try {
        
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_files'");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {
            require_once __DIR__ . '/digital_files_table.php';
            create_digital_files_table();
        }
    } catch (PDOException $e) {
    }
}
