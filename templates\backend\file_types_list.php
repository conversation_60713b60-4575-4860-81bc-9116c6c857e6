<?php

require_once __DIR__ . '/../../includes/digital_product_functions.php';

$file_types = get_all_file_types();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_file_type'])) {
        $name = sanitize_input($_POST['name'] ?? '');
        $extension = sanitize_input($_POST['extension'] ?? '');
        
        
        $errors = [];
        
        if (empty($name)) {
            $errors[] = 'O nome do tipo de arquivo é obrigatório.';
        }
        
        if (empty($extension)) {
            $errors[] = 'A extensão do tipo de arquivo é obrigatória.';
        } elseif (strpos($extension, '.') !== 0) {
            $extension = '.' . $extension;
        }
        
        
        foreach ($file_types as $type) {
            if (strtolower($type['extension']) === strtolower($extension)) {
                $errors[] = 'Esta extensão já existe.';
                break;
            }
        }
        
        if (empty($errors)) {
            
            $sql = "INSERT INTO digital_product_file_types (
                        name, extension, created_at, updated_at
                    ) VALUES (
                        :name, :extension, datetime('now', 'localtime'), datetime('now', 'localtime')
                    )";
            
            $result = db_query($sql, [
                ':name' => $name,
                ':extension' => $extension
            ]);
            
            if ($result !== false) {
                add_flash_message('Tipo de arquivo adicionado com sucesso.', 'success');
                
                header('Location: admin.php?section=file_types&' . get_session_id_param());
                exit;
            } else {
                add_flash_message('Erro ao adicionar tipo de arquivo.', 'danger');
            }
        } else {
            foreach ($errors as $error) {
                add_flash_message($error, 'danger');
            }
        }
    } elseif (isset($_POST['edit_file_type'])) {
        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        $name = sanitize_input($_POST['name'] ?? '');
        $extension = sanitize_input($_POST['extension'] ?? '');
        
        
        $errors = [];
        
        if (empty($name)) {
            $errors[] = 'O nome do tipo de arquivo é obrigatório.';
        }
        
        if (empty($extension)) {
            $errors[] = 'A extensão do tipo de arquivo é obrigatória.';
        } elseif (strpos($extension, '.') !== 0) {
            $extension = '.' . $extension;
        }
        
        
        foreach ($file_types as $type) {
            if ($type['id'] != $id && strtolower($type['extension']) === strtolower($extension)) {
                $errors[] = 'Esta extensão já existe.';
                break;
            }
        }
        
        if (empty($errors)) {
            
            $sql = "UPDATE digital_product_file_types SET
                        name = :name,
                        extension = :extension,
                        updated_at = datetime('now', 'localtime')
                    WHERE id = :id";
            
            $result = db_query($sql, [
                ':id' => $id,
                ':name' => $name,
                ':extension' => $extension
            ]);
            
            if ($result !== false) {
                add_flash_message('Tipo de arquivo atualizado com sucesso.', 'success');
                
                header('Location: admin.php?section=file_types&' . get_session_id_param());
                exit;
            } else {
                add_flash_message('Erro ao atualizar tipo de arquivo.', 'danger');
            }
        } else {
            foreach ($errors as $error) {
                add_flash_message($error, 'danger');
            }
        }
    } elseif (isset($_POST['delete_file_type'])) {
        $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
        
        
        $sql = "SELECT COUNT(*) as count FROM digital_product_file_type_associations WHERE file_type_id = :id";
        $result = db_query($sql, [':id' => $id], true);
        
        if ($result && $result['count'] > 0) {
            add_flash_message('Este tipo de arquivo está em uso e não pode ser excluído.', 'danger');
        } else {
            
            $sql = "DELETE FROM digital_product_file_types WHERE id = :id";
            $result = db_query($sql, [':id' => $id]);
            
            if ($result !== false) {
                add_flash_message('Tipo de arquivo excluído com sucesso.', 'success');
                
                header('Location: admin.php?section=file_types&' . get_session_id_param());
                exit;
            } else {
                add_flash_message('Erro ao excluir tipo de arquivo.', 'danger');
            }
        }
    }
    
    
    $file_types = get_all_file_types();
}

display_flash_messages();
?>

<div class="container-fluid">
    <h1 class="h3 mb-4">Tipos de Arquivos Digitais</h1>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Adicionar Novo Tipo de Arquivo</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="admin.php?section=file_types&<?= get_session_id_param() ?>">
                        <?= csrf_input_field() ?>
                        <div class="mb-3">
                            <label for="name" class="form-label">Nome *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="form-text">Ex: Adobe Photoshop</div>
                        </div>
                        <div class="mb-3">
                            <label for="extension" class="form-label">Extensão *</label>
                            <input type="text" class="form-control" id="extension" name="extension" required>
                            <div class="form-text">Ex: .psd (inclua o ponto)</div>
                        </div>
                        <button type="submit" name="add_file_type" value="1" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Adicionar
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Tipos de Arquivos Existentes</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($file_types)): ?>
                        <div class="alert alert-info" role="alert">
                            Nenhum tipo de arquivo encontrado.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Nome</th>
                                        <th>Extensão</th>
                                        <th class="text-end">Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($file_types as $type): ?>
                                        <tr>
                                            <td><?= sanitize_input($type['name']) ?></td>
                                            <td><code><?= sanitize_input($type['extension']) ?></code></td>
                                            <td class="text-end">
                                                <button type="button" class="btn btn-sm btn-info edit-file-type-btn" 
                                                        data-id="<?= (int)$type['id'] ?>"
                                                        data-name="<?= sanitize_input($type['name']) ?>"
                                                        data-extension="<?= sanitize_input($type['extension']) ?>">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger delete-file-type-btn"
                                                        data-id="<?= (int)$type['id'] ?>"
                                                        data-name="<?= sanitize_input($type['name']) ?>">
                                                    <i class="bi bi-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit File Type Modal -->
<div class="modal fade" id="editFileTypeModal" tabindex="-1" aria-labelledby="editFileTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="admin.php?section=file_types&<?= get_session_id_param() ?>">
                <?= csrf_input_field() ?>
                <input type="hidden" name="id" id="edit_id">
                <div class="modal-header">
                    <h5 class="modal-title" id="editFileTypeModalLabel">Editar Tipo de Arquivo</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Nome *</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_extension" class="form-label">Extensão *</label>
                        <input type="text" class="form-control" id="edit_extension" name="extension" required>
                        <div class="form-text">Ex: .psd (inclua o ponto)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" name="edit_file_type" value="1" class="btn btn-primary">Salvar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete File Type Modal -->
<div class="modal fade" id="deleteFileTypeModal" tabindex="-1" aria-labelledby="deleteFileTypeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="admin.php?section=file_types&<?= get_session_id_param() ?>">
                <?= csrf_input_field() ?>
                <input type="hidden" name="id" id="delete_id">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteFileTypeModalLabel">Confirmar Exclusão</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Tem certeza que deseja excluir o tipo de arquivo <strong id="delete_name"></strong>?</p>
                    <p class="text-danger">Esta ação não pode ser desfeita.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" name="delete_file_type" value="1" class="btn btn-danger">Excluir</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Edit file type
    const editButtons = document.querySelectorAll('.edit-file-type-btn');
    const editModal = new bootstrap.Modal(document.getElementById('editFileTypeModal'));
    
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const extension = this.getAttribute('data-extension');
            
            document.getElementById('edit_id').value = id;
            document.getElementById('edit_name').value = name;
            document.getElementById('edit_extension').value = extension;
            
            editModal.show();
        });
    });
    
    // Delete file type
    const deleteButtons = document.querySelectorAll('.delete-file-type-btn');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteFileTypeModal'));
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            
            document.getElementById('delete_id').value = id;
            document.getElementById('delete_name').textContent = name;
            
            deleteModal.show();
        });
    });
});
</script>
