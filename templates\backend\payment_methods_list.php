<?php

?>

<h1><PERSON><PERSON><PERSON> de Pagamento Manuais</h1>
<p class="text-muted"><PERSON><PERSON><PERSON>ie os métodos de pagamento manuais disponíveis para os clientes.</p>

<a href="admin.php?section=payment_methods&action=new&<?= get_session_id_param() ?>" class="btn btn-success mb-3">
    <i class="bi bi-plus-circle"></i> Adicionar Novo Método de Pagamento
</a>

<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?= $success_message ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?= $error_message ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-body">
        <?php
        

        
        if (!isset($payment_methods) || !is_array($payment_methods) || empty($payment_methods)):
        ?>
            <div class="alert alert-info">
                Não existem métodos de pagamento manuais configurados. Clique no botão acima para adicionar um.
            </div>
            <script>
                // Auto-refresh once to try to load the data
                document.addEventListener('DOMContentLoaded', function() {
                    // Only refresh if this is the first load (no URL parameter indicating a refresh)
                    if (!window.location.href.includes('refreshed=1')) {
                        setTimeout(function() {
                            window.location.href = window.location.href + (window.location.href.includes('?') ? '&' : '?') + 'refreshed=1';
                        }, 500); // Short delay before refresh
                    }
                });
            </script>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Título</th>
                            <th>Estado</th>
                            <th>Ordem</th>
                            <th class="text-end">Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($payment_methods as $method): ?>
                            <tr>
                                <td><?= sanitize_input($method['id']) ?></td>
                                <td><?= sanitize_input($method['title']) ?></td>
                                <td>
                                    <?php if ($method['is_active']): ?>
                                        <span class="badge bg-success">Ativo</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Inativo</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= sanitize_input($method['sort_order']) ?></td>
                                <td class="text-end">
                                    <a href="admin.php?section=payment_methods&action=edit&id=<?= $method['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-primary">
                                        <i class="bi bi-pencil"></i> Editar
                                    </a>
                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $method['id'] ?>">
                                        <i class="bi bi-trash"></i> Eliminar
                                    </button>
                                </td>
                            </tr>

                            <!-- Delete Confirmation Modal -->
                            <div class="modal fade" id="deleteModal<?= $method['id'] ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?= $method['id'] ?>" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="deleteModalLabel<?= $method['id'] ?>">Confirmar Eliminação</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            Tem certeza que deseja eliminar o método de pagamento <strong><?= sanitize_input($method['title']) ?></strong>?
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                                            <a href="admin.php?section=payment_methods&action=delete&id=<?= $method['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-danger">Eliminar</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>
