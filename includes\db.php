<?php

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/coupon_functions.php';

function get_db_connection(): ?PDO
{
    static $pdo = null;


    if ($pdo instanceof PDO) {
        return $pdo;
    }

    $db_path = DB_PATH;
    $db_dir = dirname($db_path);


    if (!is_dir($db_dir)) {
        if (!mkdir($db_dir, 0755, true)) {
            return null;
        }
    }

    $needs_initialization = !file_exists($db_path);

    try {

        $pdo = new PDO('sqlite:' . $db_path);

        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

        $pdo->exec('PRAGMA foreign_keys = ON;');


        if ($needs_initialization) {
            initialize_database_schema($pdo);
        } else {

            check_and_update_pages_schema($pdo);
            check_and_create_product_info_fields_table($pdo);
            check_and_update_contacts_schema($pdo);
            check_and_create_page_placeholders_table($pdo);
            check_and_create_coupons_table($pdo);
            check_and_update_orders_schema($pdo);
            check_and_create_custom_fields_tables($pdo);
            check_and_create_product_videos_table($pdo);
            check_and_create_blog_tables($pdo);
            check_and_update_products_seo_schema($pdo);
            check_and_create_sitemap_configs_table($pdo);
            check_and_create_digital_products_tables($pdo);


            require_once __DIR__ . '/db_migrations/digital_files_table.php';
            check_and_create_digital_files_table($pdo);


            require_once __DIR__ . '/db_migrations/digital_files_short_name.php';
            check_and_add_short_name_column($pdo);


            require_once __DIR__ . '/db_migrations/add_display_name_to_digital_files.php';
            check_and_add_display_name_column($pdo);


            require_once __DIR__ . '/db_migrations/create_download_tokens_table.php';
            create_download_tokens_table($pdo);


            require_once __DIR__ . '/db_migrations/add_stock_tracking_columns.php';
            add_stock_tracking_columns($pdo);


            require_once __DIR__ . '/db_migrations/create_order_visits_table.php';
            create_order_visits_table($pdo);


            require_once __DIR__ . '/db_migrations/remove_filepath_from_digital_products.php';
            check_and_remove_filepath_from_digital_products($pdo);


            require_once __DIR__ . '/db_migrations/add_stock_to_products_table.php';
            add_stock_column_to_products($pdo);


            require_once __DIR__ . '/db_migrations/add_show_title_to_pages.php';
            migrate_add_show_title_to_pages_table($pdo);


            require_once __DIR__ . '/db_migrations/add_seo_social_to_pages.php';
            migrate_add_seo_social_to_pages($pdo);

            // Migration for fixing license_files foreign key
            require_once __DIR__ . '/db_migrations/fix_license_files_fk_reference.php';
            // Ensure migrations table exists before calling migrations that use it
            $pdo->exec("CREATE TABLE IF NOT EXISTS migrations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                executed_at TEXT NOT NULL
            )");
            if (!is_migration_applied($pdo, 'fix_license_files_fk_reference')) {
                migrate_fix_license_files_fk_reference($pdo);
            }

            // Migration for adding CODE blog post type
            require_once __DIR__ . '/db_migrations/add_code_blog_post_type.php';
            if (!has_code_blog_post_type_migrated($pdo)) { // Custom check for this specific migration
                migrate_add_code_blog_post_type($pdo);
            }

            // Migration for renaming pages.show_in_headerINTEGER to pages.show_in_header
            require_once __DIR__ . '/db_migrations/rename_pages_show_in_header_column.php';
            if (!is_migration_applied($pdo, 'rename_pages_show_in_header_column')) {
                migrate_rename_pages_show_in_header_column($pdo);
            }

            // Migration for dropping digital_file_type_associations table (we'll use digital_product_file_type_associations instead)
            require_once __DIR__ . '/db_migrations/drop_digital_file_type_associations.php';
            if (!is_migration_applied($pdo, 'drop_digital_file_type_associations')) {
                migrate_drop_digital_file_type_associations();
                // Record the migration
                $pdo->exec("INSERT INTO migrations (name, executed_at) VALUES ('drop_digital_file_type_associations', datetime('now', 'localtime'))");
            }

            // Migration for fixing file paths in digital_files table
            require_once __DIR__ . '/db_migrations/fix_digital_files_paths.php';
            if (!is_migration_applied($pdo, 'fix_digital_files_paths')) {
                migrate_fix_digital_files_paths();
                // Record the migration
                $pdo->exec("INSERT INTO migrations (name, executed_at) VALUES ('fix_digital_files_paths', datetime('now', 'localtime'))");
            }

            // Migration for creating digital_file_type_associations table
            require_once __DIR__ . '/db_migrations/create_digital_file_type_associations.php';
            if (!is_migration_applied($pdo, 'create_digital_file_type_associations')) {
                create_digital_file_type_associations_table($pdo);
            }

            // Migration for migrating file types tables
            require_once __DIR__ . '/db_migrations/migrate_file_types_tables.php';
            if (!is_migration_applied($pdo, 'migrate_file_types_tables')) {
                migrate_file_types_tables();
            }

            // Migration for dropping old file types tables
            require_once __DIR__ . '/db_migrations/drop_old_file_types_tables.php';
            if (!is_migration_applied($pdo, 'drop_old_file_types_tables')) {
                drop_old_file_types_tables();
            }
        }

        return $pdo;

    } catch (PDOException $e) {



        return null;
    }
}

function initialize_database_schema(PDO $pdo): void
{
    $sql_commands = [

        "CREATE TABLE settings (
            setting_key TEXT PRIMARY KEY NOT NULL,
            setting_value TEXT
        );",


        "CREATE TABLE sessions (
            session_id TEXT PRIMARY KEY NOT NULL,
            data TEXT,
            user_fingerprint TEXT NOT NULL,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            last_access TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            expires_at TEXT NOT NULL
        );",
        "CREATE INDEX idx_sessions_expires ON sessions (expires_at);",
        "CREATE INDEX idx_sessions_fingerprint ON sessions (user_fingerprint);",


        "CREATE TABLE IF NOT EXISTS \"products\" (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_pt TEXT NOT NULL,
            slug TEXT UNIQUE NOT NULL,
            description_pt TEXT,
            base_price REAL NOT NULL DEFAULT 0.0,
            is_active INTEGER NOT NULL DEFAULT 1,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            vat_rate_id TEXT,
            sku TEXT,
            stock TEXT,
            seo_title TEXT,
            seo_description TEXT,
            seo_keywords TEXT,
            og_title TEXT,
            og_description TEXT,
            og_image TEXT,
            twitter_card TEXT,
            twitter_title TEXT,
            twitter_description TEXT,
            twitter_image TEXT,
            product_type TEXT NOT NULL DEFAULT 'regular' CHECK(product_type IN ('regular', 'variation', 'digital')),
            df TEXT
        );",
        "CREATE INDEX idx_products_slug ON products (slug);",
        "CREATE INDEX idx_products_active ON products (is_active);",


        "CREATE TABLE categories (
           id INTEGER PRIMARY KEY AUTOINCREMENT,
           name TEXT NOT NULL UNIQUE, -- Simplified name column
           slug TEXT UNIQUE NOT NULL,
           parent_id INTEGER DEFAULT NULL, -- For subcategories
           description TEXT,
           is_active INTEGER NOT NULL DEFAULT 1,
     created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
           updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
           FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
       );",
       "CREATE INDEX idx_categories_slug ON categories (slug);",
       "CREATE INDEX idx_categories_active ON categories (is_active);",


        "CREATE TABLE attributes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_pt TEXT UNIQUE NOT NULL
        );",


        "CREATE TABLE attribute_values (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            attribute_id INTEGER NOT NULL,
            value_pt TEXT NOT NULL,
           price_modifier REAL NOT NULL DEFAULT 0.0, -- Price modifier for this specific value
            FOREIGN KEY (attribute_id) REFERENCES attributes(id) ON DELETE CASCADE,
            UNIQUE (attribute_id, value_pt)
        );",


        "CREATE TABLE product_variations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL,
            sku TEXT UNIQUE, -- Can be NULL if not used, but unique if present
            -- price_modifier removed from here
            stock INTEGER NOT NULL DEFAULT 0,
            weight REAL,
            dimensions TEXT, -- e.g., 10x5x2
        price_modifier_override REAL DEFAULT 0.0, -- Added price override column
            is_active INTEGER NOT NULL DEFAULT 1,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
        );",
        "CREATE INDEX idx_variations_product ON product_variations (product_id);",
        "CREATE INDEX idx_variations_sku ON product_variations (sku);",


        "CREATE TABLE variation_values (
            variation_id INTEGER NOT NULL,
            value_id INTEGER NOT NULL,
            PRIMARY KEY (variation_id, value_id),
            FOREIGN KEY (variation_id) REFERENCES product_variations(id) ON DELETE CASCADE,
            FOREIGN KEY (value_id) REFERENCES attribute_values(id) ON DELETE CASCADE
        );",


        "CREATE TABLE product_images (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL,
            filename TEXT NOT NULL, -- Corrected column name
            alt_text_pt TEXT, -- Optional alt text
            sort_order INTEGER DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            is_default INTEGER DEFAULT 0,
            variation_id TEXT,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
        );",
        "CREATE INDEX idx_product_images_product_id ON product_images (product_id);",
        "CREATE INDEX idx_product_images_sort_order ON product_images (sort_order);",


        "CREATE TABLE product_info_fields (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL,
            icon TEXT NOT NULL,
            text TEXT NOT NULL,
            sort_order INTEGER DEFAULT 0,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
        );",
        "CREATE INDEX idx_product_info_fields_product_id ON product_info_fields (product_id);",
        "CREATE INDEX idx_product_info_fields_sort_order ON product_info_fields (sort_order);",


        "CREATE TABLE product_categories (
           product_id INTEGER NOT NULL,
           category_id INTEGER NOT NULL,
           PRIMARY KEY (product_id, category_id),
           FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
           FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
       );",
       "CREATE INDEX idx_productcategories_category ON product_categories (category_id);",


        "CREATE TABLE pages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title_pt TEXT NOT NULL,
            slug TEXT UNIQUE NOT NULL,
            content_pt TEXT,
            is_system_page INTEGER NOT NULL DEFAULT 0, -- 1 for T&C, Privacy etc.
            show_in_header INTEGER NOT NULL DEFAULT 0, -- 1 to show in header
            show_in_footer INTEGER NOT NULL DEFAULT 0, -- 1 to show in footer
            require_agreement_checkout INTEGER NOT NULL DEFAULT 0, -- 1 to require agreement at checkout (Corrected name)
            is_active INTEGER NOT NULL DEFAULT 1,
            category_id INTEGER NULL, -- Added category foreign key (Nullable)
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            FOREIGN KEY (category_id) REFERENCES page_categories(id) ON DELETE SET NULL -- Added FK constraint for category_id
        );",


        "CREATE TABLE page_categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            slug TEXT UNIQUE NOT NULL,
            show_in_header INTEGER NOT NULL DEFAULT 0,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
        );",
        "CREATE INDEX idx_page_categories_slug ON page_categories (slug);",
        "CREATE INDEX idx_page_categories_show_in_header ON page_categories (show_in_header);",

        "CREATE INDEX idx_pages_slug ON pages (slug);",
        "CREATE INDEX idx_pages_active ON pages (is_active);",


        "CREATE TABLE orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_ref TEXT UNIQUE NOT NULL, -- Human-readable unique order reference
            customer_info_json TEXT NOT NULL, -- Store name, email, phone, address etc. as JSON
            total_amount REAL NOT NULL,
            currency TEXT NOT NULL,
            status TEXT NOT NULL DEFAULT 'pending', -- e.g., pending, processing, shipped, completed, cancelled, refunded
            payment_method TEXT,
            shipping_method TEXT,
            shipping_cost REAL DEFAULT 0.0,
            tracking_number TEXT,
            admin_notes TEXT,
            terms_log_json TEXT, -- Store accepted terms version, timestamp, IP as JSON
            is_anonymized INTEGER NOT NULL DEFAULT 0, -- 0 = no, 1 = yes
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            customer_name TEXT,
            customer_email TEXT,
            order_date TEXT,
            coupon_code TEXT,
            discount_amount REAL DEFAULT 0.0,
            tax_amount REAL DEFAULT 0.0,
            tracking_url TEXT,
            shipping_method_id INTEGER,
            shipping_method_details TEXT,
            vat_details_json TEXT,
            tax_details_json TEXT,
            has_digital_products INTEGER DEFAULT 0
        );",
        "CREATE INDEX idx_orders_status ON orders (status);",
        "CREATE INDEX idx_orders_created ON orders (created_at);",


        "CREATE TABLE order_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            variation_id INTEGER, -- Link to the specific variation purchased
            product_id INTEGER NOT NULL, -- Store product ID for reference even if variation deleted
            quantity INTEGER NOT NULL,
            price_at_purchase REAL NOT NULL, -- Price per item at the time of order
            product_details_json TEXT, -- Snapshot of product name, SKU, attributes at time of order
            vat_rate REAL NOT NULL DEFAULT 0.0,
            vat_amount REAL NOT NULL DEFAULT 0.0,
            stock_reduced INTEGER NOT NULL DEFAULT 0,
            stock_restored INTEGER NOT NULL DEFAULT 0,
            FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
            FOREIGN KEY (variation_id) REFERENCES product_variations(id) ON DELETE SET NULL, -- Keep item record if variation deleted
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT -- Prevent product deletion if orders exist? Or SET NULL? RESTRICT is safer.
        );",
        "CREATE INDEX idx_orderitems_order ON order_items (order_id);",


        "CREATE TABLE contacts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT NOT NULL,
            phone TEXT, -- Added phone column
            subject TEXT,
            message TEXT NOT NULL,
            product_ref TEXT, -- Added product_ref column
            status TEXT NOT NULL DEFAULT 'new', -- e.g., new, read, replied, archived
            ip_address TEXT,
            user_agent TEXT,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            reply_message TEXT,
            replied_at TEXT
        );",
        "CREATE INDEX idx_contacts_status ON contacts (status);",
        "CREATE INDEX idx_contacts_created ON contacts (created_at);",

        "CREATE TABLE message_replies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            message_id INTEGER NOT NULL, -- Link to the original message
            admin_user_id INTEGER,       -- ID of the admin user who replied (if logged in, optional)
            reply_body TEXT NOT NULL,
            sent_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            FOREIGN KEY (message_id) REFERENCES contacts(id) ON DELETE CASCADE -- Corrected table name
            -- Optional: FOREIGN KEY (admin_user_id) REFERENCES users(id) ON DELETE SET NULL -- If you have an admin users table
        );",
        "CREATE INDEX idx_messagereplies_message ON message_replies (message_id);",




        "INSERT INTO settings (setting_key, setting_value) VALUES
            ('store_name', 'Minha Loja Simples'),
            ('admin_email', '<EMAIL>'),
            ('admin_password_hash', ''), -- IMPORTANT: Needs secure setup!
            ('default_currency', 'EUR'),
            ('currency_symbol', '€'),
            ('smtp_host', 'mail.joaocesarsilva.com'),
            ('smtp_username', '<EMAIL>'),
            ('smtp_password', ''), -- IMPORTANT: Needs secure setup!
            ('smtp_port', '587'),
            ('smtp_secure', 'tls'),
            ('from_email', '<EMAIL>'),
            ('from_name', 'Minha Loja Simples'),
            ('reply_to_email', '<EMAIL>'),
            ('reply_to_name', 'Info Minha Loja Simples'),
            ('watermark_enabled', '0'), -- 0 = disabled, 1 = enabled
            ('watermark_opacity', '0.5'), -- Opacity as float 0.0-1.0
            ('watermark_position', 'bottom-right'), -- Position key
            ('watermark_size_percent', '15'), -- Watermark width as % of target width
            ('contact_address', ''), -- Added default contact address
            ('contact_phone', ''), -- Added default contact phone
            ('contact_email', ''), -- Added default contact email
            ('store_description', 'Descubra nossa coleção de itens premium.'), -- Added default store description
            ('social_facebook', ''), -- Added default social links
            ('social_twitter', ''),
            ('social_instagram', ''),
            ('social_linkedin', ''),
            ('social_youtube', ''),
            ('store_logo_path', ''), -- Added store logo path setting
            ('items_per_page', '12'), -- Added default items per page
            ('vat_id_required_enabled', '1'), -- Added VAT ID required enabled setting
            ('vat_id_threshold', '1000'), -- Added VAT ID threshold setting
            ('blog_posts_per_page', '5'), -- Added default blog posts per page setting
            -- Add more default settings as needed
        ",


        "INSERT INTO pages (title_pt, slug, content_pt, is_system_page, is_active) VALUES
            ('Termos e Condições', 'termos-e-condicoes', '<p>Por favor, adicione aqui os seus Termos e Condições.</p>', 1, 1);",


        "INSERT INTO pages (title_pt, slug, content_pt, is_system_page, is_active) VALUES
            ('Política de Privacidade', 'politica-de-privacidade', '<p>Por favor, adicione aqui a sua Política de Privacidade.</p>', 1, 1);",


        "CREATE TABLE digital_files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            original_filename TEXT NOT NULL,
            file_path TEXT NOT NULL,
            file_size INTEGER NOT NULL,
            file_type TEXT,
            description TEXT,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            short_name TEXT,
            display_name TEXT
        );",


        "CREATE TABLE digital_products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL UNIQUE,
            digital_file_id INTEGER,
            expiry_days INTEGER DEFAULT 5,
            download_limit INTEGER DEFAULT 3,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            df TEXT,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            FOREIGN KEY (digital_file_id) REFERENCES digital_files(id) ON DELETE SET NULL
        );",


        "CREATE TABLE digital_files_file_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            extension TEXT NOT NULL UNIQUE,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
        );",


        "CREATE TABLE digital_file_type_associations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            digital_file_id INTEGER NOT NULL,
            file_type_id INTEGER NOT NULL,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            FOREIGN KEY (digital_file_id) REFERENCES digital_files(id) ON DELETE CASCADE,
            FOREIGN KEY (file_type_id) REFERENCES digital_files_file_types(id) ON DELETE CASCADE,
            UNIQUE (digital_file_id, file_type_id)
        );",


        "CREATE TABLE licenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            license_code TEXT NOT NULL UNIQUE,
            order_id INTEGER,
            order_item_id INTEGER,
            customer_name TEXT NOT NULL,
            customer_email TEXT NOT NULL,
            status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('active', 'disabled', 'canceled', 'waiting_payment')),
            expiry_date TEXT,
            download_limit INTEGER NOT NULL DEFAULT 3,
            downloads_used INTEGER NOT NULL DEFAULT 0,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            is_encrypted INTEGER NOT NULL DEFAULT 0,
            FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL,
            FOREIGN KEY (order_item_id) REFERENCES order_items(id) ON DELETE SET NULL
        );",
        "CREATE INDEX idx_licenses_license_code ON licenses (license_code);",
        "CREATE INDEX idx_licenses_order_id ON licenses (order_id);",
        "CREATE INDEX idx_licenses_customer_email ON licenses (customer_email);",


        "CREATE TABLE license_files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            license_id INTEGER NOT NULL,
            digital_product_id INTEGER NOT NULL,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE,
            FOREIGN KEY (digital_product_id) REFERENCES digital_products(id) ON DELETE CASCADE
        );",


        "CREATE TABLE downloads (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            license_id INTEGER NOT NULL,
            ip_address TEXT NOT NULL,
            user_agent TEXT,
            download_date TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE
        );",
        "CREATE INDEX idx_downloads_license_id ON downloads (license_id);",


        "CREATE TABLE download_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            license_id INTEGER NOT NULL,
            security_token TEXT NOT NULL,
            email_token TEXT NOT NULL UNIQUE,
            is_verified INTEGER NOT NULL DEFAULT 0,
            attempts INTEGER NOT NULL DEFAULT 0,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            expires_at TEXT NOT NULL,
            session_id TEXT,
            is_used TEXT,
            FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE
        );",
        "CREATE INDEX idx_download_tokens_license_id ON download_tokens (license_id);",
        "CREATE INDEX idx_download_tokens_security_token ON download_tokens (security_token);",
        "CREATE INDEX idx_download_tokens_email_token ON download_tokens (email_token);",


        "CREATE TABLE order_access_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            access_token TEXT NOT NULL,
            created_at TEXT NOT NULL,
            expires_at TEXT NOT NULL,
            UNIQUE(order_id, access_token)
        );",
        "CREATE INDEX idx_order_access_tokens_order_id ON order_access_tokens(order_id);",
        "CREATE INDEX idx_order_access_tokens_access_token ON order_access_tokens(access_token);",


        "CREATE TABLE order_visits (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            access_token TEXT NOT NULL,
            session_id TEXT NOT NULL,
            visit_time TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            is_first_visit INTEGER NOT NULL DEFAULT 0,
            FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
        );",
        "CREATE INDEX idx_order_visits_order_id ON order_visits(order_id);",
        "CREATE INDEX idx_order_visits_access_token ON order_visits(access_token);",
        "CREATE INDEX idx_order_visits_session_id ON order_visits(session_id);",


        "CREATE TABLE blog_categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            slug TEXT UNIQUE NOT NULL,
            description TEXT,
            is_active INTEGER NOT NULL DEFAULT 1,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
        );",
        "CREATE INDEX idx_blog_categories_slug ON blog_categories (slug);",
        "CREATE INDEX idx_blog_categories_active ON blog_categories (is_active);",


        "CREATE TABLE blog_posts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            slug TEXT UNIQUE NOT NULL,
            post_type TEXT NOT NULL CHECK(post_type IN ('article', 'link')),
            content TEXT,
            link_url TEXT,
            link_description TEXT,
            image_path TEXT NOT NULL,
            is_published INTEGER NOT NULL DEFAULT 0,
            published_at TEXT,
            seo_title TEXT,
            seo_description TEXT,
            seo_keywords TEXT,
            og_title TEXT,
            og_description TEXT,
            og_image TEXT,
            twitter_card TEXT,
            twitter_title TEXT,
            twitter_description TEXT,
            twitter_image TEXT,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            image_description TEXT
        );",
        "CREATE INDEX idx_blog_posts_slug ON blog_posts (slug);",
        "CREATE INDEX idx_blog_posts_published ON blog_posts (is_published, published_at);",
        "CREATE INDEX idx_blog_posts_type ON blog_posts (post_type);",


        "CREATE TABLE blog_post_categories (
            post_id INTEGER NOT NULL,
            category_id INTEGER NOT NULL,
            PRIMARY KEY (post_id, category_id),
            FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
            FOREIGN KEY (category_id) REFERENCES blog_categories(id) ON DELETE CASCADE
        );",
        "CREATE INDEX idx_blogpostcategories_category ON blog_post_categories (category_id);",
        "CREATE INDEX idx_blogpostcategories_post ON blog_post_categories (post_id);",


        "CREATE TABLE sitemap_configs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            type TEXT NOT NULL CHECK(type IN ('sitemap', 'google_merchant', 'custom')),
            output_path TEXT NOT NULL,
            include_products INTEGER NOT NULL DEFAULT 1,
            include_blog INTEGER NOT NULL DEFAULT 1,
            include_pages INTEGER NOT NULL DEFAULT 1,
            custom_config_json TEXT,
            is_active INTEGER NOT NULL DEFAULT 1,
            include_regular_products INTEGER NOT NULL DEFAULT 1,
            include_variation_products INTEGER NOT NULL DEFAULT 1,
            include_digital_products INTEGER NOT NULL DEFAULT 1,
            last_generated TEXT,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
        );"
    ];

    try {

        $pdo->beginTransaction();
        foreach ($sql_commands as $sql) {
            $pdo->exec($sql);
        }
        $pdo->commit();

    } catch (PDOException $e) {
        $pdo->rollBack();


        die("Erro crítico: Falha ao inicializar a estrutura da base de dados.");
    }
}

function check_and_update_pages_schema(PDO $pdo): void
{
    try {

        $stmt = $pdo->query("SELECT 1 FROM pages LIMIT 1;");
        $stmt->execute();



        $stmt = $pdo->query("PRAGMA table_info(pages);");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);

        if (!in_array('description', $columns)) {
            $pdo->exec("ALTER TABLE pages ADD COLUMN description TEXT;");
        }


        if (!in_array('require_agreement_digital_checkout', $columns)) {
            $pdo->exec("ALTER TABLE pages ADD COLUMN require_agreement_digital_checkout INTEGER NOT NULL DEFAULT 0;");
        }

    } catch (PDOException $e) {

    }
}

function check_and_create_product_info_fields_table(PDO $pdo): void
{
    try {

        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='product_info_fields';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE product_info_fields (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                icon TEXT NOT NULL,
                text TEXT NOT NULL,
                sort_order INTEGER DEFAULT 0,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
            );");

            $pdo->exec("CREATE INDEX idx_product_info_fields_product_id ON product_info_fields (product_id);");
            $pdo->exec("CREATE INDEX idx_product_info_fields_sort_order ON product_info_fields (sort_order);");
        }
    } catch (PDOException $e) {

    }
}

function check_and_update_contacts_schema(PDO $pdo): void
{
    try {

        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='contacts';");
        $table_exists = $stmt->fetch();

        if ($table_exists) {

            $stmt = $pdo->query("PRAGMA table_info(contacts);");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);


            if (!in_array('phone', $columns)) {
                $pdo->exec("ALTER TABLE contacts ADD COLUMN phone TEXT;");
            }


            if (!in_array('product_ref', $columns)) {
                $pdo->exec("ALTER TABLE contacts ADD COLUMN product_ref TEXT;");
            }


            if (!in_array('order_id', $columns)) {
                $pdo->exec("ALTER TABLE contacts ADD COLUMN order_id INTEGER;");
            }


            if (!in_array('is_from_admin', $columns)) {
                $pdo->exec("ALTER TABLE contacts ADD COLUMN is_from_admin INTEGER NOT NULL DEFAULT 0;");
            }


            $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='index' AND name='idx_contacts_order_id';");
            $index_exists = $stmt->fetch();

            if (!$index_exists) {
                $pdo->exec("CREATE INDEX idx_contacts_order_id ON contacts (order_id);");
            }
        }
    } catch (PDOException $e) {

    }
}

function check_and_update_orders_schema(PDO $pdo): void
{
    try {

        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='orders';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            return;
        }


        $stmt = $pdo->query("PRAGMA table_info(orders);");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);


        if (!in_array('coupon_code', $columns)) {
            $pdo->exec("ALTER TABLE orders ADD COLUMN coupon_code TEXT;");
        }

        if (!in_array('discount_amount', $columns)) {
            $pdo->exec("ALTER TABLE orders ADD COLUMN discount_amount REAL DEFAULT 0.0;");
        }

        if (!in_array('terms_log_json', $columns)) {
            $pdo->exec("ALTER TABLE orders ADD COLUMN terms_log_json TEXT;");
        }

        if (!in_array('is_anonymized', $columns)) {
            $pdo->exec("ALTER TABLE orders ADD COLUMN is_anonymized INTEGER NOT NULL DEFAULT 0;");
        }

        if (!in_array('tracking_url', $columns)) {
            $pdo->exec("ALTER TABLE orders ADD COLUMN tracking_url TEXT;");
        }

        if (!in_array('tax_details_json', $columns)) {
            $pdo->exec("ALTER TABLE orders ADD COLUMN tax_details_json TEXT;");
        }

        if (!in_array('tax_amount', $columns)) {
            $pdo->exec("ALTER TABLE orders ADD COLUMN tax_amount REAL DEFAULT 0.0;");
        }

    } catch (PDOException $e) {

    }
}

function check_and_create_page_placeholders_table(PDO $pdo): void
{
    try {

        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='page_placeholders';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE page_placeholders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                slug TEXT UNIQUE NOT NULL,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
            );");

            $pdo->exec("CREATE INDEX idx_page_placeholders_slug ON page_placeholders (slug);");


            $pdo->exec("INSERT INTO page_placeholders (name, slug) VALUES
                ('Links Úteis', 'links-uteis'),
                ('Apoio ao Cliente', 'apoio-ao-cliente')
            ");
        }


        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='placeholder_links';");
        $links_table_exists = $stmt->fetch();

        if (!$links_table_exists) {

            $pdo->exec("CREATE TABLE placeholder_links (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                url TEXT NOT NULL,
                target TEXT NOT NULL DEFAULT '_blank' CHECK(target IN ('_blank', '_self')),
                placeholder_id INTEGER NOT NULL,
                sort_order INTEGER DEFAULT 0,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                FOREIGN KEY (placeholder_id) REFERENCES page_placeholders(id) ON DELETE CASCADE
            );");

            $pdo->exec("CREATE INDEX idx_placeholder_links_placeholder_id ON placeholder_links (placeholder_id);");
            $pdo->exec("CREATE INDEX idx_placeholder_links_sort_order ON placeholder_links (sort_order);");
        }


        $stmt = $pdo->query("PRAGMA table_info(pages);");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);

        if (!in_array('placeholder_id', $columns)) {
            $pdo->exec("ALTER TABLE pages ADD COLUMN placeholder_id INTEGER DEFAULT NULL;");
            $pdo->exec("CREATE INDEX idx_pages_placeholder_id ON pages (placeholder_id);");


            $links_uteis_id = $pdo->query("SELECT id FROM page_placeholders WHERE slug = 'links-uteis' LIMIT 1")->fetchColumn();
            if ($links_uteis_id) {
                $pdo->exec("UPDATE pages SET placeholder_id = {$links_uteis_id} WHERE show_in_footer = 1;");
            }
        }
    } catch (PDOException $e) {

    }
}

function check_and_create_custom_fields_tables(PDO $pdo): void
{
    try {

        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='custom_field_types';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE custom_field_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                slug TEXT UNIQUE NOT NULL,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
            );");

            $pdo->exec("CREATE INDEX idx_custom_field_types_slug ON custom_field_types (slug);");


            $pdo->exec("INSERT INTO custom_field_types (name, slug) VALUES
                ('Texto Personalizado', 'custom-text'),
                ('Upload de Ficheiro', 'file-upload')
            ");
        }


        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='custom_fields';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE custom_fields (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                field_type_id INTEGER NOT NULL,
                min_chars INTEGER DEFAULT 0,
                max_chars INTEGER DEFAULT 255,
                price_modifier REAL DEFAULT 0.0,
                is_required INTEGER NOT NULL DEFAULT 0,
                is_active INTEGER NOT NULL DEFAULT 1,
                config_json TEXT, -- Store additional configuration as JSON
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                FOREIGN KEY (field_type_id) REFERENCES custom_field_types(id) ON DELETE RESTRICT
            );");
        }


        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='custom_field_fonts';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE custom_field_fonts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                file_path TEXT, -- Path to uploaded font file (if any)
                google_font_name TEXT, -- Google Font name (if using Google Fonts)
                is_active INTEGER NOT NULL DEFAULT 1,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
            );");
        }


        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='product_custom_fields';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE product_custom_fields (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                custom_field_id INTEGER NOT NULL,
                sort_order INTEGER DEFAULT 0,
                price_modifier_override REAL DEFAULT NULL, -- Override the price modifier from custom_fields
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
                FOREIGN KEY (custom_field_id) REFERENCES custom_fields(id) ON DELETE CASCADE,
                UNIQUE (product_id, custom_field_id)
            );");

            $pdo->exec("CREATE INDEX idx_product_custom_fields_product_id ON product_custom_fields (product_id);");
            $pdo->exec("CREATE INDEX idx_product_custom_fields_sort_order ON product_custom_fields (sort_order);");
        }


        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_item_custom_fields';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE order_item_custom_fields (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_item_id INTEGER NOT NULL,
                custom_field_id INTEGER NOT NULL,
                field_name TEXT NOT NULL, -- Store the name at time of order
                field_value TEXT, -- For text fields
                file_path TEXT, -- For file uploads
                font_id INTEGER, -- For text fields with font selection
                price_modifier REAL DEFAULT 0.0, -- Store the price modifier at time of order
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                FOREIGN KEY (order_item_id) REFERENCES order_items(id) ON DELETE CASCADE,
                FOREIGN KEY (custom_field_id) REFERENCES custom_fields(id) ON DELETE SET NULL,
                FOREIGN KEY (font_id) REFERENCES custom_field_fonts(id) ON DELETE SET NULL
            );");

            $pdo->exec("CREATE INDEX idx_order_item_custom_fields_order_item_id ON order_item_custom_fields (order_item_id);");
        }

    } catch (PDOException $e) {

    }
}

function check_and_create_product_videos_table(PDO $pdo): void
{
    try {

        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='product_videos';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE product_videos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                filename TEXT,
                video_url TEXT,
                video_type TEXT NOT NULL CHECK(video_type IN ('uploaded', 'external')),
                thumbnail_filename TEXT,
                sort_order INTEGER DEFAULT 0,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                variation_id INTEGER,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
                FOREIGN KEY (variation_id) REFERENCES product_variations(id) ON DELETE SET NULL
            );");

            $pdo->exec("CREATE INDEX idx_product_videos_product_id ON product_videos (product_id);");
            $pdo->exec("CREATE INDEX idx_product_videos_sort_order ON product_videos (sort_order);");
        }
    } catch (PDOException $e) {

    }
}

function check_and_update_products_seo_schema(PDO $pdo): void
{
    try {

        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='products';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            return;
        }


        $stmt = $pdo->query("PRAGMA table_info(products);");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);


        $seo_columns = [
            'seo_title' => 'TEXT',
            'seo_description' => 'TEXT',
            'seo_keywords' => 'TEXT',
            'og_title' => 'TEXT',
            'og_description' => 'TEXT',
            'og_image' => 'TEXT',
            'twitter_card' => 'TEXT',
            'twitter_title' => 'TEXT',
            'twitter_description' => 'TEXT',
            'twitter_image' => 'TEXT'
        ];

        foreach ($seo_columns as $column => $type) {
            if (!in_array($column, $columns)) {
                $pdo->exec("ALTER TABLE products ADD COLUMN {$column} {$type};");
            }
        }

    } catch (PDOException $e) {

    }
}

function check_and_create_sitemap_configs_table(PDO $pdo): void
{
    try {

        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='sitemap_configs';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE sitemap_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                type TEXT NOT NULL CHECK(type IN ('sitemap', 'google_merchant', 'custom')),
                output_path TEXT NOT NULL,
                include_products INTEGER NOT NULL DEFAULT 1,
                include_blog INTEGER NOT NULL DEFAULT 1,
                include_pages INTEGER NOT NULL DEFAULT 1,
                custom_config_json TEXT,
                is_active INTEGER NOT NULL DEFAULT 1,
                include_regular_products INTEGER NOT NULL DEFAULT 1,
                include_variation_products INTEGER NOT NULL DEFAULT 1,
                include_digital_products INTEGER NOT NULL DEFAULT 1,
                last_generated TEXT,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
            );");
        } else {

            $stmt = $pdo->query("PRAGMA table_info(sitemap_configs);");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);


            if (!in_array('include_regular_products', $columns)) {
                $pdo->exec("ALTER TABLE sitemap_configs ADD COLUMN include_regular_products INTEGER NOT NULL DEFAULT 1;");
            }

            if (!in_array('include_variation_products', $columns)) {
                $pdo->exec("ALTER TABLE sitemap_configs ADD COLUMN include_variation_products INTEGER NOT NULL DEFAULT 1;");
            }

            if (!in_array('include_digital_products', $columns)) {
                $pdo->exec("ALTER TABLE sitemap_configs ADD COLUMN include_digital_products INTEGER NOT NULL DEFAULT 1;");
            }
        }


        $stmt = $pdo->prepare("SELECT 1 FROM settings WHERE setting_key = ?");
        $stmt->execute(['sitemap_default_directory']);
        if (!$stmt->fetch()) {
            $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)")
                ->execute(['sitemap_default_directory', '']);
        }


        $contact_settings = [
            'contact_whatsapp' => '',
            'contact_signal' => '',
            'contact_map_latitude' => '38.7223',
            'contact_map_longitude' => '-9.1393',
            'contact_map_zoom' => '15'
        ];

        foreach ($contact_settings as $key => $default_value) {
            $stmt = $pdo->prepare("SELECT 1 FROM settings WHERE setting_key = ?");
            $stmt->execute([$key]);
            if (!$stmt->fetch()) {
                $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)")
                    ->execute([$key, $default_value]);
            }
        }

    } catch (PDOException $e) {

    }
}

function check_and_create_digital_products_tables(PDO $pdo): void
{
    try {

        $stmt = $pdo->query("PRAGMA table_info(products);");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);

        if (!in_array('product_type', $columns)) {
            $pdo->exec("ALTER TABLE products ADD COLUMN product_type TEXT NOT NULL DEFAULT 'regular' CHECK(product_type IN ('regular', 'variation', 'digital'));");
        }


        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_products';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE digital_products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL UNIQUE,
                digital_file_id INTEGER,
                expiry_days INTEGER DEFAULT 5,
                download_limit INTEGER DEFAULT 3,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                df TEXT,
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
                FOREIGN KEY (digital_file_id) REFERENCES digital_files(id) ON DELETE SET NULL
            );");

            $pdo->exec("CREATE INDEX idx_digital_products_product_id ON digital_products (product_id);");
        }


        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_files_file_types';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE digital_files_file_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                extension TEXT NOT NULL UNIQUE,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
            );");


            $pdo->exec("INSERT INTO digital_files_file_types (name, extension) VALUES
                ('Adobe Affinity Designer', '.afdesign'),
                ('Scalable Vector Graphics', '.svg'),
                ('JPEG Image', '.jpg'),
                ('PNG Image', '.png'),
                ('Encapsulated PostScript', '.eps'),
                ('CorelDRAW', '.cdr'),
                ('Serif Draw Plus', '.dpp'),
                ('Adobe Photoshop', '.psd')
            ");
        }


        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_file_type_associations';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE digital_file_type_associations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                digital_file_id INTEGER NOT NULL,
                file_type_id INTEGER NOT NULL,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                FOREIGN KEY (digital_file_id) REFERENCES digital_files(id) ON DELETE CASCADE,
                FOREIGN KEY (file_type_id) REFERENCES digital_files_file_types(id) ON DELETE CASCADE,
                UNIQUE (digital_file_id, file_type_id)
            );");
        }


        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='licenses';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE licenses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_code TEXT NOT NULL UNIQUE,
                order_id INTEGER,
                order_item_id INTEGER,
                customer_name TEXT NOT NULL,
                customer_email TEXT NOT NULL,
                status TEXT NOT NULL DEFAULT 'active' CHECK(status IN ('active', 'disabled', 'canceled', 'waiting_payment')),
                expiry_date TEXT,
                download_limit INTEGER NOT NULL DEFAULT 3,
                downloads_used INTEGER NOT NULL DEFAULT 0,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                is_encrypted INTEGER NOT NULL DEFAULT 0,
                FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL,
                FOREIGN KEY (order_item_id) REFERENCES order_items(id) ON DELETE SET NULL
            );");

            $pdo->exec("CREATE INDEX idx_licenses_license_code ON licenses (license_code);");
            $pdo->exec("CREATE INDEX idx_licenses_order_id ON licenses (order_id);");
            $pdo->exec("CREATE INDEX idx_licenses_customer_email ON licenses (customer_email);");
        }


        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='license_files';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE license_files (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_id INTEGER NOT NULL,
                digital_product_id INTEGER NOT NULL,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE,
                FOREIGN KEY (digital_product_id) REFERENCES digital_products(id) ON DELETE CASCADE
            );");
        }


        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='downloads';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE downloads (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_id INTEGER NOT NULL,
                ip_address TEXT NOT NULL,
                user_agent TEXT,
                download_date TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE
            );");

            $pdo->exec("CREATE INDEX idx_downloads_license_id ON downloads (license_id);");
        }


        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='download_tokens';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE download_tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_id INTEGER NOT NULL,
                security_token TEXT NOT NULL,
                email_token TEXT NOT NULL UNIQUE,
                is_verified INTEGER NOT NULL DEFAULT 0,
                attempts INTEGER NOT NULL DEFAULT 0,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                expires_at TEXT NOT NULL,
                session_id TEXT,
                is_used TEXT,
                FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE
            );");

            $pdo->exec("CREATE INDEX idx_download_tokens_license_id ON download_tokens (license_id);");
            $pdo->exec("CREATE INDEX idx_download_tokens_security_token ON download_tokens (security_token);");
            $pdo->exec("CREATE INDEX idx_download_tokens_email_token ON download_tokens (email_token);");
        }


        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_access_tokens';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE order_access_tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                access_token TEXT NOT NULL,
                created_at TEXT NOT NULL,
                expires_at TEXT NOT NULL,
                UNIQUE(order_id, access_token)
            );");

            $pdo->exec("CREATE INDEX idx_order_access_tokens_order_id ON order_access_tokens(order_id);");
            $pdo->exec("CREATE INDEX idx_order_access_tokens_access_token ON order_access_tokens(access_token);");
        }


        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_visits';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {

            $pdo->exec("CREATE TABLE order_visits (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                access_token TEXT NOT NULL,
                session_id TEXT NOT NULL,
                visit_time TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                is_first_visit INTEGER NOT NULL DEFAULT 0,
                FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
            );");

            $pdo->exec("CREATE INDEX idx_order_visits_order_id ON order_visits(order_id);");
            $pdo->exec("CREATE INDEX idx_order_visits_access_token ON order_visits(access_token);");
            $pdo->exec("CREATE INDEX idx_order_visits_session_id ON order_visits(session_id);");
        }


        $digital_settings = [
            'digital_products_directory' => '../digital_products',
            'digital_license_text' => 'Este ficheiro está licenciado apenas para uso pessoal e uso comercial limitado. A revenda ou distribuição não é permitida sem autorização expressa do autor, mesmo que este produto tenha sido obtido de forma gratuita.',
            'digital_checkout_agreement_page_id' => '0',
            'digital_download_expiry_days' => '5',
            'digital_download_limit' => '3'
        ];

        foreach ($digital_settings as $key => $default_value) {
            $stmt = $pdo->prepare("SELECT 1 FROM settings WHERE setting_key = ?");
            $stmt->execute([$key]);
            if (!$stmt->fetch()) {
                $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)")
                    ->execute([$key, $default_value]);
            }
        }

    } catch (PDOException $e) {

    }
}

function check_and_create_blog_tables(PDO $pdo): void
{
    try {

        $pdo->exec("CREATE TABLE IF NOT EXISTS blog_categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            slug TEXT UNIQUE NOT NULL,
            description TEXT,
            is_active INTEGER NOT NULL DEFAULT 1,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
        );");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_blog_categories_slug ON blog_categories (slug);");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_blog_categories_active ON blog_categories (is_active);");


        $stmt = $pdo->query("SELECT COUNT(*) FROM blog_categories");
        if ($stmt->fetchColumn() == 0) {
            $pdo->exec("INSERT INTO blog_categories (name, slug) VALUES
                ('Artigos', 'artigos'),
                ('Links', 'links'),
                ('Anúncios', 'anuncios'),
                ('Notícias', 'noticias')
            ");
        }


        $pdo->exec("CREATE TABLE IF NOT EXISTS blog_posts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            slug TEXT UNIQUE NOT NULL,
            post_type TEXT NOT NULL CHECK(post_type IN ('article', 'link')), -- Type of post
            content TEXT, -- Full content for 'article'
            link_url TEXT, -- URL for 'link' type
            link_description TEXT, -- Short description for 'link' type
            image_path TEXT NOT NULL, -- Required image path
            is_published INTEGER NOT NULL DEFAULT 0, -- 0=draft, 1=published
            published_at TEXT, -- Timestamp when published
            seo_title TEXT,
            seo_description TEXT,
            seo_keywords TEXT,
            og_title TEXT,
            og_description TEXT,
            og_image TEXT,
            twitter_card TEXT,
            twitter_title TEXT,
            twitter_description TEXT,
            twitter_image TEXT,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
        );");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_blog_posts_slug ON blog_posts (slug);");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_blog_posts_published ON blog_posts (is_published, published_at);");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_blog_posts_type ON blog_posts (post_type);");


        $pdo->exec("CREATE TABLE IF NOT EXISTS blog_post_categories (
            post_id INTEGER NOT NULL,
            category_id INTEGER NOT NULL,
            PRIMARY KEY (post_id, category_id),
            FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
            FOREIGN KEY (category_id) REFERENCES blog_categories(id) ON DELETE CASCADE
        );");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_blogpostcategories_category ON blog_post_categories (category_id);");
        $pdo->exec("CREATE INDEX IF NOT EXISTS idx_blogpostcategories_post ON blog_post_categories (post_id);");


        $stmt = $pdo->prepare("SELECT 1 FROM settings WHERE setting_key = ?");
        $stmt->execute(['blog_posts_per_page']);
        if (!$stmt->fetch()) {
            $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)")
                ->execute(['blog_posts_per_page', '5']);
        }


        $stmt = $pdo->prepare("SELECT 1 FROM settings WHERE setting_key = ?");
        $stmt->execute(['blog_slider_count']);
        if (!$stmt->fetch()) {
            $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)")
                ->execute(['blog_slider_count', '3']);
        }


        $stmt = $pdo->prepare("SELECT 1 FROM settings WHERE setting_key = ?");
        $stmt->execute(['blog_slider_delay']);
        if (!$stmt->fetch()) {
            $pdo->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)")
                ->execute(['blog_slider_delay', '5']);
        }

    } catch (PDOException $e) {

    }
}

/**
 * Checks if a migration has already been applied by looking it up in the 'migrations' table.
 *
 * @param PDO $pdo The PDO database connection object.
 * @param string $migration_name The unique name of the migration.
 * @return bool True if the migration has been applied, false otherwise.
 */
function is_migration_applied(PDO $pdo, string $migration_name): bool
{
    try {
        $stmt = $pdo->prepare("SELECT 1 FROM migrations WHERE name = :name LIMIT 1");
        $stmt->execute([':name' => $migration_name]);
        return (bool) $stmt->fetchColumn();
    } catch (PDOException $e) {
        // If the migrations table doesn't exist yet, or other error, assume not applied.
        // The table should be created before this is called in earnest.
        error_log("Error checking migration status for '$migration_name': " . $e->getMessage());
        return false;
    }
}

/**
 * Logs that a migration has been applied by inserting its name into the 'migrations' table.
 *
 * @param PDO $pdo The PDO database connection object.
 * @param string $migration_name The unique name of the migration.
 * @return void
 */
function log_migration_applied(PDO $pdo, string $migration_name): void
{
    try {
        $stmt = $pdo->prepare("INSERT INTO migrations (name, executed_at) VALUES (:name, datetime('now', 'localtime'))");
        $stmt->execute([':name' => $migration_name]);
    } catch (PDOException $e) {
        error_log("Error logging migration '$migration_name' as applied: " . $e->getMessage());
        // Depending on how critical this is, you might throw the exception
        // or handle it (e.g., if it's a unique constraint violation meaning it was already logged)
    }
}

function db_query(string $sql, array $params = [], bool $fetch_one = false, bool $fetch_all = false): mixed
{

    if (strpos($sql, "SELECT * FROM payment_methods") === 0) {
    }

    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }


    if (stripos(trim($sql), 'INSERT INTO contacts') === 0) {
        try {

            $stmt = $pdo->query("PRAGMA table_info(contacts);");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);


            if (!in_array('phone', $columns)) {
                $pdo->exec("ALTER TABLE contacts ADD COLUMN phone TEXT;");
            }

            if (!in_array('product_ref', $columns)) {
                $pdo->exec("ALTER TABLE contacts ADD COLUMN product_ref TEXT;");
            }
        } catch (PDOException $e) {
        }
    }


    $problematic_sql_signature = 'SELECT pv.id as variation_id';

    try {
        $stmt = $pdo->prepare($sql);


        if (strpos(trim($sql), $problematic_sql_signature) === 0) {
        }

        $success = $stmt->execute($params);


         if (strpos(trim($sql), $problematic_sql_signature) === 0) {
         }

        if (!$success) {

            $errorInfo = $stmt->errorInfo();
            return false;
        }


        if ($fetch_one) {

            $result = $stmt->fetch();
            if (strpos($sql, "SELECT * FROM payment_methods") === 0) {
            }
            return $result;
        } elseif ($fetch_all) {

            $result = $stmt->fetchAll();
            if (strpos($sql, "SELECT * FROM payment_methods") === 0) {
            }
            return $result;
        } elseif (stripos(trim($sql), 'INSERT') === 0 || stripos(trim($sql), 'UPDATE') === 0 || stripos(trim($sql), 'DELETE') === 0) {


            return $stmt->rowCount();
        } else {

            return $stmt;
        }
    } catch (PDOException $e) {


        if (stripos($e->getMessage(), 'no such column') !== false) {

            if (preg_match('/no such column: ([\w_]+)/i', $e->getMessage(), $matches)) {
                $missing_column = $matches[1];
                $table_name = '';


                if (preg_match('/INSERT INTO\s+([\w_]+)/i', $sql, $table_matches)) {
                    $table_name = $table_matches[1];
                } elseif (preg_match('/UPDATE\s+([\w_]+)/i', $sql, $table_matches)) {
                    $table_name = $table_matches[1];
                } elseif (preg_match('/FROM\s+([\w_]+)/i', $sql, $table_matches)) {
                    $table_name = $table_matches[1];
                }

                if (!empty($table_name)) {
                    try {

                        $pdo->exec("ALTER TABLE {$table_name} ADD COLUMN {$missing_column} TEXT;");


                        $stmt = $pdo->prepare($sql);
                        $success = $stmt->execute($params);

                        if ($success) {

                            if ($fetch_one) {
                                return $stmt->fetch();
                            } elseif ($fetch_all) {
                                return $stmt->fetchAll();
                            } elseif (stripos(trim($sql), 'INSERT') === 0 || stripos(trim($sql), 'UPDATE') === 0 || stripos(trim($sql), 'DELETE') === 0) {
                                return $stmt->rowCount();
                            } else {
                                return $stmt;
                            }
                        } else {
                             $errorInfo = $stmt->errorInfo();
                             return false;
                        }
                    } catch (PDOException $retry_e) {
                         return false;
                    }
                } else {
                }
            }
        }


        return false;
    }
}

?>
