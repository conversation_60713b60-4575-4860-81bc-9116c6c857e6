<?php

function recreate_digital_product_file_type_associations_table(PDO $pdo): void
{
    try {
        
        
        
        $fk_stmt = $pdo->query("PRAGMA foreign_keys;");
        $fk_enabled_before = (bool) $fk_stmt->fetchColumn();
        if ($fk_enabled_before) {
            $pdo->exec("PRAGMA foreign_keys = OFF;");
        }

        
        $pdo->exec("DROP TABLE IF EXISTS digital_product_file_type_associations;");

        
        
        $pdo->exec("CREATE TABLE digital_product_file_type_associations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            digital_product_id INTEGER NOT NULL,
            file_type_id INTEGER NOT NULL,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            FOREIGN KEY (digital_product_id) REFERENCES digital_products(id) ON DELETE CASCADE,
            FOREIGN KEY (file_type_id) REFERENCES digital_product_file_types(id) ON DELETE CASCADE,
            UNIQUE (digital_product_id, file_type_id)
        );");

        
        if ($fk_enabled_before) {
            $pdo->exec("PRAGMA foreign_keys = ON;");
        }

    } catch (PDOException $e) {
        
        try {
            if (isset($fk_enabled_before) && $fk_enabled_before) {
                 $pdo->exec("PRAGMA foreign_keys = ON;");
            }
        } catch (Exception $fk_e) {
        }
    }
}
?>