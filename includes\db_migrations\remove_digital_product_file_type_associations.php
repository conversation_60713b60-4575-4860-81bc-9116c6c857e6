<?php

/**
 * Migration to remove the incorrect digital_product_file_type_associations table
 * We'll use only the digital_file_type_associations table going forward
 */
function migrate_remove_digital_product_file_type_associations()
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        // Check if the table exists
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_product_file_type_associations';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {
            // Table doesn't exist, migration already applied
            error_log("Migration already applied: digital_product_file_type_associations table doesn't exist");
            return true;
        }

        // Start transaction
        $pdo->beginTransaction();

        // First, migrate any data that might be in digital_product_file_type_associations but not in digital_file_type_associations
        $sql = "INSERT OR IGNORE INTO digital_file_type_associations (digital_file_id, file_type_id, created_at)
                SELECT dp.digital_file_id, dpfta.file_type_id, dpfta.created_at
                FROM digital_product_file_type_associations dpfta
                JOIN digital_products dp ON dpfta.digital_product_id = dp.id
                WHERE EXISTS (SELECT 1 FROM digital_products WHERE id = dpfta.digital_product_id)
                AND EXISTS (SELECT 1 FROM digital_files WHERE id = dp.digital_file_id)";
        
        $pdo->exec($sql);
        
        // Drop the table
        $pdo->exec("DROP TABLE IF EXISTS digital_product_file_type_associations;");

        // Drop related indexes
        $pdo->exec("DROP INDEX IF EXISTS idx_digital_product_file_type_associations_product_id;");
        $pdo->exec("DROP INDEX IF EXISTS idx_digital_product_file_type_associations_type_id;");

        // Record the migration
        $stmt = $pdo->prepare("INSERT INTO migrations (name, executed_at) VALUES (:name, datetime('now', 'localtime'))");
        $stmt->execute([':name' => 'remove_digital_product_file_type_associations']);

        // Commit transaction
        $pdo->commit();
        
        error_log("Successfully removed digital_product_file_type_associations table");
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Error removing digital_product_file_type_associations table: " . $e->getMessage());
        return false;
    }
}
