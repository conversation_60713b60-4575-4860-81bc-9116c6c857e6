// VAT Rates Management JavaScript

// Track initialization state to prevent duplicate event handlers
window.vatRateManagementInitialized = false;

// Define a global initialization function that can be called when the settings page is loaded
window.initVatRateManagement = function() {
    // Only initialize once
    if (!window.vatRateManagementInitialized) {
        initVatRateCore();
        window.vatRateManagementInitialized = true;
    } else {
    }
};

// Main initialization function that runs on DOMContentLoaded and can be called again when needed
function initVatRateCore() {

    // Clean up any existing event listeners first
    cleanupEventListeners();

    // --- Get DOM Elements ---
    // Get the VAT rate form with the correct ID
    const vatRateForm = document.getElementById('vat-rate-form');
    const vatRatesTableBody = document.getElementById('vat-rates-table-body');
    const noRatesMessage = document.querySelector('#vat-rates-table-body + .alert-info'); // Assuming it follows the tbody if empty

    // --- Helper Functions ---

    /**
     * Shows a notification using AdminUtils if available, otherwise uses alert.
     * @param {string} message The message to display.
     * @param {string} type 'success', 'danger', 'info', 'warning'.
     * @returns {object|null} The toast instance or null.
     */
    function showNotification(message, type = 'info') {
        if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
            return AdminUtils.showNotification(message, type);
        } else {
            console.warn('AdminUtils.showNotification not available. Using alert().');
            alert(`${type.toUpperCase()}: ${message}`);
            return null;
        }
    }

    /**
     * Generic function to handle AJAX requests to admin.php for VAT rates.
     * @param {string} action The 'action' parameter for admin_ajax_handler.php.
     * @param {object} data Additional data to send (key-value pairs).
     * @param {string} loadingMessage Message to show while loading.
     * @returns {Promise<object>} A promise that resolves with the response data on success, or rejects on error.
     */
    async function makeAdminAjaxRequest(action, data = {}, loadingMessage = 'Processando...') {
        const formData = new FormData();
        formData.append('action', action);
        formData.append('section', 'vat_rates'); // Specific to this module
        formData.append('is_ajax', '1');

        // Add CSRF token
        const csrfToken = document.querySelector('input[name="csrf_token"]');
        if (csrfToken && csrfToken.value) {
            formData.append('csrf_token', csrfToken.value);
        } else {
            console.error('CSRF token not found or empty.');
            // Reject immediately if CSRF is crucial
            return Promise.reject({ success: false, error: 'CSRF token missing.' });
            // Or show notification and reject:
            // showNotification('Erro de segurança (CSRF token em falta). A operação foi cancelada.', 'danger');
            // return Promise.reject({ success: false, error: 'CSRF token missing.' });
        }

        // Add session parameter from URL
        const urlParams = new URLSearchParams(window.location.search);
        const sessionParam = urlParams.get('sid');
        if (sessionParam) {
            formData.append('sid', sessionParam);
        }

        // Add specific data for the action
        for (const key in data) {
            // Ensure the key is not inherited and value is not null/undefined
            if (data.hasOwnProperty(key) && data[key] != null) {
                 formData.append(key, data[key]);
            }
        } // Log FormData contents if needed (for debugging)
         // for (var pair of formData.entries()) { }


        // Show loading notification
        let loadingToast = showNotification(loadingMessage, 'info');

        try {
            const response = await fetch('admin_ajax.php', {
                method: 'POST',
                body: formData
            });

            // Always close loading toast regardless of response status
            if (loadingToast && loadingToast.close) loadingToast.close();

            if (!response.ok) {
                 // Try to get error details from response body if possible (e.g., if server sends JSON error)
                let errorDetails = `HTTP error! status: ${response.status}`;
                try {
                    const errorResult = await response.json();
                    errorDetails = errorResult.error || errorResult.message || errorDetails;
                } catch (e) {
                    // Ignore if response is not JSON or empty
                }
                throw new Error(errorDetails);
            }

            const result = await response.json();

            if (result.success) {
                showNotification(result.message || 'Operação concluída com sucesso.', 'success');
                return result; // Resolve with the successful result object
            } else {
                // Reject the promise with the error details from the server
                 // Prioritize result.message from the backend response for the notification
                 showNotification(result.message || result.error || 'Ocorreu um erro no servidor.', 'danger');
                 // Reject with a structured error object
                 return Promise.reject({ success: false, error: result.message || result.error || 'Unknown server error', details: result });
            }

        } catch (error) {
            // Also close toast in case of network error before response handling
            if (loadingToast && loadingToast.close) loadingToast.close();

            console.error(`Error during AJAX action '${action}':`, error);
            showNotification(`Erro ao comunicar com o servidor: ${error.message}`, 'danger');
            // Reject with a structured error object
            return Promise.reject({ success: false, error: `Network or processing error: ${error.message}` });
        }
    }


    /**
     * Safely escapes HTML content.
     * @param {string} unsafeStr The potentially unsafe string.
     * @returns {string} The escaped string.
     */
    function escapeHtml(unsafeStr) {
        if (unsafeStr === null || typeof unsafeStr === 'undefined') return '';
        return unsafeStr
             .toString()
             .replace(/&/g, "&")
             .replace(/</g, "<")
             .replace(/>/g, ">")
             .replace(/"/g, "&quot;")
             .replace(/'/g, "&#039;")
    }

    /**
     * Formats a number as a percentage string (e.g., 23.0 -> "23,0%").
     * @param {number|string} value The numeric value.
     * @returns {string} Formatted percentage string.
     */
    function formatPercentage(value) {
         const num = parseFloat(value);
         if (isNaN(num)) return 'N/A';
         // Format with one decimal place using comma as separator
         return num.toLocaleString(undefined, { minimumFractionDigits: 1, maximumFractionDigits: 1 }).replace('.', ',') + '%';
    }


    /**
     * Creates the HTML for action buttons for a VAT rate row.
     * @param {object} rateData The VAT rate data object.
     * @returns {string} HTML string for the buttons.
     */
    function createActionButtonsHtml(rateData) {
        let buttonsHtml = '<div class="btn-group" role="group">';
        let csrfToken = '';
        // Safely get CSRF token only if the form exists
        if (vatRateForm) {
            const csrfInput = vatRateForm.querySelector('input[name="csrf_token"]');
            if (csrfInput) {
                csrfToken = csrfInput.value;
            } else {
                 console.error("CSRF token input not found within #vat-rate-form for button generation.");
            }
        } else {
             console.error("#vat-rate-form not found during button generation.");
             // Try to get CSRF token from a hidden input in the page
             const csrfInputs = document.querySelectorAll('input[name="csrf_token"]');
             if (csrfInputs.length > 0) {
                 csrfToken = csrfInputs[0].value;
             }
        }


        // --- Add Edit Button ---
        // This button should always be present.
        buttonsHtml += `
            <button type="button" class="btn btn-sm btn-primary edit-vat-rate"
                    title="Editar Taxa"
                    data-id="${rateData.id}"
                    data-rate="${escapeHtml(rateData.rate)}"
                    data-description="${escapeHtml(rateData.description)}"
                    data-default="${rateData.is_default ? '1' : '0'}">
                <i class="fas fa-edit"></i> Editar
            </button>`;

        // Only show Set Default and Delete if not already default
        if (!rateData.is_default) {
            buttonsHtml += `
                <button type="button" class="btn btn-sm btn-success set-default-vat"
                        title="Definir como Padrão"
                        data-id="${rateData.id}" data-csrf="${escapeHtml(csrfToken)}">
                    <i class="fas fa-check"></i> Definir Padrão
                </button>`;
            buttonsHtml += `
                <button type="button" class="btn btn-sm btn-danger delete-vat-rate"
                        title="Eliminar Taxa"
                        data-id="${rateData.id}" data-csrf="${escapeHtml(csrfToken)}">
                    <i class="fas fa-trash"></i> Eliminar
                </button>`;
        }
        buttonsHtml += '</div>';
        return buttonsHtml;
    }

    /**
     * Adds a new VAT rate row to the table.
     * @param {object} rateData The data for the new VAT rate.
     */
    function addVatRateToTable(rateData) {
        if (!vatRatesTableBody) return;

        // Remove the 'no rates' message if it exists
        const noRatesMsg = document.querySelector('#no-rates-message'); // Assume an ID for the message div
        if (noRatesMsg) {
            noRatesMsg.remove();
        }
         // Or if using the class selector and assuming it's a direct sibling:
         const infoAlert = vatRatesTableBody.nextElementSibling;
         if (infoAlert && infoAlert.classList.contains('alert-info')) {
             infoAlert.remove();
         }

        const newRow = vatRatesTableBody.insertRow();
        newRow.setAttribute('data-rate-id', rateData.id); // Add data attribute for easier selection

        newRow.innerHTML = `
            <td>${formatPercentage(rateData.rate)}</td>
            <td>${escapeHtml(rateData.description)}</td>
            <td>
                ${rateData.is_default
                    ? '<span class="badge bg-success">Sim</span>'
                    : '<span class="badge bg-secondary">Não</span>'}
            </td>
            <td>${createActionButtonsHtml(rateData)}</td>
        `;
    }

    /**
     * Removes a VAT rate row from the table.
     * @param {string|number} rateId The ID of the rate to remove.
     */
    function removeVatRateFromTable(rateId) {
        if (!vatRatesTableBody) return;
        const rowToRemove = vatRatesTableBody.querySelector(`tr[data-rate-id="${rateId}"]`);
        if (rowToRemove) {
            rowToRemove.remove();

            // Check if table is now empty and show message if needed
            if (vatRatesTableBody.rows.length === 0) {
                // Add the 'no rates' message back (ensure you have the correct HTML structure)
                const parentDiv = vatRatesTableBody.closest('.table-responsive'); // Or card-body
                if(parentDiv){
                    const noRatesDiv = document.createElement('div');
                    noRatesDiv.id = 'no-rates-message'; // Give it an ID
                    noRatesDiv.className = 'alert alert-info mt-3';
                    noRatesDiv.textContent = 'Não existem taxas de IVA definidas. Utilize o formulário acima para adicionar.';
                    parentDiv.insertAdjacentElement('afterend', noRatesDiv); // Insert after the table container
                }
            }
        } else {
            console.warn(`Could not find row with ID ${rateId} to remove.`);
        }
    }

     /**
     * Updates the default status indicators and buttons in the table.
     * @param {string|number} newDefaultId The ID of the new default rate.
     */
    function updateTableDefaultStatus(newDefaultId) {
        if (!vatRatesTableBody) return;

        const rows = vatRatesTableBody.querySelectorAll('tr');
        let csrfToken = '';
         // Safely get CSRF token only if the form exists
           if (vatRateForm) {
             const csrfInput = vatRateForm.querySelector('input[name="csrf_token"]');
             if (csrfInput) {
                 csrfToken = csrfInput.value;
             } else {
                  console.error("CSRF token input not found within #vat-rate-form for default status update.");
             }
         } else {
              console.error("#vat-rate-form not found during default status update.");
              // Try to get CSRF token from a hidden input in the page
              const csrfInputs = document.querySelectorAll('input[name="csrf_token"]');
              if (csrfInputs.length > 0) {
                  csrfToken = csrfInputs[0].value;
              }
         }

        rows.forEach(row => {
            const rateId = row.dataset.rateId;
            const isNowDefault = (rateId == newDefaultId); // Compare potentially different types
            const rateCell = row.cells[0];      // 1st cell (index 0) for rate text
            const descriptionCell = row.cells[1]; // 2nd cell (index 1) for description text
            const defaultCell = row.cells[2];   // 3rd cell (index 2) for default status
            const actionCell = row.cells[3];    // 4th cell (index 3) for actions

            // Update Default Badge
            if (defaultCell) {
                defaultCell.innerHTML = isNowDefault
                    ? '<span class="badge bg-success">Sim</span>'
                    : '<span class="badge bg-secondary">Não</span>';
            }

            // Update Action Buttons
            if (actionCell) {
                const rateData = {
                    id: rateId, // Keep existing ID
                    // Extract rate and description from the row's content for the Edit button data
                    // Note: This assumes formatPercentage inverses correctly, might need adjustment
                    // It's safer to store original values in data attributes if possible
                    rate: rateCell ? parseFloat(rateCell.textContent.replace('%','').replace(',','.')) : '', // Attempt to get rate back
                    description: descriptionCell ? descriptionCell.textContent : '', // Get description text
                    is_default: isNowDefault // Set the new default status
                };
                 actionCell.innerHTML = createActionButtonsHtml(rateData);
            }
        });
    }


    // Function to handle the inline form submission for adding VAT rate
    async function handleAddVatRate(event) {
        event.preventDefault(); // Prevent default form submission
        event.stopPropagation(); // Stop event from bubbling up to parent forms
        const form = event.target;

        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Get form values directly from the form fields
        const id = document.getElementById('vat_rate_id')?.value || '';
        const rate = document.getElementById('vat_rate')?.value || '';
        const description = document.getElementById('vat_description')?.value || '';
        const isDefault = document.getElementById('vat_is_default')?.checked ? 1 : 0;

        // Validate required fields
        if (!rate) {
            alert('Por favor, insira a taxa de IVA.');
            document.getElementById('vat_rate')?.focus();
            return;
        }

        if (!description) {
            alert('Por favor, insira a descrição da taxa de IVA.');
            document.getElementById('vat_description')?.focus();
            return;
        }

        // Create data object for the AJAX request
        const data = {
            id: id,
            rate: rate,
            description: description,
            is_default: isDefault
        };

        // Determine the action based on whether we're creating or updating
        const action = id ? 'update_vat_rate' : 'create_vat_rate'; // Corrected action name
        const loadingMessage = id ? 'A atualizar taxa de IVA...' : 'A adicionar taxa de IVA...';

        try {
            const result = await makeAdminAjaxRequest(action, data, loadingMessage);

            if (result && result.success) {
                // Close the modal
                const vatRateModal = document.getElementById('vatRateModal');
                if (vatRateModal) {
                    const bsModal = bootstrap.Modal.getInstance(vatRateModal);
                    if (bsModal) {
                        bsModal.hide();
                    }
                }

                // Update the table
                if (id) {
                    // Update existing row
                    const row = document.querySelector(`tr[data-rate-id="${id}"]`);
                    if (row) {
                        row.cells[0].textContent = formatPercentage(rate);
                        row.cells[1].textContent = description;
                        row.cells[2].innerHTML = isDefault
                            ? '<span class="badge bg-success">Sim</span>'
                            : '<span class="badge bg-secondary">Não</span>';

                        // Update action buttons if default status changed
                        if (isDefault) {
                            updateTableDefaultStatus(id);
                        }
                    }
                } else {
                    // Add new row if the result contains the new VAT rate data
                    if (result.data && result.data.vat_rate) {
                        addVatRateToTable(result.data.vat_rate);
                    } else {
                        // If no data returned, refresh the table
                        location.reload();
                    }
                }

                // Reset the form
                form.reset();
            }
            // Notification is handled by makeAdminAjaxRequest

        } catch (error) {
            console.error("Add/Update operation failed:", error);
            // Notification handled by makeAdminAjaxRequest
        }
    }


    // Function to handle deleting a VAT rate using confirm()
     async function handleDeleteVatRate(button) {

        const rateId = button.dataset.id;
        if (!rateId) {
            showNotification('Erro: ID da taxa não encontrado.', 'danger');
            return;
        }
        const confirmation = confirm('Tem a certeza que pretende eliminar esta taxa de IVA?\n\nNota: Apenas taxas que não são padrão e não estão associadas a produtos podem ser eliminadas.');

        if (confirmation) {
            const data = { id: rateId };

            try {
                const result = await makeAdminAjaxRequest('delete_vat_rate', data, 'A eliminar taxa de IVA...'); // Corrected action name

                if (result && result.success) {
                    removeVatRateFromTable(rateId); // Remove row dynamically
                }
                // Notification handled by makeAdminAjaxRequest

            } catch (error) {
                console.error("Delete operation failed:", error);
                // Notification handled by makeAdminAjaxRequest
            }
        } else {
        }
    }

    // Function to handle setting a VAT rate as default
    async function handleSetDefaultVatRate(button) {
        const id = button.dataset.id;
         if (!id) {
            console.error('Missing data-id attribute for set default operation');
            showNotification('Erro: ID da taxa não encontrado.', 'danger');
            return;
        }
         const data = { id: id };

        try {
            const result = await makeAdminAjaxRequest('set_default_vat_rate', data, 'A definir como taxa padrão...'); // Corrected action name

             if (result && result.success) {
                 updateTableDefaultStatus(id); // Update table UI dynamically
             }
             // Notification handled by makeAdminAjaxRequest

        } catch (error) {
            console.error("Set default operation failed:", error);
             // Notification handled by makeAdminAjaxRequest
        }
    }


    // --- Event Listeners ---

    // VAT Rate Form Submission (Listener added directly to the VAT rate form)
    function handleFormSubmit(event) {
        handleAddVatRate(event);
    }

    // Store reference to the handler for cleanup
    window.vatRateEventHandlers.formSubmit = handleFormSubmit;

    // Add the event listener directly to the VAT rate form instead of document-level
    // Use the already declared vatRateForm variable from above
    if (vatRateForm) {
        vatRateForm.addEventListener('submit', handleFormSubmit);
    } else {
        // Set up a mutation observer to watch for the form being added to the DOM
        const observer = new MutationObserver(function() {
            const newVatRateForm = document.getElementById('vat-rate-form');
            if (newVatRateForm && !newVatRateForm.hasSubmitListener) {
                newVatRateForm.addEventListener('submit', handleFormSubmit);
                newVatRateForm.hasSubmitListener = true;
                observer.disconnect();
            }
        });

        // Start observing the document body for changes
        observer.observe(document.body, { childList: true, subtree: true });
    }

    // Add VAT Rate button click handler using event delegation
    function handleAddVatRateButtonClick(e) {
        const addButton = e.target.closest('#add-vat-rate-btn');
        if (!addButton) return;

        e.preventDefault();
        e.stopPropagation(); // Stop event from bubbling up to parent elements

        // Reset form fields directly
        if (document.getElementById('vat_rate_id')) {
            document.getElementById('vat_rate_id').value = '';
        }
        if (document.getElementById('vat_rate')) {
            document.getElementById('vat_rate').value = '';
        }
        if (document.getElementById('vat_description')) {
            document.getElementById('vat_description').value = '';
        }
        if (document.getElementById('vat_is_default')) {
            document.getElementById('vat_is_default').checked = false;
        }

        // Set modal title
        if (document.getElementById('vatRateModalLabel')) {
            document.getElementById('vatRateModalLabel').textContent = 'Adicionar Taxa de IVA';
        }

        // Show modal
        const vatRateModal = document.getElementById('vatRateModal');
        if (vatRateModal) {
            try {
                // Use getOrCreateInstance to avoid issues with multiple 'new' calls
                const bsModal = bootstrap.Modal.getOrCreateInstance(vatRateModal);
                bsModal.show();
            } catch (error) {
                console.error('Error showing modal:', error);
                alert('Erro ao abrir o modal. Por favor, recarregue a página e tente novamente.');
            }
        } else {
            console.error('VAT Rate Modal not found');
        }
    }
    // Store reference to the handler for cleanup
    window.vatRateEventHandlers.addButtonClick = handleAddVatRateButtonClick;
    document.addEventListener('click', handleAddVatRateButtonClick);

    // Event Delegation for Edit/Delete/Set Default buttons in the table
    // Attach to document to handle cases where the table is loaded dynamically.
    function handleVatRateButtonClicks(e) {
        // Find the closest relevant button without restricting to table body
        const deleteButton = e.target.closest('.delete-vat-rate');
        const setDefaultButton = e.target.closest('.set-default-vat');
        const editButton = e.target.closest('.edit-vat-rate');

        // Only process if one of our buttons was clicked
        if (!deleteButton && !setDefaultButton && !editButton) return;

        // Stop event propagation for all VAT rate buttons
        e.stopPropagation();

        if (deleteButton) {
            e.preventDefault();
            handleDeleteVatRate(deleteButton); // Use confirm() and dynamic removal
        } else if (setDefaultButton) {
            e.preventDefault();
            handleSetDefaultVatRate(setDefaultButton); // Use dynamic UI update
        } else if (editButton) {
            e.preventDefault();

            // Get data from button
            const id = editButton.dataset.id;
            const rate = editButton.dataset.rate;
            const description = editButton.dataset.description;
            const isDefault = editButton.dataset.default === '1';

            // Set form values
            if (document.getElementById('vat_rate_id')) {
                document.getElementById('vat_rate_id').value = id;
            }
            if (document.getElementById('vat_rate')) {
                document.getElementById('vat_rate').value = rate;
            }
            if (document.getElementById('vat_description')) {
                document.getElementById('vat_description').value = description;
            }
            if (document.getElementById('vat_is_default')) {
                document.getElementById('vat_is_default').checked = isDefault;
            }

            // Set modal title
            if (document.getElementById('vatRateModalLabel')) {
                document.getElementById('vatRateModalLabel').textContent = 'Editar Taxa de IVA';
            }

            // Show modal
            const vatRateModal = document.getElementById('vatRateModal');
            if (vatRateModal) {
                try {
                     // Use getOrCreateInstance to avoid issues with multiple 'new' calls
                    const bsModal = bootstrap.Modal.getOrCreateInstance(vatRateModal);
                    bsModal.show();
                } catch (error) {
                    console.error('Error showing modal:', error);
                    alert('Erro ao abrir o modal. Por favor, recarregue a página e tente novamente.');
                }
            }
        }
    }
    // Store reference to the handler for cleanup
    window.vatRateEventHandlers.tableButtonClicks = handleVatRateButtonClicks;
    document.addEventListener('click', handleVatRateButtonClicks);

    // Save VAT Rate button click handler using event delegation
    function handleSaveVatRateButtonClick(e) {
        const saveButton = e.target.closest('#save-vat-rate-btn');
        if (!saveButton) return;

        e.preventDefault();
        e.stopPropagation(); // Stop event from bubbling up to parent elements

        // If we have a form, submit it (this will trigger the form submit handler)
        const vatRateForm = document.getElementById('vat-rate-form');
        if (vatRateForm) {
            // Create and dispatch a submit event
            // Set bubbles to false to prevent event from propagating to parent forms
            const submitEvent = new Event('submit', { bubbles: false, cancelable: true });
            vatRateForm.dispatchEvent(submitEvent);
            return;
        }

        // If no form is found, continue with the direct approach

        // Validate form fields directly
        const rateInput = document.getElementById('vat_rate');
        const descriptionInput = document.getElementById('vat_description');

        if (!rateInput || !descriptionInput) {
            console.error('Required form fields not found');
            alert('Erro: Campos do formulário não encontrados.');
            return;
        }

        if (!rateInput.value) {
            alert('Por favor, insira a taxa de IVA.');
            rateInput.focus();
            return;
        }

        if (!descriptionInput.value) {
            alert('Por favor, insira a descrição da taxa de IVA.');
            descriptionInput.focus();
            return;
        }

        // Get form values
        const id = document.getElementById('vat_rate_id')?.value || '';
        const rate = rateInput.value;
        const description = descriptionInput.value;
        const isDefault = document.getElementById('vat_is_default')?.checked ? 1 : 0;

        // Create data object for the AJAX request
        const data = {
            id: id,
            rate: rate,
            description: description,
            is_default: isDefault
        };

        // Determine the action based on whether we're creating or updating
        const action = id ? 'update_vat_rate' : 'create_vat_rate'; // Corrected action name
        const loadingMessage = id ? 'A atualizar taxa de IVA...' : 'A criar taxa de IVA...';

        // Make the AJAX request
        makeAdminAjaxRequest(action, data, loadingMessage)
            .then(result => {
                if (result && result.success) {
                    // Close the modal
                    const vatRateModal = document.getElementById('vatRateModal');
                    if (vatRateModal) {
                        const bsModal = bootstrap.Modal.getInstance(vatRateModal);
                        if (bsModal) {
                            bsModal.hide();
                        }
                    }

                    // Update the table
                    if (id) {
                        // Update existing row
                        const row = document.querySelector(`tr[data-rate-id="${id}"]`);
                        if (row) {
                            row.cells[0].textContent = formatPercentage(rate);
                            row.cells[1].textContent = description;
                            row.cells[2].innerHTML = isDefault
                                ? '<span class="badge bg-success">Sim</span>'
                                : '<span class="badge bg-secondary">Não</span>';

                            // Update action buttons if default status changed
                            if (isDefault) {
                                updateTableDefaultStatus(id);
                            }
                        }
                    } else {
                        // Add new row if the result contains the new VAT rate data
                        if (result.data && result.data.vat_rate) {
                            addVatRateToTable(result.data.vat_rate);
                        } else {
                            // If no data returned, refresh the table
                            location.reload();
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Error saving VAT rate:', error);
            });
    }
    // Store reference to the handler for cleanup
    window.vatRateEventHandlers.saveButtonClick = handleSaveVatRateButtonClick;
    document.addEventListener('click', handleSaveVatRateButtonClick);

    // Remove Modal Event Listeners and Backdrop Fix logic

} // End initVatRateCore function

// Function to clean up event listeners before re-initializing
function cleanupEventListeners() {

    // Store references to our event handlers
    if (window.vatRateEventHandlers) {
        // Remove form-specific event listener
        if (window.vatRateEventHandlers.formSubmit) {
            const vatRateForm = document.getElementById('vat-rate-form');
            if (vatRateForm) {
                vatRateForm.removeEventListener('submit', window.vatRateEventHandlers.formSubmit);
            } else {
            }
        }

        // Remove document-level event listeners
        if (window.vatRateEventHandlers.addButtonClick) {
            document.removeEventListener('click', window.vatRateEventHandlers.addButtonClick);
        }
        if (window.vatRateEventHandlers.tableButtonClicks) {
            document.removeEventListener('click', window.vatRateEventHandlers.tableButtonClicks);
        }
        if (window.vatRateEventHandlers.saveButtonClick) {
            document.removeEventListener('click', window.vatRateEventHandlers.saveButtonClick);
        }

        // Clear the references
        window.vatRateEventHandlers = null;
    }

    // Initialize the event handler storage if it doesn't exist
    window.vatRateEventHandlers = {};
}

// Initialize on DOMContentLoaded - only if not already initialized
document.addEventListener('DOMContentLoaded', function() {
    if (!window.vatRateManagementInitialized) {
        initVatRateCore();
        window.vatRateManagementInitialized = true;
    } else {
    }
});
