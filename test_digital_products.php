<?php

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/digital_product_functions.php';
require_once __DIR__ . '/includes/digital_order_functions.php';

session_start();

$pdo = get_db_connection();
if (!$pdo) {
    die("Database connection failed");
}

echo "<h1>Digital Products Test Page</h1>";

echo "<h2>Digital Product Settings</h2>";
echo "<ul>";
echo "<li>Digital Download Expiry Days: " . get_setting('digital_download_expiry_days', 5) . "</li>";
echo "<li>Digital Download Limit: " . get_setting('digital_download_limit', 3) . "</li>";
echo "<li>Digital Products Directory: " . get_setting('digital_products_directory', '../digital_products') . "</li>";
echo "</ul>";

echo "<h2>Digital Product File Types</h2>";
$file_types = db_query("SELECT * FROM digital_product_file_types", [], false, true);
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Name</th><th>Extension</th></tr>";
foreach ($file_types as $type) {
    echo "<tr>";
    echo "<td>" . $type['id'] . "</td>";
    echo "<td>" . $type['name'] . "</td>";
    echo "<td>" . $type['extension'] . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h2>Digital Products Table</h2>";
$digital_products = db_query("SELECT * FROM digital_products", [], false, true);
if (empty($digital_products)) {
    echo "<p>No digital products found.</p>";
} else {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Product ID</th><th>File Path</th><th>Expiry Days</th><th>Download Limit</th></tr>";
    foreach ($digital_products as $product) {
        echo "<tr>";
        echo "<td>" . $product['id'] . "</td>";
        echo "<td>" . $product['product_id'] . "</td>";
        echo "<td>" . $product['file_path'] . "</td>";
        echo "<td>" . $product['expiry_days'] . "</td>";
        echo "<td>" . $product['download_limit'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h2>Licenses Table</h2>";
$licenses = db_query("SELECT * FROM licenses", [], false, true);
if (empty($licenses)) {
    echo "<p>No licenses found.</p>";
} else {
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>License Code</th><th>Order ID</th><th>Customer</th><th>Status</th><th>Expiry Date</th><th>Downloads</th></tr>";
    foreach ($licenses as $license) {
        echo "<tr>";
        echo "<td>" . $license['id'] . "</td>";
        echo "<td>" . $license['license_code'] . "</td>";
        echo "<td>" . $license['order_id'] . "</td>";
        echo "<td>" . $license['customer_name'] . " (" . $license['customer_email'] . ")</td>";
        echo "<td>" . $license['status'] . "</td>";
        echo "<td>" . $license['expiry_date'] . "</td>";
        echo "<td>" . $license['downloads_used'] . " / " . $license['download_limit'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h2>License Code Generation Test</h2>";
$license_code = generate_license_code();
echo "<p>Generated License Code: " . $license_code . "</p>";

echo "<h2>Digital Product Directory Test</h2>";
$digital_products_dir = get_setting('digital_products_directory', '../digital_products');
if (is_dir($digital_products_dir)) {
    echo "<p>Digital products directory exists: " . $digital_products_dir . "</p>";
} else {
    echo "<p>Digital products directory does not exist: " . $digital_products_dir . "</p>";
}

echo "<h2>Product Types Test</h2>";
$product_types = db_query("SELECT id, name_pt, product_type FROM products", [], false, true);
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Name</th><th>Product Type</th></tr>";
foreach ($product_types as $product) {
    echo "<tr>";
    echo "<td>" . $product['id'] . "</td>";
    echo "<td>" . $product['name_pt'] . "</td>";
    echo "<td>" . $product['product_type'] . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<p><a href='admin.php'>Back to Admin</a></p>";
