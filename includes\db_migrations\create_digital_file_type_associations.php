<?php

/**
 * Migration to create digital_file_type_associations table and migrate data from digital_product_file_type_associations
 */
function create_digital_file_type_associations_table(PDO $pdo): bool
{
    try {
        // Check if the migration has already been applied
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_file_type_associations';");
        $table_exists = $stmt->fetch();

        if ($table_exists) {
            // Table already exists, migration already applied
            return true;
        }

        // Begin transaction
        $pdo->beginTransaction();

        // Create the new table
        $sql = "CREATE TABLE IF NOT EXISTS digital_file_type_associations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            digital_file_id INTEGER NOT NULL,
            file_type_id INTEGER NOT NULL,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            FOREIGN KEY (digital_file_id) REFERENCES digital_files(id) ON DELETE CASCADE,
            F<PERSON><PERSON><PERSON><PERSON> KEY (file_type_id) REFERENCES digital_product_file_types(id) ON DELETE CASCADE,
            UNIQUE (digital_file_id, file_type_id)
        )";
        $pdo->exec($sql);

        // Create indexes
        $pdo->exec("CREATE INDEX idx_digital_file_type_associations_file_id ON digital_file_type_associations (digital_file_id);");
        $pdo->exec("CREATE INDEX idx_digital_file_type_associations_type_id ON digital_file_type_associations (file_type_id);");

        // Migrate data from digital_product_file_type_associations to digital_file_type_associations
        // First, get all digital products with their file IDs
        $sql = "SELECT dp.id as digital_product_id, dp.digital_file_id 
                FROM digital_products dp 
                WHERE dp.digital_file_id IS NOT NULL";
        $digital_products = $pdo->query($sql)->fetchAll(PDO::FETCH_ASSOC);

        // For each digital product, get its file type associations and create new associations for the file
        foreach ($digital_products as $product) {
            $digital_product_id = $product['digital_product_id'];
            $digital_file_id = $product['digital_file_id'];

            // Get file type associations for this digital product
            $sql = "SELECT file_type_id FROM digital_product_file_type_associations 
                    WHERE digital_product_id = :digital_product_id";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([':digital_product_id' => $digital_product_id]);
            $file_type_ids = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // Create new associations for the digital file
            foreach ($file_type_ids as $file_type_id) {
                // Check if association already exists
                $check_sql = "SELECT 1 FROM digital_file_type_associations 
                             WHERE digital_file_id = :digital_file_id AND file_type_id = :file_type_id";
                $check_stmt = $pdo->prepare($check_sql);
                $check_stmt->execute([
                    ':digital_file_id' => $digital_file_id,
                    ':file_type_id' => $file_type_id
                ]);
                
                if (!$check_stmt->fetch()) {
                    // Association doesn't exist, create it
                    $insert_sql = "INSERT INTO digital_file_type_associations 
                                  (digital_file_id, file_type_id, created_at) 
                                  VALUES (:digital_file_id, :file_type_id, datetime('now', 'localtime'))";
                    $insert_stmt = $pdo->prepare($insert_sql);
                    $insert_stmt->execute([
                        ':digital_file_id' => $digital_file_id,
                        ':file_type_id' => $file_type_id
                    ]);
                }
            }
        }

        // Commit transaction
        $pdo->commit();
        
        // Record the migration
        $pdo->exec("INSERT INTO migrations (name, executed_at) VALUES ('create_digital_file_type_associations', datetime('now', 'localtime'))");
        
        return true;
    } catch (PDOException $e) {
        // Rollback transaction on error
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Migration error: " . $e->getMessage());
        return false;
    }
}
