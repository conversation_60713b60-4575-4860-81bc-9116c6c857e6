<?php

function generate_simple_captcha(): array
{
    
    $num1 = rand(1, 10);
    $num2 = rand(1, 10);

    
    $operation = '+';

    
    $answer = $num1 + $num2;

    
    $question = "{$num1} {$operation} {$num2} = ?";

    
    $_SESSION['captcha'] = [
        'answer' => $answer,
        'num1' => $num1,
        'num2' => $num2,
        'operation' => $operation,
        'expires' => time() + 600 
    ];

    return [
        'question' => $question,
        'answer' => $answer,
        'num1' => $num1,
        'num2' => $num2,
        'operation' => $operation
    ];
}

function verify_captcha($user_answer): bool
{
    
    if (!isset($_SESSION['captcha']) || !isset($_SESSION['captcha']['answer']) || !isset($_SESSION['captcha']['expires'])) {
        return false;
    }

    
    if ($_SESSION['captcha']['expires'] < time()) {
        unset($_SESSION['captcha']);
        return false;
    }

    
    $num1 = isset($_SESSION['captcha']['num1']) ? (int)$_SESSION['captcha']['num1'] : 0;
    $num2 = isset($_SESSION['captcha']['num2']) ? (int)$_SESSION['captcha']['num2'] : 0;
    $operation = isset($_SESSION['captcha']['operation']) ? $_SESSION['captcha']['operation'] : '+';

    
    $expected_answer = ($operation === '+') ? $num1 + $num2 : $num1 - $num2;

    
    $user_answer = (int)$user_answer;

    
    $is_valid = ($expected_answer === $user_answer);

    
    $result = $is_valid;

    
    unset($_SESSION['captcha']);

    return $result;
}
