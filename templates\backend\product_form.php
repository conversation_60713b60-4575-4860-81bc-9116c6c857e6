<?php

$is_editing = ($action === 'edit' && isset($item_id));
$form_title = $is_editing ? "Editar Produto" : "Adicionar Novo Produto";
$product_data = null;
$product_attributes_used = [];

if ($is_editing) {

    $product_data = db_query("SELECT * FROM products WHERE id = :id", [':id' => $item_id], true);
    if (!$product_data) {
        echo '<div class="alert alert-danger">Produto não encontrado.</div>';
        return;
    }

    $form_title .= isset($product_data['id']) ? " (#" . $product_data['id'] . ")" : '';
}


   $product_category_ids = [];
   if ($is_editing) {
       $cats_raw = db_query("SELECT category_id FROM product_categories WHERE product_id = :pid", [':pid' => $item_id], false, true);
       if ($cats_raw) {
           $product_category_ids = array_column($cats_raw, 'category_id');
       }
   }

$product_variations = db_query(
    "SELECT
        pv.*,
        GROUP_CONCAT(av.id || ':' || a.id || ':' || a.name_pt || ':' || av.value_pt || ':' || av.price_modifier) as variation_attributes_details
     FROM product_variations pv
     LEFT JOIN variation_values vv ON pv.id = vv.variation_id
     LEFT JOIN attribute_values av ON vv.value_id = av.id
     LEFT JOIN attributes a ON av.attribute_id = a.id
     WHERE pv.product_id = :product_id /* Removed active checks here to get all, handle active status in display/logic */
     GROUP BY pv.id
     ORDER BY pv.id",
    [':product_id' => $item_id],
    false,
    true
);

$variations_data = [];
if ($product_variations) {
    foreach ($product_variations as $pv) {
        $attributes_array = [];
        $calculated_modifier = 0.0;
        if (!empty($pv['variation_attributes_details'])) {
            $attrs = explode(',', $pv['variation_attributes_details']);
            foreach ($attrs as $attr) {

                list($value_id, $attr_id, $attr_name, $value_name, $value_modifier) = explode(':', $attr, 5);
                $attributes_array[$attr_name] = ['value_id' => $value_id, 'value_name' => $value_name];
                $calculated_modifier += (float)$value_modifier;
                $product_attributes_used[$attr_id] = true;
            }
        }
        $variations_data[$pv['id']] = [
            'sku' => $pv['sku'],
            'calculated_modifier' => $calculated_modifier,
            'price_modifier_override' => $pv['price_modifier_override'],
            'stock' => $pv['stock'],
            'weight' => $pv['weight'],
            'dimensions' => $pv['dimensions'],
            'is_active' => $pv['is_active'],
            'attributes' => $attributes_array,
        ];
    }

}

$all_attributes = db_query("SELECT id, name_pt FROM attributes ORDER BY name_pt", [], false, true);

?>

<h1><?= $form_title ?></h1>
<hr>

<?php display_flash_messages(); ?>

<form method="POST" action="admin.php?section=products&action=<?= $is_editing ? 'edit&id=' . $item_id : 'new' ?>&<?= get_session_id_param() ?>" enctype="multipart/form-data"> <!-- Add enctype for file uploads -->
    <?= csrf_input_field() ?>

    <div class="card mb-4">
        <div class="card-header">Detalhes Principais</div>
        <div class="card-body">
            <div class="mb-3">
                <label for="name_pt" class="form-label">Nome do Produto (PT) *</label>
                <input type="text" class="form-control" id="name_pt" name="name_pt" value="<?= sanitize_input($product_data['name_pt'] ?? '') ?>" required>
            </div>
            <div class="mb-3">
                <label for="slug" class="form-label">Slug (URL) *</label>
                <input type="text" class="form-control" id="slug" name="slug" value="<?= sanitize_input($product_data['slug'] ?? '') ?>" required>
                <div class="form-text">Parte do URL amigável (ex: meu-produto-incrivel). Use letras minúsculas, números e hífens.</div>
            </div>
             <div class="mb-3">
                <label for="description_pt" class="form-label">Descrição (PT)</label>
                <textarea class="form-control summernote-editor" id="description_pt" name="description_pt" rows="5"><?= sanitize_input($product_data['description_pt'] ?? '') ?></textarea>
            </div>
             <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="base_price" class="form-label">Preço Base (<?= get_setting('currency_symbol', '€') ?>) *</label>
                    <input type="number" step="0.01" class="form-control" id="base_price" name="base_price" value="<?= sanitize_input($product_data['base_price'] ?? '0.00') ?>" required>
                </div>
                <div class="col-md-4 mb-3">
                    <?php

                    $vat_rates = vat_get_all_rates(true);


                    $default_vat_rate = vat_get_default_rate();
                    $default_vat_id = $default_vat_rate ? $default_vat_rate['id'] : null;


                    $product_vat_id = $product_data['vat_rate_id'] ?? $default_vat_id;
                    ?>
                    <label for="vat_rate_id" class="form-label">Taxa de IVA</label>
                    <select class="form-select" id="vat_rate_id" name="vat_rate_id">
                        <?php if ($vat_rates && count($vat_rates) > 0): ?>
                            <?php foreach ($vat_rates as $rate): ?>
                                <option value="<?= $rate['id'] ?>" <?= ($product_vat_id == $rate['id']) ? 'selected' : '' ?>>
                                    <?= number_format($rate['rate'], 1, ',', '.') ?>% - <?= sanitize_input($rate['description']) ?>
                                </option>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <option value="">Nenhuma taxa de IVA definida</option>
                        <?php endif; ?>
                    </select>
                    <div class="form-text">Selecione a taxa de IVA aplicável a este produto.</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="is_active" class="form-label">Estado</label>
                    <select class="form-select" id="is_active" name="is_active">
                        <option value="1" <?= (($product_data['is_active'] ?? 1) == 1) ? 'selected' : '' ?>>Ativo</option>
                        <option value="0" <?= (($product_data['is_active'] ?? 1) == 0) ? 'selected' : '' ?>>Inativo</option>
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="created_at" class="form-label">Data de Criação <i class="bi bi-calendar-event"></i></label>
                    <input type="datetime-local" class="form-control" id="created_at" name="created_at" value="<?= !empty($product_data['created_at']) ? date('Y-m-d\TH:i', strtotime($product_data['created_at'])) : '' ?>">
                    <div class="form-text">Deixe em branco para usar a data atual ao criar. Formato esperado: YYYY-MM-DDTHH:MM.</div>
                </div>
            </div>

            <!-- Product Type Selection -->
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Tipo de Produto</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="product_type" id="product_type_simple" value="regular" <?= (empty($variations_data) && ($product_data['product_type'] ?? 'regular') !== 'digital') ? 'checked' : '' ?>>
                        <label class="form-check-label" for="product_type_simple">
                            Produto Simples (sem variações)
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="product_type" id="product_type_variable" value="variation" <?= !empty($variations_data) ? 'checked' : '' ?>>
                        <label class="form-check-label" for="product_type_variable">
                            Produto com Variações
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="product_type" id="product_type_digital" value="digital" <?= ($product_data['product_type'] ?? '') === 'digital' ? 'checked' : '' ?>>
                        <label class="form-check-label" for="product_type_digital">
                            Produto Digital (arquivo para download)
                        </label>
                    </div>
                </div>

                <!-- Simple Product Fields (SKU and Stock) -->
                <div id="simple-product-fields" class="col-md-8 mb-3" style="<?= !empty($variations_data) || ($product_data['product_type'] ?? '') === 'digital' ? 'display: none;' : '' ?>">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="simple_sku" class="form-label">SKU (5 caracteres) *</label>
                            <input type="text" class="form-control" id="simple_sku" name="simple_sku" value="<?= sanitize_input($product_data['sku'] ?? '') ?>" maxlength="5" <?= empty($variations_data) && ($product_data['product_type'] ?? 'regular') !== 'digital' ? 'required' : '' ?>>
                            <div class="form-text">Código único de 5 caracteres para identificar este produto.</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="simple_stock" class="form-label">Stock *</label>
                            <input type="number" min="0" step="1" class="form-control" id="simple_stock" name="simple_stock" value="<?= sanitize_input($product_data['stock'] ?? '0') ?>" <?= empty($variations_data) && ($product_data['product_type'] ?? 'regular') !== 'digital' ? 'required' : '' ?>>
                            <div class="form-text">Quantidade disponível em stock.</div>
                        </div>
                    </div>
                </div>

                <!-- Digital Product Fields -->
                <div id="digital-product-fields" class="col-md-8 mb-3" style="<?= ($product_data['product_type'] ?? '') !== 'digital' ? 'display: none;' : '' ?>">
                    <?php

                    $digital_product = null;
                    if ($is_editing && ($product_data['product_type'] ?? '') === 'digital') {
                        require_once __DIR__ . '/../../includes/digital_product_functions.php';
                        $digital_product = get_digital_product_by_product_id($item_id);
                    }
                    ?>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="digital_sku" class="form-label">SKU (10 caracteres) *</label>
                            <input type="text" class="form-control" id="digital_sku" name="digital_sku" value="<?= sanitize_input($product_data['sku'] ?? '') ?>" maxlength="10" <?= ($product_data['product_type'] ?? '') === 'digital' ? 'required' : '' ?>>
                            <div class="form-text">Código único de 10 caracteres para identificar este produto digital.</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <?php if ($is_editing): ?>
                                <div class="d-grid gap-2">
                                    <?php if ($digital_product): ?>
                                        <a href="admin.php?section=digital_products&action=edit&id=<?= $item_id ?>&<?= get_session_id_param() ?>" class="btn btn-primary">
                                            <i class="bi bi-pencil-square"></i> Editar Detalhes do Produto Digital
                                        </a>
                                        <div class="mt-2">
                                            <span class="badge bg-info">Arquivo atual: <?= basename($digital_product['file_path']) ?></span>
                                        </div>
                                    <?php else: ?>
                                        <a href="admin.php?section=digital_products&action=new&id=<?= $item_id ?>&<?= get_session_id_param() ?>" class="btn btn-success">
                                            <i class="bi bi-plus-circle"></i> Adicionar Detalhes do Produto Digital
                                        </a>
                                        <div class="form-text mt-2">
                                            Clique no botão acima para adicionar o arquivo digital e configurar as opções de download.
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    Salve o produto primeiro para adicionar os detalhes do produto digital.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="categories" class="form-label">Categorias do Produto</label>
                <select class="form-select" id="categories" name="categories[]" multiple size="5"> <!-- Changed name, added multiple and size -->
                    <?php
                    $categories = function_exists('getAllCategories') ? getAllCategories() : [];
                    if (empty($categories)) {
                        echo '<option value="" disabled>Nenhuma categoria encontrada. Crie uma primeiro.</option>';
                    } else {
                        foreach ($categories as $category):

                            $is_selected = in_array($category['id'], $product_category_ids);
                    ?>
                        <option value="<?php echo $category['id']; ?>" <?= $is_selected ? 'selected' : '' ?>>
                            <?php echo htmlspecialchars($category['name']); ?>
                        </option>
                    <?php
                        endforeach;
                    }
                    ?>
                </select>
                <div class="form-text">Selecione uma ou mais categorias (use Ctrl+Click ou Shift+Click).</div>
            </div>
        </div>
    </div>

    <!-- Attributes Selection -->
    <div class="card mb-4 variable-product-section" style="<?= empty($variations_data) ? 'display: none;' : '' ?>">
        <div class="card-header">Atributos do Produto</div>
        <div class="card-body">
            <div class="mb-3">
                <label class="form-label">Selecione os Atributos para este Produto:</label>
                <div id="attribute-selector-container">
                    <?php if (!empty($all_attributes)): ?>
                        <?php foreach ($all_attributes as $attribute): ?>
                            <?php $is_checked = isset($product_attributes_used[$attribute['id']]); ?>
                            <div class="form-check mb-2">
                                <!-- Checkbox is NOT submitted, only used for JS control -->
                                <input class="form-check-input attribute-selector" type="checkbox" value="<?= $attribute['id'] ?>" id="attr_<?= $attribute['id'] ?>" data-attribute-name="<?= sanitize_input($attribute['name_pt']) ?>" <?= $is_checked ? 'checked' : '' ?>>
                                <label class="form-check-label" for="attr_<?= $attribute['id'] ?>">
                                    <?= sanitize_input($attribute['name_pt']) ?>
                                </label>
                                <!-- Container for attribute values, shown when attribute is selected -->
                                <!-- Container for attribute values checkboxes, shown when attribute is selected -->
                                <div class="attribute-values-container mt-2" id="values_for_attr_<?= $attribute['id'] ?>" style="<?= $is_checked ? 'display: block;' : 'display: none;' ?> padding-left: 20px;">
                                    <label class="form-label small mb-1">Valores disponíveis para <?= sanitize_input($attribute['name_pt']) ?>:</label>
                                    <div class="attribute-values-checkboxes border p-2 rounded" style="max-height: 150px; overflow-y: auto;">
                                        <!-- Checkboxes will be loaded here by JS -->
                                        <span class="text-muted small">A carregar valores...</span>
                                    </div>
                                    <div class="form-text small mt-1">Selecione os valores desejados. Estes valores vêm da <a href="admin.php?section=attributes&action=values&attribute_id=<?= $attribute['id'] ?>&<?= get_session_id_param() ?>" target="_blank">gestão de valores</a>.</div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p>Nenhum atributo definido. <a href="admin.php?section=attributes&action=new&<?= get_session_id_param() ?>">Adicionar Atributo</a></p>
                    <?php endif; ?>
                </div>
            </div>
            <button type="button" id="generate-variations-btn" class="btn btn-secondary btn-sm">Gerar Variações</button>
             <div class="form-text">Selecione os atributos e insira os seus valores (que já devem existir na gestão de atributos). Depois clique em "Gerar Variações".</div>
        </div>
    </div>

    <!-- Variations Management -->
    <div class="card mb-4 variable-product-section" style="<?= empty($variations_data) ? 'display: none;' : '' ?>">
        <div class="card-header">Variações Geradas</div>
        <div class="card-body">
            <div id="variations-container">
                <?php if ($is_editing && !empty($variations_data)): ?>
                    <p>Variações existentes:</p>
                    <?php foreach ($variations_data as $variation_id => $variation): ?>
                        <div class="variation-item border rounded p-3 mb-3" data-variation-id="<?= $variation_id ?>">
                            <h6>Variação:
                                <?php
                                $attr_parts = [];
                                foreach ($variation['attributes'] as $attr_name => $attr_details) {
                                    $attr_parts[] = sanitize_input($attr_name) . ': ' . sanitize_input($attr_details['value_name']);
                                }
                                echo implode(', ', $attr_parts);
                                ?>
                            </h6>
                            <input type="hidden" name="variations[<?= $variation_id ?>][id]" value="<?= $variation_id ?>">
                            <?php foreach ($variation['attributes'] as $attr_name => $attr_details): ?>
                                <!-- Pass attribute VALUE ID to the backend -->
                                <input type="hidden" name="variations[<?= $variation_id ?>][attributes][<?= $attr_details['value_id'] ?>]" value="<?= $attr_details['value_id'] ?>">
                            <?php endforeach; ?>

                            <div class="row g-3">
                                <div class="col-md-6 col-lg-3">
                                    <label class="form-label small">SKU (10 caracteres)</label>
                                    <input type="text" class="form-control form-control-sm" name="variations[<?= $variation_id ?>][sku]" value="<?= sanitize_input($variation['sku'] ?? '') ?>" maxlength="10">
                                    <small class="form-text text-muted">Primeiros 5: Base, Últimos 5: Identificador único</small>
                                </div>
                                <div class="col-md-6 col-lg-3">
                                    <label class="form-label small">Modificador Preço (Opcional)</label>
                                    <!-- Allow override. Backend needs to handle saving this. -->
                                    <input type="number" step="0.01" class="form-control form-control-sm" name="variations[<?= $variation_id ?>][price_modifier_override]" value="<?= $variation['price_modifier_override'] !== null ? sanitize_input($variation['price_modifier_override']) : '' ?>" placeholder="<?= format_price($variation['calculated_modifier']) ?>">
                                    <small class="form-text text-muted">Substitui a soma dos modificadores. Deixe em branco para usar o valor calculado (<?= format_price($variation['calculated_modifier']) ?>).</small>
                                </div>
                                <div class="col-md-6 col-lg-2">
                                    <label class="form-label small">Stock *</label>
                                    <input type="number" step="1" class="form-control form-control-sm" name="variations[<?= $variation_id ?>][stock]" value="<?= sanitize_input($variation['stock'] ?? '0') ?>" required>
                                </div>
                                <div class="col-md-6 col-lg-2">
                                    <label class="form-label small">Peso (g)</label>
                                    <input type="number" step="1" class="form-control form-control-sm" name="variations[<?= $variation_id ?>][weight]" value="<?= sanitize_input($variation['weight'] ?? '') ?>" placeholder="ex: 150">
                                </div>
                                <!-- Dimensions Removed -->
                                <div class="col-md-6 col-lg-2">
                                     <label class="form-label small">Ativo</label>
                                     <select class="form-select form-select-sm" name="variations[<?= $variation_id ?>][is_active]">
                                         <option value="1" <?= (($variation['is_active'] ?? 1) == 1) ? 'selected' : '' ?>>Sim</option>
                                         <option value="0" <?= (($variation['is_active'] ?? 1) == 0) ? 'selected' : '' ?>>Não</option>
                                     </select>
                                 </div>
                                <div class="col-12 text-end">
                                    <button type="button" class="btn btn-danger btn-sm remove-variation-btn">Remover Variação</button>
                                    <!-- Add hidden input for deletion flag -->
                                    <input type="hidden" name="variations[<?= $variation_id ?>][delete]" class="delete-flag" value="0">
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                     <p class="mt-3 form-text">Pode gerar novas variações abaixo ou modificar as existentes.</p>
                     <hr>
                <?php endif; ?>
                <p id="no-variations-message" <?= ($is_editing && !empty($variations_data)) ? 'style="display: none;"' : '' ?>>Nenhuma variação gerada. Selecione atributos, insira valores e clique em "Gerar Variações".</p>
                <!-- Variations will be dynamically added here by JS -->
            </div>
        </div>
    </div>

    <!-- Images Section Placeholder -->
    <!-- Product Information Fields Section -->
    <div class="card mb-4">
        <div class="card-header">Informações Adicionais do Produto</div>
        <div class="card-body">
            <p class="form-text mb-3">Adicione informações adicionais que serão exibidas na página do produto (como informações de envio, política de devolução, etc.)</p>

            <div id="product-info-fields-container">
                <!-- Existing fields will be loaded here when editing -->
                <?php if ($is_editing): ?>
                <?php

                require_once __DIR__ . '/../../includes/product_info_fields.php';


                $product_info_fields = get_product_info_fields($item_id);
                if (!empty($product_info_fields)):
                    foreach ($product_info_fields as $field):
                ?>
                <div class="product-info-field row mb-2 align-items-center" data-field-id="<?= $field['id'] ?>">
                    <div class="col-md-3">
                        <input type="text" class="form-control info-field-icon" name="info_fields[<?= $field['id'] ?>][icon]" value="<?= sanitize_input($field['icon']) ?>" placeholder="ri-truck-line">
                    </div>
                    <div class="col-md-7">
                        <input type="text" class="form-control info-field-text" name="info_fields[<?= $field['id'] ?>][text]" value="<?= sanitize_input($field['text']) ?>" placeholder="Texto da informação">
                    </div>
                    <div class="col-md-2 text-center">
                        <button type="button" class="btn btn-danger btn-sm remove-info-field" title="Remover campo">
                            <i class="ri-delete-bin-line"></i>
                        </button>
                        <input type="hidden" name="info_fields[<?= $field['id'] ?>][id]" value="<?= $field['id'] ?>">
                        <input type="hidden" class="info-field-delete" name="info_fields[<?= $field['id'] ?>][delete]" value="0">
                    </div>
                </div>
                <?php
                    endforeach;
                endif;
                ?>
                <?php endif; ?>
            </div>

            <div class="mt-3">
                <button type="button" id="add-info-field-btn" class="btn btn-secondary btn-sm">Adicionar Campo</button>
            </div>
        </div>
    </div>

    <!-- Custom Fields Section -->
    <div class="card mb-4">
        <div class="card-header">Campos Personalizados</div>
        <div class="card-body">
            <div class="alert alert-info mb-3">
                <p class="mb-0"><strong>Nota:</strong> Os campos marcados como <span class="badge bg-danger">Sim</span> na coluna "Obrigatório" serão exibidos com um asterisco vermelho (*) na página do produto, indicando ao cliente que são de preenchimento obrigatório.</p>
            </div>

            <?php

            require_once __DIR__ . '/../../includes/custom_field_functions.php';


            $all_custom_fields = get_custom_fields();


            $product_custom_fields = [];
            if ($is_editing) {
                $product_custom_fields = get_product_custom_fields($item_id);

                $product_custom_field_ids = [];
                foreach ($product_custom_fields as $field) {
                    $product_custom_field_ids[$field['custom_field_id']] = $field;
                }
            }
            ?>

            <p class="form-text mb-3">Selecione os campos personalizados que os compradores poderão preencher ao comprar este produto.</p>

            <?php if (empty($all_custom_fields)): ?>
                <div class="alert alert-info">
                    Nenhum campo personalizado definido. <a href="admin.php?section=custom_fields&action=new&<?= get_session_id_param() ?>">Criar um novo campo personalizado</a>.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th style="width: 50px;">Usar</th>
                                <th>Nome do Campo</th>
                                <th>Tipo</th>
                                <th>Obrigatório</th>
                                <th>Modificador de Preço</th>
                                <th>Modificador Personalizado</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($all_custom_fields as $field): ?>
                                <?php
                                $is_used = $is_editing && isset($product_custom_field_ids[$field['id']]);
                                $price_modifier_override = $is_used ? $product_custom_field_ids[$field['id']]['price_modifier_override'] : null;
                                ?>
                                <tr>
                                    <td>
                                        <div class="form-check">
                                            <input class="form-check-input custom-field-selector" type="checkbox"
                                                   id="custom_field_<?= $field['id'] ?>"
                                                   name="custom_fields[<?= $field['id'] ?>][use]"
                                                   value="1"
                                                   <?= $is_used ? 'checked' : '' ?>>
                                        </div>
                                    </td>
                                    <td>
                                        <label for="custom_field_<?= $field['id'] ?>" class="form-check-label">
                                            <?= sanitize_input($field['name']) ?>
                                        </label>
                                        <?php if (!empty($field['description'])): ?>
                                            <div class="form-text small"><?= sanitize_input($field['description']) ?></div>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= sanitize_input($field['type_name']) ?></td>
                                    <td>
                                        <?php if ($field['is_required']): ?>
                                            <span class="badge bg-danger">Sim</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Não</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= format_price($field['price_modifier']) ?></td>
                                    <td>
                                        <input type="number" step="0.01" class="form-control form-control-sm"
                                               name="custom_fields[<?= $field['id'] ?>][price_modifier_override]"
                                               value="<?= $price_modifier_override !== null ? sanitize_input($price_modifier_override) : '' ?>"
                                               placeholder="Opcional"
                                               <?= !$is_used ? 'disabled' : '' ?>>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <div class="form-text">
                    Os campos selecionados serão exibidos na página do produto para que os compradores possam personalizá-los.
                    Os campos marcados como obrigatórios terão um asterisco vermelho (*) e deverão ser preenchidos pelo cliente.
                    Você pode substituir o modificador de preço padrão para cada campo, se necessário.
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- SEO and Social Media Section -->
    <div class="card mb-4">
        <div class="card-header">SEO e Redes Sociais</div>
        <div class="card-body">
            <div class="alert alert-info mb-3">
                <p class="mb-0"><strong>Nota:</strong> Estes campos são usados para otimização de motores de busca (SEO) e partilha em redes sociais. Se deixados em branco, serão usados os valores padrão baseados no nome e descrição do produto.</p>
            </div>

            <div class="row mb-3">
                <div class="col-12 text-end">
                    <button type="button" id="prefill-seo-btn" class="btn btn-secondary btn-sm">Preencher Automaticamente</button>
                </div>
            </div>

            <!-- SEO Fields -->
            <h5 class="mb-3">Campos SEO</h5>
            <div class="row mb-3">
                <div class="col-md-6 mb-3">
                    <label for="seo_title" class="form-label">Título SEO</label>
                    <input type="text" class="form-control" id="seo_title" name="seo_title" value="<?= sanitize_input($product_data['seo_title'] ?? '') ?>">
                    <div class="form-text">Título otimizado para motores de busca. Recomendado: 50-60 caracteres.</div>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="seo_keywords" class="form-label">Palavras-chave SEO</label>
                    <input type="text" class="form-control" id="seo_keywords" name="seo_keywords" value="<?= sanitize_input($product_data['seo_keywords'] ?? '') ?>">
                    <div class="form-text">Palavras-chave separadas por vírgulas. Recomendado: 5-8 palavras-chave.</div>
                </div>
            </div>
            <div class="mb-3">
                <label for="seo_description" class="form-label">Descrição SEO</label>
                <textarea class="form-control" id="seo_description" name="seo_description" rows="2"><?= sanitize_input($product_data['seo_description'] ?? '') ?></textarea>
                <div class="form-text">Descrição curta para motores de busca. Recomendado: 150-160 caracteres.</div>
            </div>

            <!-- Open Graph (Facebook, etc.) -->
            <h5 class="mb-3 mt-4">Open Graph (Facebook, LinkedIn, etc.)</h5>
            <div class="row mb-3">
                <div class="col-md-6 mb-3">
                    <label for="og_title" class="form-label">Título Open Graph</label>
                    <input type="text" class="form-control" id="og_title" name="og_title" value="<?= sanitize_input($product_data['og_title'] ?? '') ?>">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="og_image" class="form-label">Imagem Open Graph</label>
                    <input type="text" class="form-control" id="og_image" name="og_image" value="<?= sanitize_input($product_data['og_image'] ?? '') ?>">
                    <div class="form-text">URL da imagem para partilha. Se vazio, será usada a imagem principal do produto.</div>
                </div>
            </div>
            <div class="mb-3">
                <label for="og_description" class="form-label">Descrição Open Graph</label>
                <textarea class="form-control" id="og_description" name="og_description" rows="2"><?= sanitize_input($product_data['og_description'] ?? '') ?></textarea>
            </div>

            <!-- Twitter Card -->
            <h5 class="mb-3 mt-4">Twitter Card</h5>
            <div class="row mb-3">
                <div class="col-md-6 mb-3">
                    <label for="twitter_card" class="form-label">Tipo de Card</label>
                    <select class="form-select" id="twitter_card" name="twitter_card">
                        <option value="summary" <?= ($product_data['twitter_card'] ?? '') === 'summary' ? 'selected' : '' ?>>Summary</option>
                        <option value="summary_large_image" <?= ($product_data['twitter_card'] ?? 'summary_large_image') === 'summary_large_image' ? 'selected' : '' ?>>Summary Large Image</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="twitter_image" class="form-label">Imagem Twitter</label>
                    <input type="text" class="form-control" id="twitter_image" name="twitter_image" value="<?= sanitize_input($product_data['twitter_image'] ?? '') ?>">
                    <div class="form-text">URL da imagem para Twitter. Se vazio, será usada a imagem principal do produto.</div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-md-6 mb-3">
                    <label for="twitter_title" class="form-label">Título Twitter</label>
                    <input type="text" class="form-control" id="twitter_title" name="twitter_title" value="<?= sanitize_input($product_data['twitter_title'] ?? '') ?>">
                </div>
                <div class="col-md-6 mb-3">
                    <label for="twitter_description" class="form-label">Descrição Twitter</label>
                    <input type="text" class="form-control" id="twitter_description" name="twitter_description" value="<?= sanitize_input($product_data['twitter_description'] ?? '') ?>">
                </div>
            </div>
        </div>
    </div>

    <!-- Images Section -->
    <div class="card mb-4">
        <div class="card-header">Imagens</div>
        <div class="card-body">
            <!-- Display Existing Images -->
            <?php if ($is_editing && !empty($product_images)): ?>
                <h5>Imagens Atuais</h5>
                <div class="alert alert-info mb-3">
                    <p class="mb-0"><i class="bi bi-info-circle"></i> Arraste as imagens para reordenar como elas aparecerão na loja. A primeira imagem será a principal.</p>
                </div>
                <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 row-cols-xl-5 g-3 mb-3" id="existing-images-container">
                    <?php foreach ($product_images as $image): ?>
                        <div class="col existing-image-item" id="image-<?= $image['id'] ?>">
                            <div class="card h-100 rounded shadow-sm">
                                <?php if (($image['is_default'] ?? 0) == 1): ?>
                                <div class="default-image-indicator">Padrão</div>
                                <?php endif; ?>
                                <img src="<?= get_product_image_url($image['filename']) ?>" class="card-img-top rounded" alt="Imagem Produto" style="max-height: 100px; width: 100%; object-fit: contain; padding: 8px;">
                                <div class="card-body p-2 text-center">
                                    <small class="text-muted d-block mb-1" style="word-break: break-all;"><?= sanitize_input($image['filename']) ?></small>
                                    <div class="d-flex justify-content-center gap-3"> <!-- Container for both controls -->
                                        <div class="form-check form-check-inline"> <!-- Radio button for default -->
                                            <input class="form-check-input" type="radio" name="default_image_id" value="<?= $image['id'] ?>" id="default_img_<?= $image['id'] ?>" <?= (($image['is_default'] ?? 0) == 1) ? 'checked' : '' ?>>
                                            <label class="form-check-label small" for="default_img_<?= $image['id'] ?>">Padrão</label>
                                        </div>
                                        <div class="form-check form-check-inline"> <!-- Checkbox for removal -->
                                            <input class="form-check-input" type="checkbox" name="delete_images[]" value="<?= $image['id'] ?>" id="delete_img_<?= $image['id'] ?>">
                                            <label class="form-check-label small" for="delete_img_<?= $image['id'] ?>">Remover</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <hr>
            <?php endif; ?>

            <!-- Upload New Images -->
            <div class="mb-3">
                <label for="product_images" class="form-label">Carregar Novas Imagens</label>
                <input class="form-control" type="file" id="product_images" name="product_images[]" multiple accept="image/jpeg,image/png,image/gif,image/webp">
                <div class="form-text">Pode selecionar várias imagens (JPG, PNG, GIF, WEBP). Máx 5MB por ficheiro. As novas imagens serão adicionadas às existentes (a menos que marque as existentes para remover).</div>
            </div>
        </div>
    </div>

    <!-- Videos Section -->
    <div class="card mb-4">
        <div class="card-header">Vídeos</div>
        <div class="card-body">
            <!-- Display Existing Videos -->
            <?php

            require_once __DIR__ . '/../../includes/product_video_functions.php';


            $product_videos = [];
            if ($is_editing) {
                $product_videos = get_product_videos($item_id);
            }

            if ($is_editing && !empty($product_videos)):
            ?>
                <h5>Vídeos Atuais</h5>
                <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 row-cols-xl-5 g-3 mb-3" id="existing-videos-container">
                    <?php foreach ($product_videos as $video): ?>
                        <div class="col existing-video-item" id="video-<?= $video['id'] ?>">
                            <div class="card h-100 rounded shadow-sm">
                                <?php if ($video['video_type'] === 'uploaded'): ?>
                                    <div class="position-relative" style="height: 100px;">
                                        <?php if (!empty($video['thumbnail_filename'])): ?>
                                            <img src="<?= get_asset_url('images/video_thumbnails/' . $video['thumbnail_filename']) ?>" class="card-img-top rounded" alt="Thumbnail Vídeo" style="height: 100px; width: 100%; object-fit: contain; padding: 8px;">
                                        <?php else: ?>
                                            <div class="d-flex justify-content-center align-items-center bg-dark rounded-top" style="height: 100px;">
                                                <i class="ri-video-line text-light" style="font-size: 2rem;"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div class="position-absolute top-50 start-50 translate-middle">
                                            <i class="ri-play-circle-fill text-light" style="font-size: 2rem; text-shadow: 0 0 5px rgba(0,0,0,0.5);"></i>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <?php
                                    $youtube_id = extract_youtube_video_id($video['video_url']);
                                    $thumbnail_url = $youtube_id ? get_youtube_thumbnail_url($youtube_id) : get_asset_url('images/video_placeholder.png');
                                    ?>
                                    <div class="position-relative" style="height: 100px;">
                                        <img src="<?= $thumbnail_url ?>" class="card-img-top rounded" alt="Thumbnail Vídeo" style="height: 100px; width: 100%; object-fit: cover; padding: 8px;">
                                        <div class="position-absolute top-50 start-50 translate-middle">
                                            <i class="ri-play-circle-fill text-light" style="font-size: 2rem; text-shadow: 0 0 5px rgba(0,0,0,0.5);"></i>
                                        </div>
                                    </div>
                                <?php endif; ?>
                                <div class="card-body p-2 text-center">
                                    <small class="text-muted d-block mb-1" style="word-break: break-all;">
                                        <?php if ($video['video_type'] === 'uploaded'): ?>
                                            <?= sanitize_input($video['filename']) ?>
                                        <?php else: ?>
                                            <?= sanitize_input($video['video_url']) ?>
                                        <?php endif; ?>
                                    </small>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" name="delete_videos[]" value="<?= $video['id'] ?>" id="delete_video_<?= $video['id'] ?>">
                                        <label class="form-check-label small" for="delete_video_<?= $video['id'] ?>">Remover</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <hr>
            <?php endif; ?>

            <!-- Upload New Video -->
            <div class="mb-3">
                <label for="product_video" class="form-label">Carregar Novo Vídeo</label>
                <input class="form-control" type="file" id="product_video" name="product_video" accept="video/mp4,video/webm,video/ogg">
                <div class="form-text">Formatos suportados: MP4, WEBM, OGG. Máx 50MB por ficheiro.</div>
            </div>

            <!-- OR Separator -->
            <div class="d-flex align-items-center my-3">
                <div class="flex-grow-1 border-bottom"></div>
                <div class="mx-3 text-muted">OU</div>
                <div class="flex-grow-1 border-bottom"></div>
            </div>

            <!-- External Video URL -->
            <div class="mb-3">
                <label for="video_url" class="form-label">URL de Vídeo Externo</label>
                <input type="url" class="form-control" id="video_url" name="video_url" placeholder="https://www.youtube.com/watch?v=...">
                <div class="form-text">Insira um URL do YouTube. Outros serviços de vídeo serão suportados no futuro.</div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <button type="submit" name="save_action" value="save_and_return" class="btn btn-primary">Guardar Produto & Regressar ao Menu</button>
        <button type="submit" name="save_action" value="save_and_continue" class="btn btn-success">Guardar Produto</button>
        <a href="admin.php?section=products&<?= get_session_id_param() ?>" class="btn btn-secondary">Cancelar</a>
    </div>

</form>

<!-- Add CSS for icon selector and product images -->
<link href="<?= get_asset_url('css/admin-icon-selector.css') ?>" rel="stylesheet">
<link href="<?= get_asset_url('css/admin-product-images.css') ?>" rel="stylesheet">

<!-- Add JS for product info fields -->
<script src="<?= get_asset_url('js/bootstrap-dropdown-fix.js') ?>"></script>
<script src="<?= get_asset_url('js/admin-icon-selector.js') ?>"></script>
<script src="<?= get_asset_url('js/admin-product-info-fields.js') ?>"></script>
<script src="<?= get_asset_url('js/admin-product-images.js') ?>"></script>

<!-- Add JS for SEO fields auto-fill -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // SEO Auto-fill Button
    const prefillSeoBtn = document.getElementById('prefill-seo-btn');
    const nameField = document.getElementById('name_pt');
    const descriptionField = document.getElementById('description_pt');

    if (prefillSeoBtn && nameField) {
        prefillSeoBtn.addEventListener('click', function() {
            // Get product name and description
            const title = nameField.value.trim();

            // Get plain text from description (which might be in a Summernote editor)
            let description = '';
            if (descriptionField) {
                // Check if Summernote is active on this element
                if ($(descriptionField).data('summernote')) {
                    description = $(descriptionField).summernote('code');
                    // Convert HTML to plain text
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = description;
                    description = tempDiv.textContent || tempDiv.innerText || '';
                } else {
                    description = descriptionField.value;
                }
            }

            // Trim and limit description length
            const shortDescription = description.trim().substring(0, 160);

            // Extract keywords from title and description
            const allText = (title + ' ' + description).toLowerCase();
            const words = allText.split(/\s+/)
                .filter(word => word.length > 3) // Only words longer than 3 chars
                .filter(word => !['para', 'como', 'este', 'esta', 'isso', 'aqui', 'mais', 'pode', 'deve'].includes(word)); // Filter common words

            // Get unique words and take up to 8 for keywords
            const uniqueWords = [...new Set(words)];
            const keywords = uniqueWords.slice(0, 8).join(', ');

            // Fill SEO fields
            const fields = {
                'seo_title': title,
                'seo_description': shortDescription,
                'seo_keywords': keywords,
                'og_title': title,
                'og_description': shortDescription,
                'twitter_title': title,
                'twitter_description': shortDescription,
                'twitter_card': 'summary_large_image'
            };

            // Update all fields
            Object.keys(fields).forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    if (element.tagName === 'SELECT') {
                        // For select elements, find and select the option
                        const option = Array.from(element.options).find(opt => opt.value === fields[id]);
                        if (option) option.selected = true;
                    } else {
                        // For input and textarea elements
                        element.value = fields[id];
                    }
                }
            });

            // Show success message
            alert('Campos SEO e Social preenchidos automaticamente com sucesso!');
        });
    }
});
</script>

<!-- Add JS for custom fields -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle custom field checkboxes
    const customFieldSelectors = document.querySelectorAll('.custom-field-selector');

    customFieldSelectors.forEach(checkbox => {
        const row = checkbox.closest('tr');
        const priceModifierInput = row.querySelector('input[name*="price_modifier_override"]');

        // Initial state
        if (priceModifierInput) {
            priceModifierInput.disabled = !checkbox.checked;
        }

        // Handle checkbox change
        checkbox.addEventListener('change', function() {
            if (priceModifierInput) {
                priceModifierInput.disabled = !this.checked;

                // Clear the value if unchecked
                if (!this.checked) {
                    priceModifierInput.value = '';

                    // Ensure the hidden 'use' field is properly set when unchecked
                    const fieldId = this.id.replace('custom_field_', '');
                    const hiddenField = document.querySelector(`input[type="hidden"][name="custom_fields[${fieldId}][use]"]`);

                    // If there's no hidden field, create one to explicitly set use=0
                    if (!hiddenField) {
                        const hiddenInput = document.createElement('input');
                        hiddenInput.type = 'hidden';
                        hiddenInput.name = `custom_fields[${fieldId}][use]`;
                        hiddenInput.value = '0';
                        row.appendChild(hiddenInput);
                    }
                }
            }
        });
    });
});
</script>

<!-- Add JS for slug generation, variation management etc. later -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Product Type Toggle
    const productTypeSimple = document.getElementById('product_type_simple');
    const productTypeVariable = document.getElementById('product_type_variable');
    const productTypeDigital = document.getElementById('product_type_digital');
    const simpleProductFields = document.getElementById('simple-product-fields');
    const digitalProductFields = document.getElementById('digital-product-fields');
    const variableProductSections = document.querySelectorAll('.variable-product-section');
    const simpleSkuField = document.getElementById('simple_sku');
    const simpleStockField = document.getElementById('simple_stock');
    const digitalSkuField = document.getElementById('digital_sku');
    const digitalFileField = document.getElementById('digital_file');
    const digitalExpiryDaysField = document.getElementById('digital_expiry_days');
    const digitalDownloadLimitField = document.getElementById('digital_download_limit');

    // Function to toggle product type sections
    function toggleProductType() {
        if (productTypeSimple.checked) {
            // Show simple product fields, hide variable product sections and digital product fields
            simpleProductFields.style.display = 'block';
            digitalProductFields.style.display = 'none';
            variableProductSections.forEach(section => {
                section.style.display = 'none';
            });

            // Set required fields
            simpleSkuField.required = true;
            simpleStockField.required = true;

            // Unset digital fields required
            if (digitalSkuField) digitalSkuField.required = false;

        } else if (productTypeVariable.checked) {
            // Hide simple product fields and digital product fields, show variable product sections
            simpleProductFields.style.display = 'none';
            digitalProductFields.style.display = 'none';
            variableProductSections.forEach(section => {
                section.style.display = 'block';
            });

            // Unset required fields
            simpleSkuField.required = false;
            simpleStockField.required = false;

            // Unset digital fields required
            if (digitalSkuField) digitalSkuField.required = false;
            if (digitalFileField) digitalFileField.required = false;
            if (digitalExpiryDaysField) digitalExpiryDaysField.required = false;
            if (digitalDownloadLimitField) digitalDownloadLimitField.required = false;

        } else if (productTypeDigital.checked) {
            // Hide simple product fields and variable product sections, show digital product fields
            simpleProductFields.style.display = 'none';
            digitalProductFields.style.display = 'block';
            variableProductSections.forEach(section => {
                section.style.display = 'none';
            });

            // Unset simple fields required
            simpleSkuField.required = false;
            simpleStockField.required = false;

            // Set digital fields required
            if (digitalSkuField) digitalSkuField.required = true;
        }
    }

    // Add event listeners to product type radio buttons
    if (productTypeSimple && productTypeVariable && productTypeDigital) {
        productTypeSimple.addEventListener('change', toggleProductType);
        productTypeVariable.addEventListener('change', toggleProductType);
        productTypeDigital.addEventListener('change', toggleProductType);

        // Initial toggle based on selected type
        toggleProductType();
    }

    const attributeSelectors = document.querySelectorAll('.attribute-selector');
    const valuesContainers = document.querySelectorAll('.attribute-values-container');
    const generateBtn = document.getElementById('generate-variations-btn');
    const variationsContainer = document.getElementById('variations-container');
    const noVariationsMsg = document.getElementById('no-variations-message');
    const currencySymbol = '<?= get_setting('currency_symbol', '€') ?>';
    const existingVariationsData = <?= $is_editing && !empty($variations_data) ? json_encode($variations_data) : 'null' ?>;
    const productAttributesUsed = <?= json_encode($product_attributes_used) ?>; // Attribute IDs used by product

    // Helper function to format price for display
    function formatPriceForDisplay(amount) {
        return parseFloat(amount).toFixed(2).replace('.', ',') + ' ' + currencySymbol;
    }

    // Function to fetch and display attribute values as checkboxes
    async function loadAttributeValues(attributeId, containerElement, preselectValueIds = []) {
        const checkboxesContainer = containerElement.querySelector('.attribute-values-checkboxes');
        if (!checkboxesContainer) return;
        checkboxesContainer.innerHTML = '<span class="text-muted small">A carregar valores...</span>'; // Loading indicator

        try {
            // Construct URL with proper section and action parameters
            // Make sure to include the section and action as query parameters
            const url = `section=products&action=get_attribute_values&attribute_id=${attributeId}`;

            // Add X-Requested-With header to ensure it's recognized as an AJAX request
            const response = await adminAjaxRequest(url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (response.success && response.values && response.values.length > 0) {
                checkboxesContainer.innerHTML = ''; // Clear loading/previous content

                response.values.forEach(value => {
                    const isChecked = preselectValueIds.includes(value.id.toString()); // Check if this value should be pre-selected
                    const modifierText = value.price_modifier != 0 ? ` (${value.price_modifier > 0 ? '+' : ''}${formatPriceForDisplay(value.price_modifier)})` : '';
                    const checkboxId = `attr_${attributeId}_val_${value.id}`;

                    const div = document.createElement('div');
                    div.className = 'form-check form-check-sm';
                    div.innerHTML = `
                        <input class="form-check-input attribute-value-selector" type="checkbox" value="${value.id}" id="${checkboxId}" data-value-name="${escapeHtml(value.value_pt)}" data-modifier="${value.price_modifier}" ${isChecked ? 'checked' : ''}>
                        <label class="form-check-label" for="${checkboxId}">
                            ${escapeHtml(value.value_pt)}${modifierText}
                        </label>
                    `;
                    checkboxesContainer.appendChild(div);
                });
            } else if (response.success && response.values && response.values.length === 0) {
                checkboxesContainer.innerHTML = '<span class="text-muted small">Nenhum valor definido para este atributo.</span>';
            } else {
                console.error(`Error in response for attribute ${attributeId}:`, response);
                checkboxesContainer.innerHTML = `<span class="text-danger small">Erro ao carregar valores: ${response.message || 'Erro desconhecido'}</span>`;
            }
        } catch (error) {
            console.error(`Error loading attribute values for attribute ${attributeId}:`, error);
            checkboxesContainer.innerHTML = `<span class="text-danger small">Erro de comunicação ao carregar valores: ${error.message}</span>`;
        }
    }

    // Get preselected value IDs for editing
    const preselectedValuesMap = {}; // { attributeId: [valueId1, valueId2], ... }
    if (existingVariationsData) {
        Object.values(existingVariationsData).forEach(variation => {
            if (variation.attributes) {
                Object.values(variation.attributes).forEach(attrDetail => {
                    // Find the attribute ID for this value ID (needs improvement if structure changes)
                    const attrEntry = Object.entries(productAttributesUsed).find(([attrId, used]) => {
                         // This is a bit fragile, relies on the structure from PHP query
                         return Object.values(variation.attributes).some(v => v.value_id == attrDetail.value_id);
                    });
                    if (attrEntry) {
                        const attributeId = attrEntry[0];
                        if (!preselectedValuesMap[attributeId]) {
                            preselectedValuesMap[attributeId] = new Set();
                        }
                        preselectedValuesMap[attributeId].add(attrDetail.value_id.toString());
                    }
                });
            }
        });
        // Convert Sets to Arrays
        for (const attrId in preselectedValuesMap) {
            preselectedValuesMap[attrId] = Array.from(preselectedValuesMap[attrId]);
        }
    }

    // Setup attribute selectors
    attributeSelectors.forEach(checkbox => {
        const attributeId = checkbox.value;
        const valuesContainer = document.getElementById(`values_for_attr_${attributeId}`);

        if (valuesContainer) {
            // Event listener for attribute checkbox change
            checkbox.addEventListener('change', function() {
                valuesContainer.style.display = this.checked ? 'block' : 'none';
                const checkboxesDiv = valuesContainer.querySelector('.attribute-values-checkboxes');

                if (this.checked && checkboxesDiv && checkboxesDiv.children.length <= 1) { // Load only if checked and not already loaded (or only has placeholder)
                    const preselectIds = preselectedValuesMap[attributeId] || [];
                    loadAttributeValues(attributeId, valuesContainer, preselectIds);
                } else if (!this.checked && checkboxesDiv) {
                    // Optional: Clear checkboxes when attribute is deselected? Or just hide? Hiding is simpler.
                    // checkboxesDiv.innerHTML = '<span class="text-muted small">A carregar valores...</span>'; // Reset if needed
                }
            });

            // Trigger initial load if checkbox is already checked (on edit page load)
            if (checkbox.checked) {
                 const preselectIds = preselectedValuesMap[attributeId] || [];
                 loadAttributeValues(attributeId, valuesContainer, preselectIds);
            }
        }
    });

     // Helper function for AJAX requests within admin context
     async function adminAjaxRequest(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: { 'Content-Type': 'application/json', 'X-Requested-With': 'XMLHttpRequest' },
        };
        const mergedOptions = { ...defaultOptions, ...options };
        if (options.headers) mergedOptions.headers = { ...defaultOptions.headers, ...options.headers };

        // Fix URL construction - ensure proper handling of query parameters
        let fullUrl;
        if (url.includes('?')) {
            // If URL already has query parameters, append session ID with &
            fullUrl = `admin.php?${url}&<?= get_session_id_param() ?>`;
        } else {
            // If URL has no query parameters, start with ?
            fullUrl = `admin.php?${url}&<?= get_session_id_param() ?>`;
        }

        try {
            const response = await fetch(fullUrl, mergedOptions);
            if (!response.ok) {
                console.error('AJAX request failed with status:', response.status);
                let errorMessage = `HTTP error! status: ${response.status}`;
                try {
                    const errorData = await response.json();
                    errorMessage = errorData?.message || errorMessage;
                    console.error('Error details:', errorData);
                } catch (jsonError) {
                    console.error('Could not parse error response as JSON:', jsonError);
                }
                throw new Error(errorMessage);
            }

            const responseData = await response.json();
            return responseData;
        } catch (error) {
            console.error('Admin AJAX Error:', error, 'URL:', fullUrl);
            throw error;
        }
    }

    // Generate Variations Button Click (Async function now)
    generateBtn.addEventListener('click', async function() {
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Gerando...';

        const attributesToProcess = []; // Array of { id: attributeId, name: attributeName, selectedValues: [{id, name, modifier}, ...] }
        let hasSelectionError = false;

        // Collect selected attributes and their selected values from checkboxes
        document.querySelectorAll('.attribute-selector:checked').forEach(attrCheckbox => {
            if (hasSelectionError) return;
            const attributeId = attrCheckbox.value;
            const attributeName = attrCheckbox.dataset.attributeName;
            const valuesContainer = document.getElementById(`values_for_attr_${attributeId}`);
            const selectedValueCheckboxes = valuesContainer.querySelectorAll('.attribute-value-selector:checked');

            if (selectedValueCheckboxes.length > 0) {
                const selectedValuesData = Array.from(selectedValueCheckboxes).map(cb => ({
                    id: cb.value,
                    name: cb.dataset.valueName,
                    modifier: parseFloat(cb.dataset.modifier || '0') // Get modifier from data attribute
                }));
                attributesToProcess.push({
                    id: attributeId,
                    name: attributeName,
                    selectedValues: selectedValuesData
                });
            } else {
                alert(`Por favor, selecione pelo menos um valor para o atributo "${attributeName}".`);
                hasSelectionError = true;
            }
        });

        if (hasSelectionError || attributesToProcess.length === 0) {
            if (!hasSelectionError && attributesToProcess.length === 0) {
                alert('Selecione pelo menos um atributo e pelo menos um valor para cada atributo selecionado para gerar variações.');
            }
            generateBtn.disabled = false;
            generateBtn.innerHTML = 'Gerar Variações';
            return;
        }

        // No need for separate AJAX call here anymore, we have the data from checkboxes

        // Prepare data for combination generation using the collected selectedValues
        const attributesForCombination = attributesToProcess.map(attr => {
             // Map selectedValues to the format needed by generateCombinations
             // { name: valueName, id: valueId, modifier: valueModifier }
            return attr.selectedValues.map(val => ({
                name: val.name,
                id: val.id,
                modifier: val.modifier
            }));
        });

        // Generate Cartesian product
        const combinations = generateCombinations(attributesForCombination);

        if (combinations.length === 0) {
            alert('Não foi possível gerar combinações.');
             generateBtn.disabled = false;
             generateBtn.innerHTML = 'Gerar Variações';
            return;
        }

        noVariationsMsg.style.display = 'none';

        combinations.forEach((combination, index) => {
            const uniqueId = `new_${Date.now()}_${index}`;
            const variationItem = document.createElement('div');
            variationItem.className = 'variation-item new-variation-item border rounded p-3 mb-3';

            let attributesHtml = '';
            let hiddenInputsHtml = '';
            let titleParts = [];
            let calculatedModifier = 0.0;

            combination.forEach((valueDetail, attrIndex) => {
                const attribute = attributesToProcess[attrIndex];
                hiddenInputsHtml += `<input type="hidden" name="variations[${uniqueId}][attributes][${valueDetail.id}]" value="${valueDetail.id}">`;
                titleParts.push(`${escapeHtml(attribute.name)}: ${escapeHtml(valueDetail.name)}`);
                calculatedModifier += parseFloat(valueDetail.modifier);
            });

            attributesHtml = titleParts.join(', ');

            variationItem.innerHTML = `
                <h6>Nova Variação: ${attributesHtml}</h6>
                ${hiddenInputsHtml}
                <input type="hidden" name="variations[${uniqueId}][is_new]" value="1">
                <div class="row g-3">
                    <div class="col-md-6 col-lg-3">
                        <label class="form-label small">SKU</label>
                        <input type="text" class="form-control form-control-sm" name="variations[${uniqueId}][sku]" value="">
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <label class="form-label small">Modificador Preço (Opcional)</label>
                        <input type="number" step="0.01" class="form-control form-control-sm" name="variations[${uniqueId}][price_modifier_override]" value="${calculatedModifier.toFixed(2)}" placeholder="${formatPriceForDisplay(calculatedModifier)}">
                        <small class="form-text text-muted">Substitui a soma dos modificadores. Deixe em branco para usar o valor calculado (${formatPriceForDisplay(calculatedModifier)}).</small>
                    </div>
                    <div class="col-md-6 col-lg-2">
                        <label class="form-label small">Stock *</label>
                        <input type="number" step="1" class="form-control form-control-sm" name="variations[${uniqueId}][stock]" value="0" required>
                    </div>
                    <div class="col-md-6 col-lg-2">
                        <label class="form-label small">Peso (g)</label>
                        <input type="number" step="1" class="form-control form-control-sm" name="variations[${uniqueId}][weight]" value="" placeholder="ex: 150">
                    </div>
                    <!-- Dimensions Removed -->
                    <div class="col-md-6 col-lg-2">
                         <label class="form-label small">Ativo</label>
                         <select class="form-select form-select-sm" name="variations[${uniqueId}][is_active]">
                             <option value="1" selected>Sim</option>
                             <option value="0">Não</option>
                         </select>
                     </div>
                    <div class="col-12 text-end">
                        <button type="button" class="btn btn-danger btn-sm remove-variation-btn">Remover Variação</button>
                        <input type="hidden" name="variations[${uniqueId}][delete]" class="delete-flag" value="0">
                    </div>
                </div>
            `;
            variationsContainer.appendChild(variationItem);
        });

        updateRemoveButtons();
        generateBtn.disabled = false;
        generateBtn.innerHTML = 'Gerar Variações';
    });

    // Function to generate Cartesian product
    function generateCombinations(arrays) {
        if (!arrays || arrays.length === 0) return [];
        return arrays.reduce((acc, current) => {
            const result = [];
            acc.forEach(a => { current.forEach(c => { result.push(a.concat([c])); }); });
            return result;
        }, [[]]);
    }

     // Function to add/update event listeners for remove buttons
    function updateRemoveButtons() {
        variationsContainer.querySelectorAll('.remove-variation-btn').forEach(button => {
            button.removeEventListener('click', handleRemoveVariation); // Prevent duplicates
            button.addEventListener('click', handleRemoveVariation);
        });
    }

    // Handler for removing/undeleting a variation
    function handleRemoveVariation(event) {
        // Find the closest variation item container
        const variationItem = event.target.closest('.variation-item');
        if (!variationItem) return;

        // Get the delete flag input element
        const deleteFlagInput = variationItem.querySelector('.delete-flag');

        // Check if this is an existing variation (has a data-variation-id attribute)
        // or a new variation (has class new-variation-item)
        const isExistingVariation = variationItem.hasAttribute('data-variation-id');
        const isNewVariation = variationItem.classList.contains('new-variation-item');

        // For existing variations with a delete flag input, toggle the delete flag
        if (isExistingVariation && deleteFlagInput) {
            if (deleteFlagInput.value === '1') { // Undo deletion
                deleteFlagInput.value = '0';
                variationItem.style.opacity = '1';
                variationItem.querySelectorAll('input:not([readonly]), select, .remove-variation-btn').forEach(el => el.disabled = false);
                event.target.classList.remove('btn-warning');
                event.target.classList.add('btn-danger');
                event.target.innerHTML = 'Remover Variação';
            } else { // Mark for deletion
                deleteFlagInput.value = '1';
                variationItem.style.opacity = '0.5';
                // Exclude the delete flag itself from being disabled
                variationItem.querySelectorAll('input:not([readonly]):not(.delete-flag), select').forEach(el => el.disabled = true);
                event.target.classList.remove('btn-danger');
                event.target.classList.add('btn-warning');
                event.target.innerHTML = '<i class="bi bi-arrow-counterclockwise"></i>'; // Undo icon
            }
        } else {
            // For new variations or any other case, just remove the element from the DOM
            variationItem.remove();
        }

        // Update visibility of "no variations" message
        const anyVisible = variationsContainer.querySelector('.variation-item:not([style*="opacity: 0.5"])');
        noVariationsMsg.style.display = anyVisible ? 'none' : 'block';
    }

    // Utility to escape HTML
    function escapeHtml(unsafe) {
        if (typeof unsafe !== 'string') return unsafe;
        return unsafe.replace(/&/g, "&").replace(/</g, "<").replace(/>/g, ">").replace(/"/g, "\"").replace(/'/g, "&#039;");
     }

    // Initial setup
    updateRemoveButtons();

});
</script>

<!-- Add our CSRF token fix script -->
<script src="<?= get_asset_url('js/admin-product-form-fix.js') ?>"></script>
