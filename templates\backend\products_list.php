<?php

$name_filter = isset($_GET['name']) ? trim($_GET['name']) : '';
$state_filter = isset($_GET['state']) ? $_GET['state'] : '';
$type_filter = isset($_GET['type']) ? $_GET['type'] : '';
$sort_option = isset($_GET['sort']) ? $_GET['sort'] : 'recent';

$products_per_page = 20;
$current_page = isset($_GET['p']) ? max(1, (int)$_GET['p']) : 1;
$offset = ($current_page - 1) * $products_per_page;

$conditions = [];
$params = [];
$count_params = []; // Separate params for count query as it might differ if LIMIT/OFFSET were part of it

if (!empty($name_filter)) {
    $conditions[] = "name_pt LIKE ?";
    $params[] = "%$name_filter%";
    $count_params[] = "%$name_filter%";
}

if ($state_filter !== '') {
    $conditions[] = "is_active = ?";
    $params[] = ($state_filter === '1') ? 1 : 0;
    $count_params[] = ($state_filter === '1') ? 1 : 0;
}

if ($type_filter !== '') {
    $conditions[] = "product_type = ?";
    $params[] = $type_filter;
    $count_params[] = $type_filter;
}

// Count total products
$count_sql = "SELECT COUNT(*) as total FROM products";
if (!empty($conditions)) {
    $count_sql .= " WHERE " . implode(" AND ", $conditions);
}
$total_products_result = db_query($count_sql, $count_params, true, true);
$total_products = $total_products_result ? (int)$total_products_result['total'] : 0;
$total_pages = ceil($total_products / $products_per_page);

// Fetch products for the current page
$sql = "SELECT id, name_pt, slug, base_price, is_active, product_type, created_at FROM products";

if (!empty($conditions)) {
    $sql .= " WHERE " . implode(" AND ", $conditions);
}

$sort_options = [
    'recent' => 'created_at DESC',
    'name_asc' => 'name_pt ASC',
    'price_asc' => 'base_price ASC',
    'price_desc' => 'base_price DESC',
    'status' => 'is_active DESC'
];

$order_by = isset($sort_options[$sort_option]) ? $sort_options[$sort_option] : $sort_options['recent'];
$sql .= " ORDER BY " . $order_by;
$sql .= " LIMIT " . (int)$products_per_page . " OFFSET " . (int)$offset; // Append LIMIT and OFFSET

$products = db_query($sql, $params, false, true);

if (isset($_GET['delete_id'])) {
    $delete_id = (int)$_GET['delete_id'];
    
    
    
        
        require_once __DIR__ . '/../../includes/product_functions.php';

        try {
            $deleted = delete_product($delete_id);
            if ($deleted) {
                add_flash_message('Produto removido com sucesso.', 'success');
            } else {
                
                add_flash_message('Erro ao remover produto. Verifique se existem encomendas associadas.', 'danger');
            }
        } catch (Exception $e) {
            
            add_flash_message('Erro ao remover produto: ' . $e->getMessage(), 'danger');
        }
    
    
    

    
    $redirect_url = 'admin.php?section=products&action=list&' . get_session_id_param();
    echo '<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="refresh" content="0;url=' . $redirect_url . '">
    <script>window.location.href = "' . $redirect_url . '";</script>
</head>
<body>
    <p>Produto removido com sucesso. Redirecionando...</p>
    <p>Se não for redirecionado automaticamente, <a href="' . $redirect_url . '">clique aqui</a>.</p>
</body>
</html>';
    exit;
}

?>

<h1>Gerir Produtos</h1>
<div class="d-flex justify-content-between mb-3">
    <a href="admin.php?section=products&action=new&<?= get_session_id_param() ?>" class="btn btn-success">
        <i class="bi bi-plus-circle"></i> Adicionar Novo Produto
    </a>

    <button class="btn btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
        <i class="bi bi-funnel"></i> Filtros
    </button>
</div>

<div class="collapse mb-4" id="filterCollapse">
    <div class="card card-body">
        <form method="get" action="admin.php" class="row g-3">
            <input type="hidden" name="section" value="products">
            <?php
            
            $session_param = get_session_id_param();
            if (!empty($session_param)) {
                list($name, $value) = explode('=', $session_param);
                echo '<input type="hidden" name="' . $name . '" value="' . $value . '">';
            }
            ?>

            <div class="col-md-4">
                <label for="name" class="form-label">Nome</label>
                <input type="text" class="form-control" id="name" name="name" value="<?= htmlspecialchars($name_filter) ?>">
            </div>

            <div class="col-md-3">
                <label for="state" class="form-label">Estado</label>
                <select class="form-select" id="state" name="state">
                    <option value="" <?= $state_filter === '' ? 'selected' : '' ?>>Todos</option>
                    <option value="1" <?= $state_filter === '1' ? 'selected' : '' ?>>Ativo</option>
                    <option value="0" <?= $state_filter === '0' ? 'selected' : '' ?>>Inativo</option>
                </select>
            </div>

            <div class="col-md-3">
                <label for="type" class="form-label">Tipo</label>
                <select class="form-select" id="type" name="type">
                    <option value="" <?= $type_filter === '' ? 'selected' : '' ?>>Todos</option>
                    <option value="digital" <?= $type_filter === 'digital' ? 'selected' : '' ?>>Digital</option>
                    <option value="regular" <?= $type_filter === 'regular' ? 'selected' : '' ?>>Regular</option>
                </select>
            </div>

            <div class="col-md-3">
                <label for="sort" class="form-label">Ordenar por</label>
                <select class="form-select" id="sort" name="sort">
                    <option value="recent" <?= $sort_option === 'recent' ? 'selected' : '' ?>>Mais recentes</option>
                    <option value="name_asc" <?= $sort_option === 'name_asc' ? 'selected' : '' ?>>Nome (A-Z)</option>
                    <option value="price_asc" <?= $sort_option === 'price_asc' ? 'selected' : '' ?>>Preço (Menor-Maior)</option>
                    <option value="price_desc" <?= $sort_option === 'price_desc' ? 'selected' : '' ?>>Preço (Maior-Menor)</option>
                    <option value="status" <?= $sort_option === 'status' ? 'selected' : '' ?>>Estado (Ativos primeiro)</option>
                </select>
            </div>

            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">Filtrar</button>
                <a href="admin.php?section=products&<?= get_session_id_param() ?>" class="btn btn-outline-secondary">Limpar</a>
            </div>
        </form>
    </div>
</div>

<?php display_flash_messages(); ?>

<div class="card">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="card-title mb-0">Lista de Produtos</h5>
            <?php if (!empty($name_filter) || $state_filter !== '' || $type_filter !== '' || $sort_option !== 'recent'): ?>
                <div class="d-flex align-items-center">
                    <span class="badge bg-info me-2">Filtros ativos</span>
                    <a href="admin.php?section=products&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-x-circle"></i> Limpar filtros
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <?php if (!empty($name_filter) || $state_filter !== '' || $type_filter !== '' || $sort_option !== 'recent'): ?>
        <script>
            // Auto-expand filter panel when filters are active
            document.addEventListener('DOMContentLoaded', function() {
                const filterCollapse = document.getElementById('filterCollapse');
                if (filterCollapse) {
                    const bsCollapse = new bootstrap.Collapse(filterCollapse, {
                        toggle: true
                    });
                }
            });
        </script>
        <?php endif; ?>

        <?php if (empty($products)): ?>
            <div class="alert alert-info">Nenhum produto encontrado.</div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nome (PT)</th>
                            <th>Preço Base</th>
                            <th><i class="bi bi-calendar-event"></i> Data Criação</th>
                            <th>Estado</th>
                            <th>Digital</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        
                        if (!function_exists('display_checkmark')) {
                            function display_checkmark($value) {
                                return $value ? '<i class="bi bi-check-circle-fill text-success" title="Sim"></i>' : '<i class="bi bi-x-circle-fill text-secondary" title="Não"></i>';
                            }
                        }
                        ?>
                        <?php foreach ($products as $product): ?>
                            <tr>
                                <td><?= $product['id'] ?></td>
                                <td><?= sanitize_input($product['name_pt']) ?></td>
                                <td><?= format_price($product['base_price']) ?></td>
                                <td><?= !empty($product['created_at']) ? format_date($product['created_at'], 'd/m/Y H:i') : '-' ?></td>
                                <td>
                                    <?php if ($product['is_active']): ?>
                                        <span class="badge bg-success">Ativo</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Inativo</span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center">
                                    <?= display_checkmark($product['product_type'] === 'digital') ?>
                                </td>
                                <td>
                                    <a href="admin.php?section=products&action=edit&id=<?= $product['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-primary" title="Editar">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <a href="admin.php?section=products&action=list&delete_id=<?= $product['id'] ?>&<?= get_session_id_param()  ?>"
                                       class="btn btn-sm btn-outline-danger"
                                       title="Remover"
                                       onclick="return confirm('Tem a certeza que quer remover este produto? Esta ação não pode ser desfeita.');">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                    <a href="admin.php?section=products&action=duplicate&id=<?= $product['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-secondary" title="Duplicar">
                                        <i class="bi bi-copy"></i>
                                    </a>
                                    <!-- Add view on site link? -->
                                    <!-- <a href="<?= get_product_url($product['slug']) ?>" target="_blank" class="btn btn-sm btn-outline-info" title="Ver no site"><i class="bi bi-eye"></i></a> -->
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>

        <?php if ($total_pages > 1): ?>
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mt-4">
                    <?php
                    // Build query string for pagination links, preserving filters and sort
                    $query_params = [];
                    if (!empty($name_filter)) $query_params['name'] = $name_filter;
                    if ($state_filter !== '') $query_params['state'] = $state_filter;
                    if ($type_filter !== '') $query_params['type'] = $type_filter;
                    if ($sort_option !== 'recent') $query_params['sort'] = $sort_option;
                    
                    $base_query_string = http_build_query(array_merge(['section' => 'products'], $query_params));
                    $session_param_str = get_session_id_param();
                    if (!empty($session_param_str)) {
                        $base_query_string .= '&' . $session_param_str;
                    }
                    ?>

                    <!-- Previous Page -->
                    <li class="page-item <?= $current_page <= 1 ? 'disabled' : '' ?>">
                        <a class="page-link" href="?<?= $base_query_string ?>&p=<?= $current_page - 1 ?>" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>

                    <?php
                    $num_links_to_show = 5; // Number of page links to show around current page
                    $start_page = max(1, $current_page - floor($num_links_to_show / 2));
                    $end_page = min($total_pages, $start_page + $num_links_to_show - 1);
                    
                    // Adjust start_page if end_page is at the limit
                    if ($end_page == $total_pages) {
                        $start_page = max(1, $total_pages - $num_links_to_show + 1);
                    }

                    if ($start_page > 1): ?>
                        <li class="page-item"><a class="page-link" href="?<?= $base_query_string ?>&p=1">1</a></li>
                        <?php if ($start_page > 2): ?>
                            <li class="page-item disabled"><span class="page-link">...</span></li>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                        <li class="page-item <?= $i == $current_page ? 'active' : '' ?>">
                            <a class="page-link" href="?<?= $base_query_string ?>&p=<?= $i ?>"><?= $i ?></a>
                        </li>
                    <?php endfor; ?>

                    <?php if ($end_page < $total_pages): ?>
                        <?php if ($end_page < $total_pages - 1): ?>
                            <li class="page-item disabled"><span class="page-link">...</span></li>
                        <?php endif; ?>
                        <li class="page-item"><a class="page-link" href="?<?= $base_query_string ?>&p=<?= $total_pages ?>"><?= $total_pages ?></a></li>
                    <?php endif; ?>

                    <!-- Next Page -->
                    <li class="page-item <?= $current_page >= $total_pages ? 'disabled' : '' ?>">
                        <a class="page-link" href="?<?= $base_query_string ?>&p=<?= $current_page + 1 ?>" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                </ul>
            </nav>
        <?php endif; ?>
    </div>
</div>
