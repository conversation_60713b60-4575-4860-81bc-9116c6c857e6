<?php

$attributes = db_query("SELECT * FROM attributes ORDER BY name_pt ASC", [], false, true);

?>

<h1>Gerir Atributos</h1>
<hr>

<div class="mb-3 text-end">
    <a href="admin.php?section=attributes&action=new&<?= get_session_id_param() ?>" class="btn btn-primary">
        <i class="bi bi-plus-lg"></i> Adicionar Novo Atributo
    </a>
</div>

<?php display_flash_messages(); ?>

<div class="card">
    <div class="card-body">
        <h5 class="card-title">Atributos Existentes</h5>
        <?php if (empty($attributes)): ?>
            <div class="alert alert-info">Nenhum atributo encontrado.</div>
        <?php else: ?>
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nome (PT)</th>
                        <th>A<PERSON><PERSON><PERSON></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($attributes as $attribute): ?>
                        <tr>
                            <td><?= $attribute['id'] ?></td>
                            <td><?= sanitize_input($attribute['name_pt']) ?></td>
                            <td>
                                <a href="admin.php?section=attributes&action=values&attribute_id=<?= $attribute['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-info" title="Gerir Valores">
                                    <i class="bi bi-list-ol"></i> Valores
                                </a>
                                <a href="admin.php?section=attributes&action=edit&id=<?= $attribute['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-secondary" title="Editar Atributo">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <!-- This link now correctly points to the GET handler in admin.php -->
                                <a href="admin.php?section=attributes&action=delete&id=<?= $attribute['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-danger" title="Remover Atributo" onclick="return confirm('Tem a certeza que quer remover este atributo? Todos os seus valores associados também serão removidos.');">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>
