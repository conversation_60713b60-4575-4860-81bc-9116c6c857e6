<?php

function migrate_add_show_title_to_pages_table(PDO $pdo) {
    try {
        
        $stmt = $pdo->query("PRAGMA table_info(pages)");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);

        if (!in_array('show_title', $columns)) {
            $pdo->exec("ALTER TABLE pages ADD COLUMN show_title INTEGER NOT NULL DEFAULT 1");
            
        } else {
            
        }
        return true;
    } catch (PDOException $e) {
        
        return false;
    }
}

if (basename(__FILE__) == basename($_SERVER["SCRIPT_FILENAME"])) {
    require_once __DIR__ . '/../db.php'; 
    
    $pdo = get_db_connection();
    if ($pdo) {
        if (migrate_add_show_title_to_pages_table($pdo)) {
            echo "Migration executed successfully from direct call.\n";
        } else {
            echo "Migration failed when executed from direct call.\n";
        }
    } else {
        echo "Failed to connect to the database for direct migration execution.\n";
    }
}
?>