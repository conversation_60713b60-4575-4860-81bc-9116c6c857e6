<?php
/**
 * <PERSON><PERSON><PERSON> to remove comments and log statements from PHP and JavaScript files.
 *
 * Usage: php cleanup_codebase.php <directory1> [<directory2> ...]
 * Example: php cleanup_codebase.php frontoffice backoffice
 *
 * This script will:
 * - Recursively find .php and .js files in the specified directories.
 * - Create a backup of each file (e.g., file.php.bak) before modifying it.
 * - Remove PHP comments  from .php files.
 * - Remove JS comments from .js files.
 * - Remove error_log(...); statements from both .php and .js files.
 * - Remove console.log(...); statements from both .php and .js files.
 * - Report a summary of actions taken.
 */

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// --- Configuration ---
$SCRIPT_FILENAME = basename(__FILE__);
$EXCLUDE_FILES = [
    $SCRIPT_FILENAME,
    'remove_logs.php',
    'remove_comments.php',
    'remove_all_logs.php',
    'remove_all_logs_comprehensive.php',
    'fix_remaining_logs.php'
];
$TARGET_EXTENSIONS = ['php', 'js'];

// --- Global Counters ---
$summary = [
    'files_processed' => 0,
    'files_failed_read' => 0,
    'files_failed_write' => 0,
    'files_failed_backup' => 0,
    'php_comments_removed' => 0,
    'js_comments_removed' => 0,
    'error_logs_removed' => 0,
    'console_logs_removed' => 0,
];

// --- Argument Handling ---
if ($argc < 2) {
    echo "Usage: php {$SCRIPT_FILENAME} <directory1> [<directory2> ...]\n";
    exit(1);
}

$targetDirectories = array_slice($argv, 1);

foreach ($targetDirectories as $dir) {
    if (!is_dir($dir)) {
        echo "Error: Directory '$dir' does not exist or is not a directory.\n";
        exit(1);
    }
}

echo "Starting codebase cleanup...\n";

// --- Helper Functions ---

/**
 * Recursively finds files with specified extensions in given directories.
 * @param array $dirs Directories to search.
 * @param array $extensions File extensions to find (e.g., ['php', 'js']).
 * @param array $excludeFilenames Filenames to exclude.
 * @return array List of file paths.
 */
function findFiles(array $dirs, array $extensions, array $excludeFilenames): array {
    $foundFiles = [];
    foreach ($dirs as $dir) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $filename = $file->getFilename();
                $filePath = $file->getRealPath();
                if (in_array(strtolower($file->getExtension()), $extensions) && !in_array($filename, $excludeFilenames)) {
                    $foundFiles[] = $filePath;
                }
            }
        }
    }
    return $foundFiles;
}

/**
 * Removes PHP comments from code.
 * @param string $code PHP code.
 * @param int $removedCount Number of comments removed (passed by reference).
 * @return string PHP code without comments.
 */
function removePhpComments(string $code, &$removedCount): string {
    $output = '';
    $tokens = token_get_all($code);
    $removedCount = 0;

    foreach ($tokens as $token) {
        if (is_array($token)) {
            if ($token[0] === T_COMMENT || $token[0] === T_DOC_COMMENT) {
                $removedCount++;
                // Replace comment with a single newline if it originally ended with one, to preserve some structure
                if (substr($token[1], -1) === "\n") {
                    $output .= "\n";
                }
                continue;
            }
            $output .= $token[1];
        } else {
            $output .= $token;
        }
    }
    // Clean up multiple consecutive empty lines
    $output = preg_replace('/(\r\n|\r|\n){3,}/', "\n\n", $output);
    return $output;
}

/**
 * Removes JavaScript comments from code using a robust callback.
 * @param string $code JavaScript code.
 * @param int $removedCount Number of comments removed (passed by reference).
 * @return string JavaScript code without comments.
 */
function removeJsComments(string $code, &$removedCount): string {
    $removedCount = 0;
    $output = preg_replace_callback(
        '#(/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+/)|(//[^\r\n]*)|(\'(?:[^\\\\\']|\\\\.)*\'|"(?:[^\\\\"]|\\\\.)*")#s',
        function ($matches) use (&$removedCount) {
            // $matches[1] is for /* ... */
            // $matches[0] starting with // is for // ...
            // $matches[0] starting with ' or " is for strings
            if (!empty($matches[1])) { // Matched /* ... */
                $removedCount++;
                // Preserve newlines within multi-line comments to maintain line count somewhat
                return preg_replace('/[^\r\n]+/', '', $matches[1]);
            } elseif (strpos($matches[0], '//') === 0) { // Matched // ...
                $removedCount++;
                return ''; // Remove single-line comment, add "\n" if it was on its own line and you want to preserve line numbers.
            } else { // Matched a string literal or regex
                return $matches[0]; // Keep string literal or regex
            }
        },
        $code
    );

    if ($output === null) { // preg_replace_callback can return null on error
        echo "Warning: preg_replace_callback failed for JS comment removal. Original content kept.\n";
        return $code;
    }

    // Clean up multiple consecutive empty lines
    $output = preg_replace('/(\r\n|\r|\n){3,}/', "\n\n", $output);
    return $output;
}


/**
 * Removes log statements (error_log, console.log) from code.
 * @param string $code The source code.
 * @param string $logType 'error_log' or 'console.log'.
 * @param int $removedCount Number of log statements removed (passed by reference).
 * @return string Code without specified log statements.
 */
function removeLogStatements(string $code, string $logType, &$removedCount): string {
    $pattern = '';
    if ($logType === 'error_log') {
        // Matches: optional_space error_log optional_space ( anything ) optional_space ;
        // The 's' modifier makes . match newlines.
        $pattern = '/\s*error_log\s*\(.*?\)\s*;/s';
    } elseif ($logType === 'console.log') {
        $pattern = '/\s*console\.log\s*\(.*?\)\s*;/s';
    } else {
        return $code; // Unknown log type
    }

    $originalCode = $code;
    $modifiedCode = preg_replace($pattern, '', $originalCode, -1, $currentRemoved);
    
    if ($modifiedCode === null) { // preg_replace can return null on error
        echo "Warning: preg_replace failed for {$logType} removal. Original content kept.\n";
        $removedCount += 0;
        return $originalCode;
    }
    $removedCount += $currentRemoved;
    return $modifiedCode;
}


// --- Main Processing Logic ---
$allFilesToProcess = findFiles($targetDirectories, $TARGET_EXTENSIONS, $EXCLUDE_FILES);

if (empty($allFilesToProcess)) {
    echo "No files found to process in the specified directories with extensions: " . implode(', ', $TARGET_EXTENSIONS) . "\n";
    exit(0);
}

echo "Found " . count($allFilesToProcess) . " files to process.\n\n";

foreach ($allFilesToProcess as $filePath) {
    echo "Processing file: $filePath\n";
    $summary['files_processed']++;

    if (!is_readable($filePath)) {
        echo "  Error: File is not readable. Skipping.\n";
        $summary['files_failed_read']++;
        continue;
    }
    if (!is_writable($filePath)) {
        echo "  Error: File is not writable. Skipping.\n";
        $summary['files_failed_write']++;
        continue;
    }

    // Backup file
    $backupPath = $filePath . '.bak';
    if (!copy($filePath, $backupPath)) {
        echo "  Error: Failed to create backup at '$backupPath'. Skipping.\n";
        $summary['files_failed_backup']++;
        continue;
    }
    echo "  Backed up to: $backupPath\n";

    $content = file_get_contents($filePath);
    if ($content === false) {
        echo "  Error: Failed to read file content. Skipping.\n";
        $summary['files_failed_read']++;
        continue;
    }

    $originalContent = $content;
    $fileExtension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

    $phpCommentsRemovedThisFile = 0;
    $jsCommentsRemovedThisFile = 0;
    $errorLogsRemovedThisFile = 0;
    $consoleLogsRemovedThisFile = 0;

    if ($fileExtension === 'php') {
        $content = removePhpComments($content, $phpCommentsRemovedThisFile);
        $summary['php_comments_removed'] += $phpCommentsRemovedThisFile;
        if ($phpCommentsRemovedThisFile > 0) {
            echo "    Removed $phpCommentsRemovedThisFile PHP comment(s).\n";
        }
    } elseif ($fileExtension === 'js') {
        $content = removeJsComments($content, $jsCommentsRemovedThisFile);
        $summary['js_comments_removed'] += $jsCommentsRemovedThisFile;
        if ($jsCommentsRemovedThisFile > 0) {
            echo "    Removed $jsCommentsRemovedThisFile JS comment(s).\n";
        }
    }

    // Remove error_log statements (for both PHP and JS files)
    $content = removeLogStatements($content, 'error_log', $currentErrorLogsRemoved);
    $errorLogsRemovedThisFile += $currentErrorLogsRemoved;
    
    // Remove console.log statements (for both PHP and JS files)
    $content = removeLogStatements($content, 'console.log', $currentConsoleLogsRemoved);
    $consoleLogsRemovedThisFile += $currentConsoleLogsRemoved;

    if ($errorLogsRemovedThisFile > 0) {
        echo "    Removed $errorLogsRemovedThisFile error_log statement(s).\n";
        $summary['error_logs_removed'] += $errorLogsRemovedThisFile;
    }
    if ($consoleLogsRemovedThisFile > 0) {
        echo "    Removed $consoleLogsRemovedThisFile console.log statement(s).\n";
        $summary['console_logs_removed'] += $consoleLogsRemovedThisFile;
    }
    

    if ($content !== $originalContent) {
        if (file_put_contents($filePath, $content) === false) {
            echo "  Error: Failed to write modified content back to file. RESTORING FROM BACKUP.\n";
            $summary['files_failed_write']++;
            copy($backupPath, $filePath); // Attempt to restore
        } else {
            echo "  Successfully modified and saved.\n";
        }
    } else {
        echo "  No changes made to this file.\n";
    }
    echo "\n";
}

// --- Final Summary ---
echo "-------------------------------------\n";
echo "Cleanup Process Summary:\n";
echo "-------------------------------------\n";
echo "Total files scanned for processing: " . count($allFilesToProcess) . "\n";
echo "Files actually processed: " . $summary['files_processed'] . "\n";
if ($summary['files_failed_read'] > 0) {
    echo "Files failed to read: " . $summary['files_failed_read'] . "\n";
}
if ($summary['files_failed_write'] > 0) {
    echo "Files failed to write (and attempted restore): " . $summary['files_failed_write'] . "\n";
}
if ($summary['files_failed_backup'] > 0) {
    echo "Files failed to backup: " . $summary['files_failed_backup'] . "\n";
}
echo "\n";
echo "Total PHP comments removed: " . $summary['php_comments_removed'] . "\n";
echo "Total JavaScript comments removed: " . $summary['js_comments_removed'] . "\n";
echo "Total error_log statements removed: " . $summary['error_logs_removed'] . "\n";
echo "Total console.log statements removed: " . $summary['console_logs_removed'] . "\n";
echo "-------------------------------------\n";
echo "Cleanup complete. Please review the changes and test your application.\n";
echo "Backup files have been created with a '.bak' extension.\n";

?>