<?php

require_once __DIR__ . '/db.php';
require_once __DIR__ . '/digital_product_functions.php';
require_once __DIR__ . '/order_functions.php';

function cleanup_expired_order_access_tokens(): array
{
    $pdo = get_db_connection();
    if (!$pdo) return ['success' => false, 'message' => 'Database connection failed'];

    try {

        $table_check = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_access_tokens'");
        if (!$table_check->fetch()) {
            return ['success' => false, 'message' => 'Order access tokens table does not exist'];
        }


        $sql = "DELETE FROM order_access_tokens WHERE expires_at <= datetime('now', 'localtime')";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $deleted_count = $stmt->rowCount();

        return [
            'success' => true,
            'deleted_count' => $deleted_count,
            'message' => "Deleted $deleted_count expired order access tokens"
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Error cleaning up expired order access tokens: ' . $e->getMessage()
        ];
    }
}
function cleanup_all_active_download_tokens(): array
{
    $pdo = get_db_connection();
    if (!$pdo) return ['success' => false, 'message' => 'Database connection failed'];

    try {
        $table_check = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='download_tokens'");
        if (!$table_check->fetch()) {
            return ['success' => false, 'message' => 'Download tokens table does not exist'];
        }

        // Delete tokens that are not expired
        $sql = "DELETE FROM download_tokens WHERE expires_at > datetime('now', 'localtime')";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $deleted_count = $stmt->rowCount();

        return [
            'success' => true,
            'deleted_count' => $deleted_count,
            'message' => "Deleted $deleted_count active (non-expired) download tokens"
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Error cleaning up active download tokens: ' . $e->getMessage()
        ];
    }
}

function cleanup_all_active_order_access_tokens(): array
{
    $pdo = get_db_connection();
    if (!$pdo) return ['success' => false, 'message' => 'Database connection failed'];

    try {
        $table_check = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_access_tokens'");
        if (!$table_check->fetch()) {
            return ['success' => false, 'message' => 'Order access tokens table does not exist'];
        }

        // Delete tokens that are not expired
        $sql = "DELETE FROM order_access_tokens WHERE expires_at > datetime('now', 'localtime')";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $deleted_count = $stmt->rowCount();

        return [
            'success' => true,
            'deleted_count' => $deleted_count,
            'message' => "Deleted $deleted_count active (non-expired) order access tokens"
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Error cleaning up active order access tokens: ' . $e->getMessage()
        ];
    }
}

function get_download_tokens_stats(): array
{
    $pdo = get_db_connection();
    if (!$pdo) return ['success' => false, 'message' => 'Database connection failed'];

    try {

        $table_check = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='download_tokens'");
        if (!$table_check->fetch()) {
            return ['success' => false, 'message' => 'Download tokens table does not exist'];
        }


        $total_stmt = $pdo->query("SELECT COUNT(*) as count FROM download_tokens");
        $total = $total_stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;


        $expired_stmt = $pdo->query("SELECT COUNT(*) as count FROM download_tokens WHERE expires_at <= datetime('now', 'localtime')");
        $expired = $expired_stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;


        $used_stmt = $pdo->query("SELECT COUNT(*) as count FROM download_tokens WHERE is_used = 1");
        $used = $used_stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;


        $expired_used_stmt = $pdo->query("SELECT COUNT(*) as count FROM download_tokens WHERE expires_at <= datetime('now', 'localtime') AND is_used = 1");
        $expired_used = $expired_used_stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

        return [
            'success' => true,
            'total' => $total,
            'expired' => $expired,
            'used' => $used,
            'expired_used' => $expired_used
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Error getting download tokens stats: ' . $e->getMessage()
        ];
    }
}

function get_order_access_tokens_stats(): array
{
    $pdo = get_db_connection();
    if (!$pdo) return ['success' => false, 'message' => 'Database connection failed'];

    try {

        $table_check = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_access_tokens'");
        if (!$table_check->fetch()) {
            return ['success' => false, 'message' => 'Order access tokens table does not exist'];
        }


        $total_stmt = $pdo->query("SELECT COUNT(*) as count FROM order_access_tokens");
        $total = $total_stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;


        $expired_stmt = $pdo->query("SELECT COUNT(*) as count FROM order_access_tokens WHERE expires_at <= datetime('now', 'localtime')");
        $expired = $expired_stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;


        $old_format_count = 0;
        $tokens_stmt = $pdo->query("SELECT access_token FROM order_access_tokens");
        while ($token = $tokens_stmt->fetch(PDO::FETCH_ASSOC)) {
            if (!extract_order_id_from_token($token['access_token'])) {
                $old_format_count++;
            }
        }

        return [
            'success' => true,
            'total' => $total,
            'expired' => $expired,
            'old_format' => $old_format_count
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Error getting order access tokens stats: ' . $e->getMessage()
        ];
    }
}

function optimize_database(): array
{
    $pdo = get_db_connection();
    if (!$pdo) return ['success' => false, 'message' => 'Database connection failed'];

    try {

        $pdo->exec('VACUUM');


        $pdo->exec('ANALYZE');

        return [
            'success' => true,
            'message' => 'Database optimized successfully'
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Error optimizing database: ' . $e->getMessage()
        ];
    }
}

function check_database_integrity(): array
{
    $pdo = get_db_connection();
    if (!$pdo) return ['success' => false, 'message' => 'Database connection failed'];

    try {

        $stmt = $pdo->query('PRAGMA integrity_check');
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        $integrity_ok = ($result && isset($result['integrity_check']) && $result['integrity_check'] === 'ok');

        return [
            'success' => true,
            'integrity_ok' => $integrity_ok,
            'message' => $integrity_ok ? 'Database integrity check passed' : 'Database integrity check failed',
            'details' => $result
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Error checking database integrity: ' . $e->getMessage()
        ];
    }
}

function backup_database(string $backup_dir = '../backups'): array
{

    if (!is_dir($backup_dir)) {
        if (!mkdir($backup_dir, 0755, true)) {
            return [
                'success' => false,
                'message' => "Failed to create backup directory: $backup_dir"
            ];
        }
    }


    $timestamp = date('Y-m-d_H-i-s');
    $backup_file = "$backup_dir/database_backup_$timestamp.sqlite";


    if (copy(DB_PATH, $backup_file)) {
        return [
            'success' => true,
            'message' => "Database backed up successfully to $backup_file",
            'backup_file' => $backup_file
        ];
    } else {
        return [
            'success' => false,
            'message' => "Failed to backup database to $backup_file"
        ];
    }
}

function get_order_visits_stats(): array
{
    $pdo = get_db_connection();
    if (!$pdo) return ['success' => false, 'message' => 'Database connection failed'];

    try {

        $table_check = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_visits'");
        if (!$table_check->fetch()) {
            return ['success' => false, 'message' => 'Order visits table does not exist'];
        }


        $total_stmt = $pdo->query("SELECT COUNT(*) as count FROM order_visits");
        $total = $total_stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;


        $unique_orders_stmt = $pdo->query("SELECT COUNT(DISTINCT order_id) as count FROM order_visits");
        $unique_orders = $unique_orders_stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;


        $unique_sessions_stmt = $pdo->query("SELECT COUNT(DISTINCT session_id) as count FROM order_visits");
        $unique_sessions = $unique_sessions_stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;


        $old_visits_stmt = $pdo->query("SELECT COUNT(*) as count FROM order_visits WHERE visit_time < datetime('now', 'localtime', '-30 days')");
        $old_visits = $old_visits_stmt->fetch(PDO::FETCH_ASSOC)['count'] ?? 0;

        return [
            'success' => true,
            'total' => $total,
            'unique_orders' => $unique_orders,
            'unique_sessions' => $unique_sessions,
            'old_visits' => $old_visits
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Error getting order visits stats: ' . $e->getMessage()
        ];
    }
}

/**
 * Clean up old order visits
 *
 * @see order_functions.php for implementation
 * This function is implemented in order_functions.php to avoid duplication
 */

/**
 * Migrate file types tables:
 * 1. Ensure all data from digital_product_file_types is in digital_files_file_types
 * 2. Remove the digital_product_file_type_associations table
 *
 * @see db_migrations/migrate_file_types_tables.php for implementation
 */
function run_migrate_file_types_tables() {
    require_once __DIR__ . '/db_migrations/migrate_file_types_tables.php';
    return migrate_file_types_tables();
}