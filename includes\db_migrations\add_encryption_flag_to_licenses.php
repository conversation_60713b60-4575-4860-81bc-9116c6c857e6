<?php

require_once __DIR__ . '/../../config.php';
require_once __DIR__ . '/../db.php';

$pdo = get_db_connection();

$stmt = $pdo->query("PRAGMA table_info(licenses);");
$columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1); 

if (!in_array('is_encrypted', $columns)) {
    try {
        $pdo->exec("ALTER TABLE licenses ADD COLUMN is_encrypted INTEGER NOT NULL DEFAULT 0;");
        echo "Successfully added 'is_encrypted' column to licenses table.\n";
    } catch (PDOException $e) {
        echo "Error adding column: " . $e->getMessage() . "\n";
    }
} else {
    echo "'is_encrypted' column already exists in licenses table.\n";
}
