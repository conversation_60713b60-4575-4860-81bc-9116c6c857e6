<?php
// <PERSON><PERSON>t to fix remaining error_log and console.log statements

// Files with remaining error_log statements
$files_with_error_logs = [
    'includes/order_functions.php',
    'includes/session.php',
    'includes/sitemap_functions.php',
    'templates/frontend/download_file.php'
];

// Files with remaining console.log statements
$files_with_console_logs = [
    'templates/frontend/order_success.php'
];

// Process error_log files
echo "Fixing remaining error_log statements...\n";
foreach ($files_with_error_logs as $file) {
    echo "Processing file: $file\n";
    
    // Read the file
    $content = file_get_contents($file);
    if ($content === false) {
        echo "  Error: Could not read file: $file\n";
        continue;
    }
    
    // Count original error_log statements
    preg_match_all('/error_log\(.*?\);/', $content, $error_matches);
    $original_error_count = count($error_matches[0]);
    
    // Replace error_log statements
    $content = preg_replace('/\s*error_log\(.*?\);/', '', $content);
    
    // Write the file back
    if (file_put_contents($file, $content) === false) {
        echo "  Error: Could not write to file: $file\n";
        continue;
    }
    
    // Count remaining error_log statements
    preg_match_all('/error_log\(.*?\);/', $content, $error_matches);
    $remaining_error_count = count($error_matches[0]);
    
    echo "  Removed " . ($original_error_count - $remaining_error_count) . " error_log statements\n";
}

// Process console.log files
echo "\nFixing remaining console.log statements...\n";
foreach ($files_with_console_logs as $file) {
    echo "Processing file: $file\n";
    
    // Read the file
    $content = file_get_contents($file);
    if ($content === false) {
        echo "  Error: Could not read file: $file\n";
        continue;
    }
    
    // Count original console.log statements
    preg_match_all('/console\.log\(.*?\)/', $content, $console_matches);
    $original_console_count = count($console_matches[0]);
    
    // Replace console.log statements
    $content = preg_replace('/\.then\(\s*\(\)\s*=>\s*console\.log\(.*?\)\)/', '.then(() => {})', $content);
    $content = preg_replace('/console\.log\(.*?\)/', '', $content);
    
    // Write the file back
    if (file_put_contents($file, $content) === false) {
        echo "  Error: Could not write to file: $file\n";
        continue;
    }
    
    // Count remaining console.log statements
    preg_match_all('/console\.log\(.*?\)/', $content, $console_matches);
    $remaining_console_count = count($console_matches[0]);
    
    echo "  Removed " . ($original_console_count - $remaining_console_count) . " console.log statements\n";
}

echo "\nAll remaining logs fixed!\n";
