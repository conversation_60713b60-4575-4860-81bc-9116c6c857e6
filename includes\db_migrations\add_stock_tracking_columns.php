<?php

function add_stock_tracking_columns(PDO $pdo): bool
{
    try {
        
        $stmt = $pdo->query("PRAGMA table_info(order_items)");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1); 
        
        
        $pdo->beginTransaction();
        
        
        if (!in_array('stock_reduced', $columns)) {
            $pdo->exec("ALTER TABLE order_items ADD COLUMN stock_reduced INTEGER NOT NULL DEFAULT 0");
        }
        
        
        if (!in_array('stock_restored', $columns)) {
            $pdo->exec("ALTER TABLE order_items ADD COLUMN stock_restored INTEGER NOT NULL DEFAULT 0");
        }
        
        
        $pdo->commit();
        return true;
    } catch (PDOException $e) {
        
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}
