<?php

$license_id = isset($_GET['license_id']) ? (int)$_GET['license_id'] : 0;
$license_code = isset($_GET['code']) ? sanitize_input($_GET['code']) : '';

$license = null;
if ($license_id > 0) {
    $license = get_license_by_id($license_id);
} elseif (!empty($license_code)) {
    $license = get_license_by_code($license_code);
    if ($license) {
        $license_id = $license['id'];
    }
}

if (!$license) {
    
    header('Location: ' . BASE_URL . '/index.php?view=download&error=license_not_found');
    exit;
}

$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    $reason = isset($_POST['reason']) ? sanitize_input($_POST['reason']) : '';
    $days_to_add = isset($_POST['days_to_add']) ? (int)$_POST['days_to_add'] : 5;

    
    if (empty($reason)) {
        $error_message = 'Por favor, forneça um motivo para a solicitação de extensão.';
    } elseif ($days_to_add <= 0 || $days_to_add > 30) {
        $error_message = 'O número de dias solicitados deve estar entre 1 e 30.';
    } else {
        
        $result = request_license_extension($license_id, $days_to_add, $reason);

        if ($result['success']) {
            $success_message = $result['message'];
        } else {
            $error_message = $result['message'];
        }
    }
}

$censored_name = get_censored_string($license['customer_name'], 2, 2);
$censored_email = get_censored_email($license['customer_email']);
?>

<div class="max-w-4xl mx-auto px-4 py-8">
    <h1 class="text-2xl font-semibold mb-6">Solicitar Extensão de Licença</h1>

    <?php if (!empty($error_message)): ?>
        <div class="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded relative mb-6" role="alert">
            <strong class="font-bold">Erro!</strong>
            <span class="block sm:inline"><?= sanitize_input($error_message) ?></span>
        </div>
    <?php endif; ?>

    <?php if (!empty($success_message)): ?>
        <div class="bg-green-900 border border-green-700 text-green-100 px-4 py-3 rounded relative mb-6" role="alert">
            <strong class="font-bold">Sucesso!</strong>
            <span class="block sm:inline"><?= sanitize_input($success_message) ?></span>
            <div class="mt-3">
                <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download&code=' . urlencode($license['license_code'])) ?>" class="inline-block bg-green-700 hover:bg-green-600 text-white py-2 px-4 rounded-button text-sm">
                    <i class="ri-arrow-left-line mr-1"></i> Voltar para Verificação de Licença
                </a>
            </div>
        </div>
    <?php endif; ?>

    <?php if (empty($success_message)): ?>
        <div class="bg-gray-900 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-medium mb-4">Detalhes da Licença</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <p class="text-gray-400 text-sm mb-1">Código de Licença:</p>
                    <p class="font-medium"><?= sanitize_input($license['license_code']) ?></p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Licenciado para:</p>
                    <p class="font-medium"><?= sanitize_input($censored_name) ?></p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Email:</p>
                    <p class="font-medium"><?= sanitize_input($censored_email) ?></p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Data de Expiração:</p>
                    <p class="font-medium">
                        <?= date('d/m/Y H:i', strtotime($license['expiry_date'])) ?>
                        <?php if (strtotime($license['expiry_date']) < time()): ?>
                            <span class="text-sm text-red-400">(Expirada)</span>
                        <?php endif; ?>
                    </p>
                </div>
            </div>

            <form method="post" action="<?= add_session_param_to_url(BASE_URL . '/index.php?view=license_extension&license_id=' . $license_id) ?>">
                <?php if (function_exists('csrf_token_field')): ?>
                    <?= csrf_token_field() ?>
                <?php endif; ?>

                <div class="mb-4">
                    <label for="days_to_add" class="block text-sm font-medium text-gray-300 mb-1">Dias Adicionais Solicitados</label>
                    <select id="days_to_add" name="days_to_add" class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none">
                        <option value="5">5 dias</option>
                        <option value="10">10 dias</option>
                        <option value="15">15 dias</option>
                        <option value="30">30 dias</option>
                    </select>
                    <p class="text-sm text-gray-400 mt-1">
                        Selecione o número de dias adicionais que você precisa para acessar seus arquivos.
                    </p>
                </div>

                <div class="mb-6">
                    <label for="reason" class="block text-sm font-medium text-gray-300 mb-1">Motivo da Solicitação</label>
                    <textarea id="reason" name="reason" rows="4" class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none" required></textarea>
                    <p class="text-sm text-gray-400 mt-1">
                        Por favor, explique por que você precisa de uma extensão para sua licença.
                    </p>
                </div>

                <div class="flex items-center justify-between">
                    <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download&code=' . urlencode($license['license_code'])) ?>" class="inline-block bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-button font-medium">
                        <i class="ri-arrow-left-line mr-2"></i> Cancelar
                    </a>
                    <button type="submit" class="inline-block bg-primary hover:bg-primary/90 text-white py-3 px-6 rounded-button font-medium">
                        <i class="ri-send-plane-line mr-2"></i> Enviar Solicitação
                    </button>
                </div>
            </form>
        </div>
    <?php endif; ?>
</div>
