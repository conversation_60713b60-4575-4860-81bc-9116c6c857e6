<?php

$is_editing = ($action === 'edit' && isset($item_id));
$form_title = $is_editing ? "Editar Página" : "Criar Nova Página";
$page_data = null;

if ($is_editing) {
    
    $page_data = db_query("SELECT * FROM pages WHERE id = :id", [':id' => $item_id], true);
    if (!$page_data) {
        echo '<div class="alert alert-danger">Página não encontrada.</div>';
        return; 
    }
    $form_title .= " (#" . $page_data['id'] . ")";
}

$form_data = $_SESSION['form_data']['pages'] ?? []; 

$title_pt = $form_data['title_pt'] ?? ($page_data['title_pt'] ?? '');
$content_pt = $form_data['content_pt'] ?? ($page_data['content_pt'] ?? '');
$slug_pt = $form_data['slug'] ?? ($page_data['slug'] ?? ''); 

$auto_fill_seo = !$is_editing && empty($form_data);
$plain_content_pt = '';

if ($auto_fill_seo) {
    if (!empty($content_pt)) {
        $plain_content_pt = strip_tags($content_pt);
    }
    $short_description_pt = mb_substr($plain_content_pt, 0, 160);
    if (mb_strlen($plain_content_pt) > 160) {
        $short_description_pt .= '...';
    }
}

$seo_title = $form_data['seo_title'] ?? ($page_data['seo_title'] ?? ($auto_fill_seo ? $title_pt : ''));
$seo_description = $form_data['seo_description'] ?? ($page_data['seo_description'] ?? ($auto_fill_seo ? ($short_description_pt ?? '') : ''));
$seo_keywords = $form_data['seo_keywords'] ?? ($page_data['seo_keywords'] ?? '');
$og_title = $form_data['og_title'] ?? ($page_data['og_title'] ?? ($auto_fill_seo ? $title_pt : ''));
$og_description = $form_data['og_description'] ?? ($page_data['og_description'] ?? ($auto_fill_seo ? ($short_description_pt ?? '') : ''));
$og_image = $form_data['og_image'] ?? ($page_data['og_image'] ?? ''); 
$twitter_card = $form_data['twitter_card'] ?? ($page_data['twitter_card'] ?? 'summary'); 
$twitter_title = $form_data['twitter_title'] ?? ($page_data['twitter_title'] ?? ($auto_fill_seo ? $title_pt : ''));
$twitter_description = $form_data['twitter_description'] ?? ($page_data['twitter_description'] ?? ($auto_fill_seo ? ($short_description_pt ?? '') : ''));
$twitter_image = $form_data['twitter_image'] ?? ($page_data['twitter_image'] ?? ''); 

if (isset($_SESSION['form_data']['pages'])) {
    unset($_SESSION['form_data']['pages']); 
}

?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <form method="POST" action="admin.php?section=pages&action=<?= $is_editing ? 'edit&id=' . $item_id : 'new' ?>&<?= get_session_id_param() ?>" id="pageForm">
                <?= csrf_input_field() ?>
                 <?php if ($is_editing): ?>
                    <input type="hidden" name="id" value="<?php echo htmlspecialchars($item_id); ?>">
                <?php endif; ?>

                <div class="card card-primary card-tabs">
                    <div class="card-header p-0 pt-1">
                        <ul class="nav nav-tabs" id="custom-tabs-one-tab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="custom-tabs-content-tab" data-bs-toggle="pill" href="#custom-tabs-content" role="tab" aria-controls="custom-tabs-content" aria-selected="true">Conteúdo Principal</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="custom-tabs-seo-tab" data-bs-toggle="pill" href="#custom-tabs-seo" role="tab" aria-controls="custom-tabs-seo" aria-selected="false">SEO & Social</a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <?php display_flash_messages(); ?>
                        <div class="tab-content" id="custom-tabs-one-tabContent">
                            <!-- Content Tab -->
                            <div class="tab-pane fade show active" id="custom-tabs-content" role="tabpanel" aria-labelledby="custom-tabs-content-tab">
                                <div class="mb-3">
                                    <label for="title_pt" class="form-label">Título da Página (PT) *</label>
                                    <input type="text" class="form-control" id="title_pt" name="title_pt" value="<?= htmlspecialchars($title_pt) ?>" required>
                                </div>
                                <div class="mb-3">
                                    <label for="slug" class="form-label">Slug (URL) *</label>
                <input type="text" class="form-control" id="slug" name="slug" value="<?= htmlspecialchars($slug_pt) ?>" required>
                <div class="form-text">Parte do URL amigável (ex: sobre-nos). Use letras minúsculas, números e hífens. Deixe em branco para gerar automaticamente a partir do título.</div>
            </div>

            <div class="mb-3">
                <label for="category_id" class="form-label">Categoria</label>
                <select class="form-select" id="category_id" name="category_id">
                    <option value="">-- Nenhuma --</option>
                    <?php
                    $selected_category_id = $page_data['category_id'] ?? null;
                    if (!empty($categories)) { 
                        foreach ($categories as $category) {
                            $selected = ($category['id'] == $selected_category_id) ? 'selected' : '';
                            echo '<option value="' . $category['id'] . '" ' . $selected . '>' . htmlspecialchars($category['name']) . '</option>';
                        }
                    }
                    ?>
                </select>
            </div>

             <div class="mb-3">
                <label for="content_pt" class="form-label">Conteúdo (PT)</label>
                <textarea class="form-control summernote-editor" id="content_pt" name="content_pt" rows="15"><?= htmlspecialchars($content_pt) ?></textarea>
            </div>
             <div class="row">
                 <div class="col-md-6 mb-3">
                    <label for="is_active" class="form-label">Estado</label>
                    <select class="form-select" id="is_active" name="is_active">
                        <option value="1" <?= (($page_data['is_active'] ?? 1) == 1) ? 'selected' : '' ?>>Ativa</option>
                        <option value="0" <?= (($page_data['is_active'] ?? 1) == 0) ? 'selected' : '' ?>>Inativa</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label for="placeholder_id" class="form-label">Placeholder</label>
                    <select class="form-select" id="placeholder_id" name="placeholder_id">
                        <option value="">-- Nenhum --</option>
                        <?php
                        $selected_placeholder_id = $page_data['placeholder_id'] ?? null;
                        $placeholders = get_all_page_placeholders(); 
                        if (!empty($placeholders)) {
                            foreach ($placeholders as $placeholder) {
                                $selected = ($placeholder['id'] == $selected_placeholder_id) ? 'selected' : '';
                                echo '<option value="' . $placeholder['id'] . '" ' . $selected . '>' . htmlspecialchars($placeholder['name']) . '</option>';
                            }
                        }
                        ?>
                    </select>
                    <div class="form-text">Selecione um placeholder para agrupar esta página no rodapé.</div>
                </div>
            </div>
            <div class="row">
                 <div class="col-md-12 mb-3">
                    <label class="form-label">Opções de Exibição e Checkout</label>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="1" id="show_in_header" name="show_in_header" <?= (($page_data['show_in_header'] ?? 0) == 1) ? 'checked' : '' ?>>
                        <label class="form-check-label" for="show_in_header">
                            Mostrar no Cabeçalho
                        </label>
                    </div>

                     <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="1" id="require_agreement_checkout" name="require_agreement_checkout" <?= (($page_data['require_agreement_checkout'] ?? 0) == 1) ? 'checked' : '' ?>>
                        <label class="form-check-label" for="require_agreement_checkout">
                            Requerer Concordância no Checkout
                        </label>
                   </div>

                   <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="1" id="require_agreement_digital_checkout" name="require_agreement_digital_checkout" <?= (($page_data['require_agreement_digital_checkout'] ?? 0) == 1) ? 'checked' : '' ?>>
                        <label class="form-check-label" for="require_agreement_digital_checkout">
                            Requerer Concordância no Checkout se o carrinho tiver produtos digitais
                        </label>
                   </div>

                   <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="1" id="show_title" name="show_title" <?= (($page_data['show_title'] ?? 1) == 1) ? 'checked' : '' ?>>
                        <label class="form-check-label" for="show_title">
                            Mostrar Título na Página (no Frontend)
                        </label>
                   </div>
                   <!-- Removed System Page Checkbox -->
                </div>
           </div>
                           </div> <!-- /#custom-tabs-content -->

                           <!-- SEO & Social Tab -->
                           <div class="tab-pane fade" id="custom-tabs-seo" role="tabpanel" aria-labelledby="custom-tabs-seo-tab">
                               <div class="d-flex justify-content-between align-items-center mb-3">
                                   <h4 class="mb-0">Otimização para Motores de Busca (SEO)</h4>
                                   <button type="button" id="prefill-seo-button" class="btn btn-outline-primary">
                                       <i class="bi bi-magic"></i> Preencher Automaticamente
                                   </button>
                               </div>
                               <div class="alert alert-info mb-3">
                                   <i class="bi bi-info-circle"></i> Clique no botão "Preencher Automaticamente" para gerar automaticamente os campos SEO e Social com base no conteúdo principal.
                               </div>
                               <div class="form-group mb-3">
                                   <label for="seo_title">Título SEO</label>
                                   <input type="text" class="form-control" id="seo_title" name="seo_title" value="<?php echo htmlspecialchars($seo_title); ?>">
                                   <small class="form-text text-muted">Opcional. Título que aparecerá nos resultados de busca. Se vazio, usa o título da página.</small>
                               </div>
                               <div class="form-group mb-3">
                                   <label for="seo_description">Descrição SEO</label>
                                   <textarea class="form-control" id="seo_description" name="seo_description" rows="2"><?php echo htmlspecialchars($seo_description); ?></textarea>
                                   <small class="form-text text-muted">Opcional. Breve descrição para resultados de busca.</small>
                               </div>
                               <div class="form-group mb-3">
                                   <label for="seo_keywords">Palavras-chave SEO</label>
                                   <input type="text" class="form-control" id="seo_keywords" name="seo_keywords" value="<?php echo htmlspecialchars($seo_keywords); ?>">
                                   <small class="form-text text-muted">Opcional. Separadas por vírgula.</small>
                               </div>

                               <hr>

                               <h4>Redes Sociais (Open Graph & Twitter Cards)</h4>
                               <p class="text-muted"><small>Opcional. Se deixado em branco, tentará usar os dados de SEO ou da página principal.</small></p>

                               <div class="row">
                                   <div class="col-md-6">
                                       <h5>Facebook / Open Graph</h5>
                                       <div class="form-group mb-3">
                                           <label for="og_title">Título Open Graph</label>
                                           <input type="text" class="form-control" id="og_title" name="og_title" value="<?php echo htmlspecialchars($og_title); ?>">
                                       </div>
                                        <div class="form-group mb-3">
                                           <label for="og_description">Descrição Open Graph</label>
                                           <textarea class="form-control" id="og_description" name="og_description" rows="2"><?php echo htmlspecialchars($og_description); ?></textarea>
                                       </div>
                                       <div class="form-group mb-3">
                                           <label for="og_image">URL da Imagem Open Graph</label>
                                           <input type="text" class="form-control" id="og_image" name="og_image" value="<?php echo htmlspecialchars($og_image); ?>">
                                            <small class="form-text text-muted">URL completo ou caminho relativo. Páginas não têm imagem destacada por defeito.</small>
                                       </div>
                                   </div>
                                   <div class="col-md-6">
                                        <h5>Twitter Card</h5>
                                       <div class="form-group mb-3">
                                           <label for="twitter_card">Tipo de Card</label>
                                           <select class="form-select" id="twitter_card" name="twitter_card">
                                               <option value="summary" <?php echo ($twitter_card === 'summary') ? 'selected' : ''; ?>>Summary Card</option>
                                               <option value="summary_large_image" <?php echo ($twitter_card === 'summary_large_image') ? 'selected' : ''; ?>>Summary Card with Large Image</option>
                                           </select>
                                       </div>
                                        <div class="form-group mb-3">
                                           <label for="twitter_title">Título Twitter</label>
                                           <input type="text" class="form-control" id="twitter_title" name="twitter_title" value="<?php echo htmlspecialchars($twitter_title); ?>">
                                       </div>
                                        <div class="form-group mb-3">
                                           <label for="twitter_description">Descrição Twitter</label>
                                           <textarea class="form-control" id="twitter_description" name="twitter_description" rows="2"><?php echo htmlspecialchars($twitter_description); ?></textarea>
                                       </div>
                                        <div class="form-group mb-3">
                                           <label for="twitter_image">URL da Imagem Twitter</label>
                                           <input type="text" class="form-control" id="twitter_image" name="twitter_image" value="<?php echo htmlspecialchars($twitter_image); ?>">
                                           <small class="form-text text-muted">URL completo ou caminho relativo. Páginas não têm imagem destacada por defeito.</small>
                                       </div>
                                   </div>
                               </div>
                           </div> <!-- /#custom-tabs-seo -->
                       </div>
                   </div>
                   <!-- /.card-body -->
                   <div class="card-footer">
                        <button type="submit" name="save_action" value="save_and_return" class="btn btn-primary">Guardar e Voltar</button>
                        <button type="submit" name="save_action" value="save_and_continue" class="btn btn-info">Guardar e Continuar a Editar</button>
                        <a href="admin.php?section=pages&<?= get_session_id_param() ?>" class="btn btn-secondary">Cancelar</a>
                   </div>
               </div>
               <!-- /.card -->
           </form>
       </div>
   </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
   // Slug generation
   const titleInput = document.getElementById('title_pt');
   const slugInput = document.getElementById('slug');
   if (titleInput && slugInput) {
       titleInput.addEventListener('blur', function() {
           if (slugInput.value.trim() === '') {
               let slug = this.value.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\-]+/g, '').replace(/\-\-+/g, '-').replace(/^-+/, '').replace(/-+$/, '');
               slugInput.value = slug;
           }
       });
       slugInput.addEventListener('blur', function() {
           let slug = this.value.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\-]+/g, '').replace(/\-\-+/g, '-').replace(/^-+/, '').replace(/-+$/, '');
           if (this.value !== slug) { this.value = slug; }
       });
   }

   // SEO & Social Auto-fill functionality
   const prefillSeoButton = document.getElementById('prefill-seo-button');
   if (prefillSeoButton) {
       prefillSeoButton.addEventListener('click', function(e) {
           e.preventDefault();

           const title = document.getElementById('title_pt').value.trim();
           let content = '';

           // Get content from Summernote editor for pages
           if (typeof jQuery !== 'undefined' && jQuery('#content_pt').length && typeof jQuery.summernote !== 'undefined') {
               try {
                   content = jQuery('#content_pt').summernote('code');
               } catch (err) {
                   console.warn('Error getting Summernote content for page:', err);
                   content = document.getElementById('content_pt') ? document.getElementById('content_pt').value : '';
               }
           } else {
                content = document.getElementById('content_pt') ? document.getElementById('content_pt').value : '';
           }

           const tempDiv = document.createElement('div');
           tempDiv.innerHTML = content;
           const plainText = tempDiv.textContent || tempDiv.innerText || '';

           let shortDescription = plainText.substring(0, 160).trim();
           if (plainText.length > 160) {
               shortDescription += '...';
           }

           // Basic keyword extraction (can be improved)
           const commonWords = ['a', 'o', 'e', 'de', 'da', 'do', 'em', 'para', 'com', 'um', 'uma', 'os', 'as', 'que', 'por', 'na', 'no', 'se', 'ser', 'estar', 'ter', 'ir', 'fazer', 'poder', 'dizer', 'querer', 'ver', 'vir', 'ficar', 'dar', 'saber', 'haver', 'dever', 'passar', 'começar', 'deixar', 'parecer', 'encontrar', 'continuar', 'usar', 'levar', 'abrir', 'fechar', 'precisar', 'tentar', 'mudar', 'ajudar', 'sentir', 'ouvir', 'falar', 'escrever', 'ler', 'jogar', 'correr', 'andar', 'parar', 'voltar', 'entrar', 'sair', 'subir', 'descer', 'cair', 'nascer', 'morrer', 'viver', 'trabalhar', 'estudar', 'dormir', 'acordar', 'comer', 'beber', 'gostar', 'odiar', 'amar', 'pensar', 'lembrar', 'esquecer', 'entender', 'aprender', 'ensinar', 'mostrar', 'olhar', 'tocar', 'cheirar', 'provar', 'sentar', 'levantar', 'partir', 'chegar', 'viajar', 'comprar', 'vender', 'pagar', 'receber', 'pedir', 'responder', 'perguntar', 'chamar', 'esperar', 'procurar', 'enviar', 'receber', 'abrir', 'fechar', 'limpar', 'sujar', 'construir', 'destruir', 'ganhar', 'perder', 'escolher', 'decidir', 'acreditar', 'duvidar', 'sonhar', 'desejar', 'imaginar', 'criar', 'inventar', 'descobrir', 'explorar', 'conquistar', 'lutar', 'defender', 'atacar', 'proteger', 'salvar', 'curar', 'adoecer', 'melhorar', 'piorar', 'crescer', 'diminuir', 'aumentar', 'reduzir', 'transformar', 'adaptar', 'evoluir', 'revolucionar', 'inovar', 'organizar', 'planejar', 'executar', 'controlar', 'avaliar', 'corrigir', 'resolver', 'superar', 'vencer', 'celebrar', 'comemorar', 'participar', 'colaborar', 'competir', 'negociar', 'investir', 'economizar', 'gastar', 'doar', 'emprestar', 'pegar', 'devolver', 'guardar', 'perder', 'achar', 'sumir', 'aparecer', 'desaparecer', 'brilhar', 'escurecer', 'iluminar', 'aquecer', 'esfriar', 'molhar', 'secar', 'chover', 'nevar', 'ventar', 'nascer', 'pôr', 'florecer', 'murchar', 'cantar', 'dançar', 'rir', 'chorar', 'sorrir', 'gritar', 'sussurrar', 'silenciar', 'observar', 'escutar', 'prestar', 'atenção', 'ignorar', 'respeitar', 'desrespeitar', 'obedecer', 'desobedecer', 'perdoar', 'culpar', 'agradecer', 'desculpar', 'elogiar', 'criticar', 'aconselhar', 'sugerir', 'recomendar', 'proibir', 'permitir', 'autorizar', 'negar', 'confirmar', 'cancelar', 'adiar', 'antecipar', 'marcar', 'desmarcar', 'reservar', 'alugar', 'hospedar', 'visitar', 'conhecer', 'explorar', 'descansar', 'relaxar', 'divertir', 'entreter', 'cansar', 'aborrecer', 'surpreender', 'assustar', 'acalmar', 'irritar', 'animar', 'desanimar', 'motivar', 'inspirar', 'influenciar', 'manipular', 'enganar', 'mentir', 'confiar', 'desconfiar', 'trair', 'denunciar', 'acusar', 'defender', 'julgar', 'condenar', 'absolver', 'punir', 'castigar', 'libertar', 'prender', 'fugir', 'escapar', 'seguir', 'perseguir', 'acompanhar', 'guiar', 'orientar', 'dirigir', 'conduzir', 'pilotar', 'navegar', 'voar', 'nadar', 'mergulhar', 'escalar', 'caminhar', 'correr', 'saltar', 'pular', 'rastejar', 'rolar', 'girar', 'balançar', 'vibrar', 'tremer', 'explodir', 'implodir', 'queimar', 'apagar', 'acender', 'congelar', 'derreter', 'evaporar', 'condensar', 'solidificar', 'liquefazer', 'misturar', 'separar', 'dissolver', 'filtrar', 'cozinhar', 'assar', 'fritar', 'grelhar', 'ferver', 'refogar', 'temperar', 'cortar', 'picar', 'ralar', 'descascar', 'lavar', 'secar', 'limpar', 'arrumar', 'organizar', 'decorar', 'pintar', 'desenhar', 'esculpir', 'modelar', 'costurar', 'bordar', 'tricotar', 'crochetar', 'plantar', 'colher', 'regar', 'podar', 'cuidar', 'alimentar', 'vestir', 'calçar', 'pentear', 'maquiar', 'barbear', 'tomar', 'banho', 'escovar', 'dentes', 'lavar', 'mãos', 'estudar', 'ler', 'escrever', 'calcular', 'pesquisar', 'analisar', 'interpretar', 'concluir', 'apresentar', 'discutir', 'debater', 'argumentar', 'convencer', 'persuadir', 'negociar', 'votar', 'eleger', 'governar', 'legislar', 'julgar', 'executar', 'fiscalizar', 'protestar', 'reivindicar', 'manifestar', 'lutar', 'resistir', 'render', 'desistir', 'abandonar', 'recusar', 'aceitar', 'concordar', 'discordar', 'opor', 'apoiar', 'criticar', 'elogiar', 'condenar', 'perdoar', 'reconciliar', 'unir', 'separar', 'dividir', 'multiplicar', 'somar', 'subtrair', 'integrar', 'diferenciar', 'medir', 'pesar', 'contar', 'numerar', 'classificar', 'ordenar', 'agrupar', 'comparar', 'avaliar', 'testar', 'experimentar', 'verificar', 'comprovar', 'demonstrar', 'explicar', 'descrever', 'narrar', 'relatar', 'informar', 'comunicar', 'notificar', 'avisar', 'alertar', 'advertir', 'ameaçar', 'intimidar', 'chatear', 'incomodar', 'perturbar', 'distrair', 'concentrar', 'focar', 'relaxar', 'meditar', 'orar', 'rezar', 'agradecer', 'louvar', 'adorar', 'invocar', 'abençoar', 'amaldiçoar', 'conjurar', 'exorcizar', 'batizar', 'casar', 'divorciar', 'adotar', 'herdar', 'legar', 'testar', 'registrar', 'autenticar', 'validar', 'invalidar', 'certificar', 'credenciar', 'habilitar', 'desabilitar', 'ativar', 'desativar', 'bloquear', 'desbloquear', 'criptografar', 'descriptografar', 'compactar', 'descompactar', 'copiar', 'colar', 'recortar', 'mover', 'renomear', 'excluir', 'restaurar', 'salvar', 'carregar', 'imprimir', 'digitalizar', 'formatar', 'configurar', 'instalar', 'desinstalar', 'atualizar', 'restaurar', 'backup', 'sincronizar', 'conectar', 'desconectar', 'navegar', 'pesquisar', 'baixar', 'enviar', 'compartilhar', 'publicar', 'comentar', 'curtir', 'seguir', 'bloquear', 'denunciar', 'configurar', 'personalizar', 'customizar', 'ajustar', 'calibrar', 'otimizar', 'acelerar', 'desacelerar', 'frear', 'parar', 'iniciar', 'pausar', 'continuar', 'reiniciar', 'desligar', 'ligar', 'conectar', 'desconectar', 'montar', 'desmontar', 'construir', 'destruir', 'reformar', 'reparar', 'consertar', 'manter', 'inspecionar', 'diagnosticar', 'monitorar', 'rastrear', 'localizar', 'identificar', 'reconhecer', 'distinguir', 'diferenciar', 'separar', 'juntar', 'unir', 'combinar', 'misturar', 'agitar', 'mexer', 'sacudir', 'virar', 'dobrar', 'esticar', 'encolher', 'expandir', 'comprimir', 'torcer', 'apertar', 'afrouxar', 'amarrar', 'desamarrar', 'prender', 'soltar', 'fixar', 'remover', 'inserir', 'retirar', 'empurrar', 'puxar', 'levantar', 'abaixar', 'arremessar', 'lançar', 'atirar', 'pegar', 'largar', 'segurar', 'soltar', 'carregar', 'descarregar', 'transportar', 'entregar', 'receber', 'enviar', 'mandar', 'trazer', 'levar', 'buscar', 'procurar', 'achar', 'perder', 'esconder', 'revelar', 'cobrir', 'descobrir', 'vestir', 'despir', 'calçar', 'descalçar', 'abotoar', 'desabotoar', 'amarrar', 'desamarrar', 'pentear', 'escovar', 'lavar', 'secar', 'enxugar', 'limpar', 'sujar', 'polir', 'lixar', 'pintar', 'envernizar', 'encerar', 'raspar', 'cortar', 'aparar', 'podar', 'colher', 'semear', 'plantar', 'regar', 'adubar', 'cultivar', 'criar', 'alimentar', 'cuidar', 'proteger', 'domesticar', 'treinar', 'ensinar', 'educar', 'instruir', 'formar', 'orientar', 'aconselhar', 'guiar', 'liderar', 'gerenciar', 'administrar', 'coordenar', 'supervisionar', 'controlar', 'fiscalizar', 'auditar', 'avaliar', 'julgar', 'premiar', 'punir', 'promover', 'rebaixar', 'contratar', 'demitir', 'aposentar', 'recrutar', 'selecionar', 'entrevistar', 'negociar', 'contratar', 'comprar', 'vender', 'alugar', 'arrendar', 'emprestar', 'tomar', 'devolver', 'trocar', 'substituir', 'reparar', 'consertar', 'reformar', 'modernizar', 'atualizar', 'inovar', 'inventar', 'criar', 'desenvolver', 'projetar', 'planejar', 'executar', 'implementar', 'lançar', 'divulgar', 'promover', 'anunciar', 'publicar', 'distribuir', 'exportar', 'importar', 'armazenar', 'estocar', 'embalar', 'rotular', 'etiquetar', 'marcar', 'registrar', 'patenteаr', 'licenciar', 'franquear', 'segurar', 'proteger', 'garantir', 'financiar', 'investir', 'economizar', 'poupar', 'gastar', 'desperdiçar', 'doar', 'contribuir', 'apoiar', 'patrocinar', 'subsidiar', 'taxar', 'multar', 'indenizar', 'reembolsar', 'cobrar', 'pagar', 'receber', 'depositar', 'sacar', 'transferir', 'converter', 'cambiar', 'valorizar', 'desvalorizar', 'inflacionar', 'deflacionar', 'negociar', 'especular', 'arriscar', 'apostar', 'jogar', 'ganhar', 'perder', 'empatar', 'competir', 'colaborar', 'cooperar', 'associar', 'filiar', 'desfiliar', 'participar', 'assistir', 'presenciar', 'testemunhar', 'relatar', 'descrever', 'narrar', 'contar', 'informar', 'comunicar', 'noticiar', 'anunciar', 'divulgar', 'publicar', 'espalhar', 'boatar', 'fofocar', 'difamar', 'caluniar', 'injuriar', 'elogiar', 'criticar', 'avaliar', 'julgar', 'opinar', 'sugerir', 'recomendar', 'aconselhar', 'orientar', 'instruir', 'ensinar', 'educar', 'treinar', 'formar', 'capacitar', 'habilitar', 'qualificar', 'certificar', 'credenciar', 'promover', 'apoiar', 'incentivar', 'motivar', 'inspirar', 'estimular', 'desafiar', 'provocar', 'irritar', 'acalmar', 'confortar', 'consolar', 'animar', 'desanimar', 'alegrar', 'entristecer', 'emocionar', 'comover', 'surpreender', 'assustar', 'amedrontar', 'intimidar', 'ameaçar', 'persuadir', 'convencer', 'dissuadir', 'influenciar', 'manipular', 'controlar', 'dominar', 'submeter', 'obedecer', 'desobedecer', 'rebelar', 'revoltar', 'protestar', 'manifestar', 'reivindicar', 'exigir', 'pedir', 'implorar', 'suplicar', 'orar', 'rezar', 'meditar', 'refletir', 'pensar', 'raciocinar', 'analisar', 'deduzir', 'induzir', 'concluir', 'inferir', 'supor', 'imaginar', 'fantasiar', 'sonhar', 'lembrar', 'recordar', 'esquecer', 'ignorar', 'perceber', 'notar', 'observar', 'ver', 'olhar', 'escutar', 'ouvir', 'sentir', 'tocar', 'cheirar', 'provar', 'degustar', 'experimentar', 'testar', 'verificar', 'comprovar', 'confirmar', 'validar', 'certificar', 'autenticar', 'falsificar', 'imitar', 'copiar', 'plagiar', 'traduzir', 'interpretar', 'decifrar', 'desvendar', 'resolver', 'solucionar', 'desenvolver', 'evoluir', 'progredir', 'avançar', 'retroceder', 'regredir', 'estagnar', 'paralisar', 'continuar', 'prosseguir', 'interromper', 'suspender', 'cancelar', 'terminar', 'finalizar', 'concluir', 'começar', 'iniciar', 'retomar', 'recomeçar', 'nascer', 'crescer', 'amadurecer', 'envelhecer', 'morrer', 'renascer', 'transformar', 'mudar', 'adaptar', 'ajustar', 'modificar', 'alterar', 'variar', 'diferenciar', 'distinguir', 'separar', 'dividir', 'unir', 'juntar', 'reunir', 'agrupar', 'organizar', 'arrumar', 'classificar', 'ordenar', 'listar', 'tabular', 'graficar', 'mapear', 'desenhar', 'pintar', 'esculpir', 'modelar', 'construir', 'fabricar', 'produzir', 'montar', 'desmontar', 'instalar', 'configurar', 'programar', 'codificar', 'depurar', 'testar', 'otimizar', 'manter', 'atualizar', 'migrar', 'exportar', 'importar', 'integrar', 'conectar', 'comunicar', 'transmitir', 'receber', 'armazenar', 'processar', 'analisar', 'visualizar', 'apresentar', 'publicar', 'distribuir', 'compartilhar', 'vender', 'comprar', 'alugar', 'assinar', 'cancelar', 'renovar', 'gerenciar', 'administrar', 'liderar', 'coordenar', 'supervisionar', 'controlar', 'monitorar', 'auditar', 'avaliar', 'revisar', 'corrigir', 'melhorar', 'inovar', 'criar', 'inventar', 'descobrir', 'explorar', 'pesquisar', 'estudar', 'aprender', 'ensinar', 'treinar', 'palestrar', 'escrever', 'ler', 'editar', 'revisar', 'publicar', 'traduzir', 'legendar', 'dublar', 'fotografar', 'filmar', 'gravar', 'editar', 'produzir', 'dirigir', 'atuar', 'cantar', 'dançar', 'tocar', 'instrumento', 'compor', 'arranjar', 'conduzir', 'orquestra', 'exibir', 'apresentar', 'performar', 'participar', 'competir', 'jogar', 'treinar', 'arbitrar', 'torcer', 'apostar', 'viajar', 'explorar', 'acampar', 'pescar', 'caçar', 'mergulhar', 'escalar', 'surfar', 'esquiar', 'patinar', 'andar', 'bicicleta', 'dirigir', 'pilotar', 'navegar', 'voar', 'cozinhar', 'assar', 'fritar', 'grelhar', 'churrasquear', 'temperar', 'decorar', 'jardinar', 'cuidar', 'animais', 'colecionar', 'restaurar', 'consertar', 'fazer', 'bricolage', 'voluntariar', 'ajudar', 'doar', 'ensinar', 'aprender', 'línguas', 'praticar', 'esportes', 'tocar', 'instrumentos', 'musicais', 'dançar', 'cantar', 'atuar', 'escrever', 'ler', 'pintar', 'desenhar', 'esculpir', 'fotografar', 'filmar', 'cozinhar', 'jardinar', 'viajar', 'colecionar', 'fazer', 'artesanato', 'resolver', 'quebra-cabeças', 'jogar', 'videogames', 'assistir', 'filmes', 'séries', 'ouvir', 'música', 'ir', 'shows', 'eventos', 'sair', 'amigos', 'família', 'passar', 'tempo', 'natureza', 'meditar', 'relaxar', 'dormir', 'sonhar', 'refletir', 'planejar', 'organizar', 'limpar', 'arrumar', 'decorar', 'casa', 'cuidar', 'saúde', 'fazer', 'exercícios', 'alimentar-se', 'bem', 'beber', 'água', 'descansar', 'visitar', 'médico', 'dentista', 'fazer', 'exames', 'seguir', 'tratamentos', 'tomar', 'remédios', 'vacinar-se', 'proteger-se', 'prevenir', 'doenças', 'buscar', 'informações', 'aprender', 'coisas', 'novas', 'desenvolver', 'habilidades', 'superar', 'desafios', 'alcançar', 'metas', 'realizar', 'sonhos', 'ser', 'feliz', 'viver', 'plenamente', 'contribuir', 'mundo', 'melhor', 'deixar', 'legado', 'ser', 'lembrado', 'inspirar', 'outros', 'amar', 'ser', 'amado', 'respeitar', 'ser', 'respeitado', 'confiar', 'ser', 'confiável', 'ajudar', 'ser', 'ajudado', 'perdoar', 'ser', 'perdoado', 'agradecer', 'ser', 'grato', 'sorrir', 'fazer', 'outros', 'sorrirem', 'celebrar', 'vida', 'aproveitar', 'cada', 'momento'].map(w => w.toLowerCase());

           let words = plainText.toLowerCase()
               .replace(/[^\w\s]/g, ' ') // Remove punctuation
               .split(/\s+/) // Split by spaces
               .filter(word => word.length > 3 && !commonWords.includes(word)); // Filter short and common words

           const wordCount = {};
           words.forEach(word => {
               wordCount[word] = (wordCount[word] || 0) + 1;
           });

           const sortedWords = Object.keys(wordCount).sort((a, b) => wordCount[b] - wordCount[a]);
           let keywords = sortedWords.slice(0, 8).join(', ');

           // Add title words if not already included
           const titleWords = title.toLowerCase()
               .replace(/[^\w\s]/g, ' ')
               .split(/\s+/)
               .filter(word => word.length > 3 && !commonWords.includes(word));

           titleWords.forEach(word => {
               if (!keywords.toLowerCase().includes(word.toLowerCase())) { // Case-insensitive check
                   keywords = word + ', ' + keywords;
               }
           });
            // Limit keywords string length to avoid overly long strings
           if (keywords.length > 250) {
               keywords = keywords.substring(0, 250) + "...";
           }

           // Fill SEO fields
           document.getElementById('seo_title').value = title;
           document.getElementById('seo_description').value = shortDescription;
           document.getElementById('seo_keywords').value = keywords;

           // Fill Open Graph fields
           document.getElementById('og_title').value = title;
           document.getElementById('og_description').value = shortDescription;
           // For pages, og_image and twitter_image are left blank by default as there's no primary image

           // Fill Twitter Card fields
           document.getElementById('twitter_title').value = title;
           document.getElementById('twitter_description').value = shortDescription;

           alert('Campos SEO e Social preenchidos automaticamente com sucesso!');
       });
   }

   // Summernote for #content_pt is now initialized by the global
   // summernote-config.js script which targets '.summernote-editor'
   // No specific initialization is needed here anymore.
});
</script>
<!-- Page form uses the slug generation functionality from admin.js -->
