<?php

function get_product_videos(int $product_id, ?int $variation_id = null): array|false
{
    if ($product_id <= 0) return false;
    
    $sql = "SELECT * FROM product_videos WHERE product_id = :pid";
    $params = [':pid' => $product_id];
    
    if ($variation_id !== null) {
        $sql .= " AND (variation_id = :vid OR variation_id IS NULL)";
        $params[':vid'] = $variation_id;
    }
    
    $sql .= " ORDER BY sort_order ASC, id ASC";
    
    return db_query($sql, $params, false, true);
}

function get_product_video_by_id(int $video_id): array|false
{
    if ($video_id <= 0) return false;
    
    $sql = "SELECT * FROM product_videos WHERE id = :id";
    return db_query($sql, [':id' => $video_id], true);
}

function add_product_video(
    int $product_id, 
    string $video_type, 
    ?string $filename = null, 
    ?string $video_url = null, 
    ?string $thumbnail_filename = null, 
    ?int $variation_id = null
): int|false
{
    if ($product_id <= 0) return false;
    if (!in_array($video_type, ['uploaded', 'external'])) return false;
    if ($video_type === 'uploaded' && empty($filename)) return false;
    if ($video_type === 'external' && empty($video_url)) return false;
    
    $sql = "INSERT INTO product_videos (
                product_id, 
                filename, 
                video_url, 
                video_type, 
                thumbnail_filename, 
                variation_id,
                sort_order
            ) VALUES (
                :pid, 
                :filename, 
                :video_url, 
                :video_type, 
                :thumbnail, 
                :vid,
                (SELECT IFNULL(MAX(sort_order), -1) + 1 FROM product_videos WHERE product_id = :pid2)
            )";
    
    $params = [
        ':pid' => $product_id,
        ':filename' => $filename,
        ':video_url' => $video_url,
        ':video_type' => $video_type,
        ':thumbnail' => $thumbnail_filename,
        ':vid' => $variation_id,
        ':pid2' => $product_id
    ];
    
    return db_query($sql, $params);
}

function update_product_video(int $video_id, array $data): bool
{
    if ($video_id <= 0 || empty($data)) return false;
    
    $allowed_fields = ['filename', 'video_url', 'video_type', 'thumbnail_filename', 'variation_id', 'sort_order'];
    $updates = [];
    $params = [':id' => $video_id];
    
    foreach ($data as $field => $value) {
        if (in_array($field, $allowed_fields)) {
            $updates[] = "$field = :$field";
            $params[":$field"] = $value;
        }
    }
    
    if (empty($updates)) return false;
    
    $sql = "UPDATE product_videos SET " . implode(', ', $updates) . " WHERE id = :id";
    return db_query($sql, $params) !== false;
}

function delete_product_video(int $video_id): bool
{
    if ($video_id <= 0) return false;
    
    
    $video = get_product_video_by_id($video_id);
    if (!$video) return false;
    
    
    if ($video['video_type'] === 'uploaded' && !empty($video['filename'])) {
        $video_path = __DIR__ . '/../public/assets/videos/products/' . $video['filename'];
        if (file_exists($video_path)) {
            unlink($video_path);
        }
    }
    
    
    if (!empty($video['thumbnail_filename'])) {
        $thumbnail_path = __DIR__ . '/../public/assets/images/video_thumbnails/' . $video['thumbnail_filename'];
        if (file_exists($thumbnail_path)) {
            unlink($thumbnail_path);
        }
    }
    
    $sql = "DELETE FROM product_videos WHERE id = :id";
    return db_query($sql, [':id' => $video_id]) !== false;
}

function generate_video_thumbnail(string $video_path, string $output_path): bool
{
    
    $ffmpeg_available = false;
    exec('ffmpeg -version', $output, $return_var);
    $ffmpeg_available = ($return_var === 0);
    
    if ($ffmpeg_available) {
        
        $command = "ffmpeg -i \"$video_path\" -ss 00:00:01 -frames:v 1 \"$output_path\" 2>&1";
        exec($command, $output, $return_var);
        return ($return_var === 0 && file_exists($output_path));
    }
    
    
    
    return false;
}

function extract_youtube_video_id(string $url): string|false
{
    $pattern = '/(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/';
    if (preg_match($pattern, $url, $matches)) {
        return $matches[1];
    }
    return false;
}

function get_youtube_thumbnail_url(string $video_id): string
{
    return "https://img.youtube.com/vi/$video_id/maxresdefault.jpg";
}

function download_file(string $url, string $save_path): bool
{
    $content = file_get_contents($url);
    if ($content === false) return false;
    return file_put_contents($save_path, $content) !== false;
}

function get_video_url(array $video): string
{
    if ($video['video_type'] === 'uploaded') {
        return get_asset_url('videos/products/' . $video['filename']);
    } else {
        return $video['video_url'];
    }
}

function get_video_thumbnail_url(array $video): string
{
    if (!empty($video['thumbnail_filename'])) {
        return get_asset_url('images/video_thumbnails/' . $video['thumbnail_filename']);
    }
    
    if ($video['video_type'] === 'external') {
        $youtube_id = extract_youtube_video_id($video['video_url']);
        if ($youtube_id) {
            return get_youtube_thumbnail_url($youtube_id);
        }
    }
    
    
    return get_asset_url('images/video_placeholder.png');
}

function is_valid_video_url(string $url): bool
{
    
    if (extract_youtube_video_id($url) !== false) {
        return true;
    }
    
    
    
    return false;
}

function get_video_embed_code(string $url): string|false
{
    $youtube_id = extract_youtube_video_id($url);
    if ($youtube_id) {
        return '<iframe width="100%" height="100%" src="https://www.youtube.com/embed/' . $youtube_id . '" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>';
    }
    
    
    
    return false;
}
