<?php

require_once __DIR__ . '/db.php';
require_once __DIR__ . '/functions.php'; 

function get_blog_categories(bool $only_active = false): array
{
    $sql = "SELECT * FROM blog_categories";
    $params = [];
    if ($only_active) {
        $sql .= " WHERE is_active = ?";
        $params[] = 1;
    }
    $sql .= " ORDER BY name ASC";
    return db_query($sql, $params, false, true) ?: [];
}

function get_blog_category(int|string $id_or_slug): array|false
{
    if (is_numeric($id_or_slug)) {
        $sql = "SELECT * FROM blog_categories WHERE id = ?";
    } else {
        $sql = "SELECT * FROM blog_categories WHERE slug = ?";
    }
    return db_query($sql, [$id_or_slug], true);
}

function add_blog_category(array $data): int|false
{
    if (empty($data['name'])) {
        return false; 
    }
    $slug = generate_slug($data['name']);
    $sql = "INSERT INTO blog_categories (name, slug, description, is_active) VALUES (?, ?, ?, ?)";
    $params = [
        $data['name'],
        $slug,
        $data['description'] ?? '',
        isset($data['is_active']) ? (int)$data['is_active'] : 1
    ];

    if (db_query($sql, $params)) {
        $pdo = get_db_connection();
        return $pdo ? $pdo->lastInsertId() : false;
    }
    return false;
}

function update_blog_category(int $id, array $data): bool
{
    if (empty($data['name'])) {
        return false; 
    }
    $slug = generate_slug($data['name']);
    $sql = "UPDATE blog_categories SET name = ?, slug = ?, description = ?, is_active = ?, updated_at = datetime('now', 'localtime') WHERE id = ?";
    $params = [
        $data['name'],
        $slug,
        $data['description'] ?? '',
        isset($data['is_active']) ? (int)$data['is_active'] : 1,
        $id
    ];
    return db_query($sql, $params) !== false;
}

function delete_blog_category(int $id): bool
{
    
    
    
    $sql = "DELETE FROM blog_categories WHERE id = ?";
    return db_query($sql, [$id]) !== false;
}

function get_blog_posts(array $options = []): array
{
    $pdo = get_db_connection();
    if (!$pdo) return ['posts' => [], 'total_count' => 0];

    $base_sql = "FROM blog_posts p";
    $count_sql = "SELECT COUNT(DISTINCT p.id) " . $base_sql;
    $select_sql = "SELECT DISTINCT p.* " . $base_sql; 
    $joins = "";
    $where = [];
    $params = [];

    
    if (!empty($options['category_id']) || !empty($options['category_slug'])) {
        $joins .= " LEFT JOIN blog_post_categories bpc ON p.id = bpc.post_id";
        $joins .= " LEFT JOIN blog_categories bc ON bpc.category_id = bc.id";
        if (!empty($options['category_id'])) {
            $where[] = "bpc.category_id = ?";
            $params[] = (int)$options['category_id'];
        } else {
            $where[] = "bc.slug = ?";
            $params[] = $options['category_slug'];
        }
    }

    
    if (!empty($options['post_type']) && in_array($options['post_type'], ['article', 'link'])) {
        $where[] = "p.post_type = ?";
        $params[] = $options['post_type'];
    }

    
    if (isset($options['is_published'])) {
        $where[] = "p.is_published = ?";
        $params[] = (int)$options['is_published'];
    }

    
    if (!empty($options['search_term'])) {
        $term = '%' . $options['search_term'] . '%';
        $where[] = "(p.title LIKE ? OR (p.post_type = 'article' AND p.content LIKE ?) OR (p.post_type = 'link' AND p.link_description LIKE ?))";
        $params[] = $term;
        $params[] = $term;
        $params[] = $term;
    }

    
    $where_clause = "";
    if (!empty($where)) {
        $where_clause = " WHERE " . implode(" AND ", $where);
    }

    
    $count_stmt = $pdo->prepare($count_sql . $joins . $where_clause);
    $count_stmt->execute($params);
    $total_count = (int)$count_stmt->fetchColumn();

    
    $order_by = $options['order_by'] ?? 'published_at';
    $order_dir = $options['order_dir'] ?? 'DESC';
    
    $valid_order_cols = ['published_at', 'created_at', 'title', 'updated_at'];
    $valid_order_dirs = ['ASC', 'DESC'];
    if (!in_array($order_by, $valid_order_cols)) $order_by = 'published_at';
    if (!in_array(strtoupper($order_dir), $valid_order_dirs)) $order_dir = 'DESC';

    $order_clause = " ORDER BY p." . $order_by . " " . $order_dir;

    $limit_clause = "";
    if (isset($options['per_page'])) { 
        $per_page = max(1, (int)$options['per_page']);
        
        $page = isset($options['page']) ? max(1, (int)$options['page']) : 1;
        $offset = ($page - 1) * $per_page;

        $limit_clause = " LIMIT ? OFFSET ?";
        $params[] = $per_page;
        $params[] = $offset;
    }

    $full_sql = $select_sql . $joins . $where_clause . $order_clause . $limit_clause;
    $posts_stmt = $pdo->prepare($full_sql);
    $posts_stmt->execute($params);
    $posts = $posts_stmt->fetchAll() ?: [];

    
    if (!empty($posts)) {
        $post_ids = array_column($posts, 'id');
        $placeholders = rtrim(str_repeat('?,', count($post_ids)), ',');
        $cat_sql = "SELECT bpc.post_id, bc.id, bc.name, bc.slug
                    FROM blog_post_categories bpc
                    JOIN blog_categories bc ON bpc.category_id = bc.id
                    WHERE bpc.post_id IN ($placeholders)";
        $cat_stmt = $pdo->prepare($cat_sql);
        $cat_stmt->execute($post_ids);
        $category_map = [];
        while ($cat = $cat_stmt->fetch()) {
            $category_map[$cat['post_id']][] = $cat;
        }
        foreach ($posts as &$post) {
            $post['categories'] = $category_map[$post['id']] ?? [];
        }
        unset($post); 
    }

    return ['posts' => $posts, 'total_count' => $total_count];
}

function get_blog_post(int|string $id_or_slug): array|false
{
    if (is_numeric($id_or_slug)) {
        $sql = "SELECT * FROM blog_posts WHERE id = ?";
    } else {
        $sql = "SELECT * FROM blog_posts WHERE slug = ?";
    }
    $post = db_query($sql, [$id_or_slug], true);

    if ($post) {
        
        $cat_sql = "SELECT bc.id, bc.name, bc.slug
                    FROM blog_post_categories bpc
                    JOIN blog_categories bc ON bpc.category_id = bc.id
                    WHERE bpc.post_id = ?";
        $post['categories'] = db_query($cat_sql, [$post['id']], false, true) ?: [];
    }

    return $post;
}

function add_blog_post(array $data): int|false
{
    $pdo = get_db_connection();
    if (!$pdo || empty($data['title']) || empty($data['post_type']) || !in_array($data['post_type'], ['article', 'link', 'CODE']) || empty($data['image_path'])) {
        return false;
    }

    $slug = generate_slug($data['title']);
    
    $i = 1;
    $original_slug = $slug;
    while (db_query("SELECT id FROM blog_posts WHERE slug = ?", [$slug], true)) {
        $slug = $original_slug . '-' . $i++;
    }

    $sql = "INSERT INTO blog_posts (
                title, slug, post_type, content, link_url, link_description, code_content, image_path, image_description, is_published, published_at,
                seo_title, seo_description, seo_keywords, og_title, og_description, og_image,
                twitter_card, twitter_title, twitter_description, twitter_image, created_at, updated_at
            ) VALUES (
                :title, :slug, :post_type, :content, :link_url, :link_description, :code_content, :image_path, :image_description, :is_published, :published_at,
                :seo_title, :seo_description, :seo_keywords, :og_title, :og_description, :og_image,
                :twitter_card, :twitter_title, :twitter_description, :twitter_image, datetime('now', 'localtime'), datetime('now', 'localtime')
            )";

    $is_published = isset($data['is_published']) ? (int)$data['is_published'] : 0;
    $published_at = ($is_published && empty($data['published_at'])) ? date('Y-m-d H:i:s') : ($data['published_at'] ?? null);

    $params = [
        ':title' => $data['title'],
        ':slug' => $slug,
        ':post_type' => $data['post_type'],
        ':content' => ($data['post_type'] === 'article') ? ($data['content'] ?? '') : null,
        ':link_url' => ($data['post_type'] === 'link') ? ($data['link_url'] ?? '') : null,
        ':link_description' => ($data['post_type'] === 'link' || $data['post_type'] === 'CODE') ? ($data['link_description'] ?? '') : null,
        ':code_content' => ($data['post_type'] === 'CODE') ? ($data['code_content'] ?? '') : null,
        ':image_path' => $data['image_path'],
        ':image_description' => $data['image_description'] ?? null,
        ':is_published' => $is_published,
        ':published_at' => $published_at,
        ':seo_title' => $data['seo_title'] ?? null,
        ':seo_description' => $data['seo_description'] ?? null,
        ':seo_keywords' => $data['seo_keywords'] ?? null,
        ':og_title' => $data['og_title'] ?? null,
        ':og_description' => $data['og_description'] ?? null,
        ':og_image' => $data['og_image'] ?? $data['image_path'], 
        ':twitter_card' => $data['twitter_card'] ?? 'summary_large_image',
        ':twitter_title' => $data['twitter_title'] ?? null,
        ':twitter_description' => $data['twitter_description'] ?? null,
        ':twitter_image' => $data['twitter_image'] ?? $data['image_path'] 
    ];

    try {
        $pdo->beginTransaction();

        $stmt = $pdo->prepare($sql);
        if (!$stmt->execute($params)) {
            $pdo->rollBack();
            return false;
        }
        $post_id = $pdo->lastInsertId();

        
        if (!empty($data['category_ids']) && is_array($data['category_ids'])) {
            $cat_sql = "INSERT INTO blog_post_categories (post_id, category_id) VALUES (?, ?)";
            $cat_stmt = $pdo->prepare($cat_sql);
            foreach ($data['category_ids'] as $cat_id) {
                if (is_numeric($cat_id)) {
                    $cat_stmt->execute([$post_id, (int)$cat_id]);
                }
            }
        }

        $pdo->commit();
        return (int)$post_id;

    } catch (PDOException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function update_blog_post(int $id, array $data): bool
{
    $pdo = get_db_connection();
    if (!$pdo || empty($data['title']) || empty($data['post_type']) || !in_array($data['post_type'], ['article', 'link', 'CODE'])) {
        return false;
    }

    

    
    $current_post = get_blog_post($id);
    if (!$current_post) return false;

    

    $slug = $current_post['slug'];
    if ($current_post['title'] !== $data['title']) {
        $slug = generate_slug($data['title']);
        
        $i = 1;
        $original_slug = $slug;
        while (db_query("SELECT id FROM blog_posts WHERE slug = ? AND id != ?", [$slug, $id], true)) {
            $slug = $original_slug . '-' . $i++;
        }
    }

    $sql = "UPDATE blog_posts SET
                title = :title,
                slug = :slug,
                post_type = :post_type,
                content = :content,
                link_url = :link_url,
                link_description = :link_description,
                code_content = :code_content,
                image_path = :image_path,
                image_description = :image_description,
                is_published = :is_published,
                published_at = :published_at,
                seo_title = :seo_title,
                seo_description = :seo_description,
                seo_keywords = :seo_keywords,
                og_title = :og_title,
                og_description = :og_description,
                og_image = :og_image,
                twitter_card = :twitter_card,
                twitter_title = :twitter_title,
                twitter_description = :twitter_description,
                twitter_image = :twitter_image,
                updated_at = datetime('now', 'localtime')
            WHERE id = :id";

    

    
    $is_published = isset($data['is_published']) ? (int)$data['is_published'] : (int)$current_post['is_published'];

    

    
    $published_at = $current_post['published_at'];

    

    
    if ($is_published) {
        if (!$current_post['is_published']) {
            
            $published_at = date('Y-m-d H:i:s');
        } elseif (!empty($data['published_at'])) {
            
            $published_at = $data['published_at'];
        } elseif (empty($published_at)) {
            
            $published_at = date('Y-m-d H:i:s');
        }
        
    } else {
        
        $published_at = null;
    }

    

    $params = [
        ':id' => $id,
        ':title' => $data['title'],
        ':slug' => $slug,
        ':post_type' => $data['post_type'],
        ':content' => ($data['post_type'] === 'article') ? ($data['content'] ?? $current_post['content']) : null,
        ':link_url' => ($data['post_type'] === 'link') ? ($data['link_url'] ?? $current_post['link_url']) : null,
        ':link_description' => ($data['post_type'] === 'link' || $data['post_type'] === 'CODE') ? ($data['link_description'] ?? $current_post['link_description']) : null,
        ':code_content' => ($data['post_type'] === 'CODE') ? ($data['code_content'] ?? $current_post['code_content']) : null,
        ':image_path' => $data['image_path'] ?? $current_post['image_path'],
        ':image_description' => $data['image_description'] ?? $current_post['image_description'] ?? null,
        ':is_published' => $is_published,
        ':published_at' => $published_at,
        ':seo_title' => $data['seo_title'] ?? $current_post['seo_title'],
        ':seo_description' => $data['seo_description'] ?? $current_post['seo_description'],
        ':seo_keywords' => $data['seo_keywords'] ?? $current_post['seo_keywords'],
        ':og_title' => $data['og_title'] ?? $current_post['og_title'],
        ':og_description' => $data['og_description'] ?? $current_post['og_description'],
        ':og_image' => !empty($data['og_image']) ? $data['og_image'] : ($data['image_path'] ?? $current_post['og_image']),
        ':twitter_card' => $data['twitter_card'] ?? $current_post['twitter_card'],
        ':twitter_title' => $data['twitter_title'] ?? $current_post['twitter_title'],
        ':twitter_description' => $data['twitter_description'] ?? $current_post['twitter_description'],
        ':twitter_image' => !empty($data['twitter_image']) ? $data['twitter_image'] : ($data['image_path'] ?? $current_post['twitter_image'])
    ];

    try {
        $pdo->beginTransaction();

        $stmt = $pdo->prepare($sql);
        if (!$stmt->execute($params)) {
            $pdo->rollBack();
            return false;
        }

        
        $del_sql = "DELETE FROM blog_post_categories WHERE post_id = ?";
        db_query($del_sql, [$id]);

        if (!empty($data['category_ids']) && is_array($data['category_ids'])) {
            $cat_sql = "INSERT INTO blog_post_categories (post_id, category_id) VALUES (?, ?)";
            $cat_stmt = $pdo->prepare($cat_sql);
            foreach ($data['category_ids'] as $cat_id) {
                 if (is_numeric($cat_id)) {
                     $cat_stmt->execute([$id, (int)$cat_id]);
                 }
            }
        }

        $pdo->commit();

        

        return true;

    } catch (PDOException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function delete_blog_post(int $id): bool
{
    
    
    $sql = "DELETE FROM blog_posts WHERE id = ?";
    return db_query($sql, [$id]) !== false;
}

function get_blog_posts_per_page_setting(): int
{
    $pdo = get_db_connection();
    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
    $stmt->execute(['blog_posts_per_page']);
    $value = $stmt->fetchColumn();
    return $value ? (int)$value : 5; 
}

function get_blog_slider_count_setting(): int
{
    $pdo = get_db_connection();
    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
    $stmt->execute(['blog_slider_count']);
    $value = $stmt->fetchColumn();
    return $value ? (int)$value : 3; 
}

function get_blog_slider_delay_setting(): int
{
    $pdo = get_db_connection();
    $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
    $stmt->execute(['blog_slider_delay']);
    $value = $stmt->fetchColumn();
    return $value ? (int)$value : 5; 
}

function get_active_categories_with_posts(): array
{
    $pdo = get_db_connection();
    if (!$pdo) return [];

    $sql = "SELECT DISTINCT bc.id, bc.name, bc.slug, bc.description, COUNT(p.id) as post_count
            FROM blog_categories bc
            JOIN blog_post_categories bpc ON bc.id = bpc.category_id
            JOIN blog_posts p ON bpc.post_id = p.id
            WHERE bc.is_active = 1 AND p.is_published = 1
            GROUP BY bc.id
            ORDER BY bc.name ASC";

    return db_query($sql, [], false, true) ?: [];
}

?>