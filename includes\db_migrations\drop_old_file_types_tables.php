<?php

/**
 * Migration to drop the old file type tables:
 * 1. digital_product_file_types
 * 2. digital_product_file_type_associations
 */
function drop_old_file_types_tables()
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return [
            'success' => false,
            'message' => 'Erro ao conectar à base de dados.'
        ];
    }

    try {
        // Start transaction
        $pdo->beginTransaction();

        // Step 1: Check if the tables exist
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_product_file_types'");
        $old_table_exists = $stmt->fetch();

        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_product_file_type_associations'");
        $old_assoc_table_exists = $stmt->fetch();

        if (!$old_table_exists && !$old_assoc_table_exists) {
            // Old tables don't exist, nothing to drop
            $pdo->commit();
            return [
                'success' => true,
                'message' => 'As tabelas antigas não existem, nada para remover.'
            ];
        }

        // Step 2: Drop the digital_product_file_type_associations table if it exists
        if ($old_assoc_table_exists) {
            $pdo->exec("DROP TABLE IF EXISTS digital_product_file_type_associations");
            $pdo->exec("DROP INDEX IF EXISTS idx_digital_product_file_type_associations_product_id");
            $pdo->exec("DROP INDEX IF EXISTS idx_digital_product_file_type_associations_type_id");
        }

        // Step 3: Drop the digital_product_file_types table if it exists
        if ($old_table_exists) {
            $pdo->exec("DROP TABLE IF EXISTS digital_product_file_types");
            $pdo->exec("DROP INDEX IF EXISTS idx_digital_product_file_types_name");
            $pdo->exec("DROP INDEX IF EXISTS idx_digital_product_file_types_extension");
        }

        // Record the migration
        $stmt = $pdo->prepare("INSERT INTO migrations (name, executed_at) VALUES (:name, datetime('now', 'localtime'))");
        $stmt->execute([':name' => 'drop_old_file_types_tables']);

        // Commit transaction
        $pdo->commit();
        
        return [
            'success' => true,
            'message' => 'Tabelas antigas removidas com sucesso.'
        ];
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Error dropping old file types tables: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'Erro ao remover tabelas antigas: ' . $e->getMessage()
        ];
    }
}
