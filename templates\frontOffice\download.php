<?php

require_once __DIR__ . '/../../includes/digital_product_functions.php';
require_once __DIR__ . '/../../includes/digital_order_functions.php';

$license_code = '';
$error_message = '';
$success_message = '';
$download_ready = false;
$download_info = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'Erro de segurança. Por favor, tente novamente.';
    } else {
        $license_code = trim($_POST['license_code'] ?? '');
        
        if (empty($license_code)) {
            $error_message = 'Por favor, insira um código de licença.';
        } else {
            
            $result = process_download_request($license_code);
            
            if ($result['success']) {
                $download_ready = true;
                $download_info = $result;
                $success_message = 'Seu download está pronto!';
                
                
                $_SESSION['download_file'] = [
                    'path' => $result['file_path'],
                    'name' => $result['file_name'],
                    'expires' => time() + 300 
                ];
                
                
                header('Location: index.php?view=download_file&token=' . bin2hex(random_bytes(16)));
                exit;
            } else {
                $error_message = $result['message'];
            }
        }
    }
} elseif (isset($_GET['code'])) {
    
    $license_code = trim($_GET['code']);
    
    if (!empty($license_code)) {
        
        $validity = check_license_validity($license_code);
        
        if ($validity['valid']) {
            
            $success_message = 'Licença válida. Clique no botão abaixo para fazer o download.';
        } else {
            $error_message = $validity['message'];
        }
    }
}

$license_info = null;
if (!empty($license_code)) {
    $license = get_license_by_code($license_code);
    
    if ($license) {
        $license_info = [
            'customer_name' => $license['customer_name'],
            'customer_email' => get_censored_email($license['customer_email']),
            'expiry_date' => date('d/m/Y', strtotime($license['expiry_date'])),
            'downloads_used' => $license['downloads_used'],
            'download_limit' => $license['download_limit'],
            'downloads_remaining' => max(0, $license['download_limit'] - $license['downloads_used'])
        ];
    }
}
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h1 class="h3 mb-0">Download de Produtos Digitais</h1>
                </div>
                <div class="card-body">
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger" role="alert">
                            <?php echo htmlspecialchars($error_message); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success" role="alert">
                            <?php echo htmlspecialchars($success_message); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($license_info): ?>
                        <div class="card mb-4">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Informações da Licença</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-striped">
                                    <tr>
                                        <th>Cliente:</th>
                                        <td><?php echo htmlspecialchars($license_info['customer_name']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Email:</th>
                                        <td><?php echo htmlspecialchars($license_info['customer_email']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Expira em:</th>
                                        <td><?php echo htmlspecialchars($license_info['expiry_date']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Downloads restantes:</th>
                                        <td>
                                            <?php echo htmlspecialchars($license_info['downloads_remaining']); ?> 
                                            de <?php echo htmlspecialchars($license_info['download_limit']); ?>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <form method="post" action="index.php?view=download">
                        <?php echo csrf_token_field(); ?>
                        
                        <div class="mb-3">
                            <label for="license_code" class="form-label">Código de Licença</label>
                            <input type="text" class="form-control" id="license_code" name="license_code" 
                                   value="<?php echo htmlspecialchars($license_code); ?>" required
                                   placeholder="Ex: XXXX-XXXX-JCS-XXXX-XXXX">
                            <div class="form-text">
                                Insira o código de licença que você recebeu por email após a compra.
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-download me-2"></i> Fazer Download
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-4">
                        <h5>Não tem um código de licença?</h5>
                        <p>
                            Se comprou um produto digital na loja JCS, o código de licença foi-lhe enviado para o email que forneceu durante a compra. Verifique sua caixa de entrada e pasta de spam.
                        </p>
                        <p>
                            Se não encontrar o email ou tiver problemas com seu download, entre em contato conosco 
                            através da <a href="index.php?view=contact">página de contato</a>.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
