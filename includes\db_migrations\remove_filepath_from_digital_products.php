<?php

require_once __DIR__ . '/../../config.php'; 
require_once __DIR__ . '/../db.php';       

function remove_filepath_from_digital_products_table(): bool
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        $pdo->beginTransaction();

        
        $stmt = $pdo->query("PRAGMA table_info(digital_products)");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $column_exists = false;
        foreach ($columns as $column) {
            if ($column['name'] === 'file_path') {
                $column_exists = true;
                break;
            }
        }

        if ($column_exists) {
            
            
            
            
            
            

            
            $fk_stmt = $pdo->query("PRAGMA foreign_keys;");
            $fk_enabled = (bool) $fk_stmt->fetchColumn();
            if ($fk_enabled) {
                $pdo->exec("PRAGMA foreign_keys = OFF;");
            }

            $pdo->exec("ALTER TABLE digital_products RENAME TO digital_products_old;");

            
            
            
            $pdo->exec("CREATE TABLE digital_products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL UNIQUE,
                digital_file_id INTEGER, -- Foreign key to digital_files
                expiry_days INTEGER DEFAULT 5,
                download_limit INTEGER DEFAULT 3,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
                FOREIGN KEY (digital_file_id) REFERENCES digital_files(id) ON DELETE SET NULL
            )");
             
            $pdo->exec("CREATE INDEX IF NOT EXISTS idx_digital_products_product_id ON digital_products (product_id)");
            $pdo->exec("CREATE INDEX IF NOT EXISTS idx_digital_products_file_id ON digital_products (digital_file_id)");

            
            $pdo->exec("INSERT INTO digital_products (id, product_id, digital_file_id, expiry_days, download_limit, created_at, updated_at)
                        SELECT id, product_id, digital_file_id, expiry_days, download_limit, created_at, updated_at
                        FROM digital_products_old;");

            $pdo->exec("DROP TABLE digital_products_old;");
            
            
            if ($fk_enabled) {
                $pdo->exec("PRAGMA foreign_keys = ON;");
            }
        } else {
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        
        try {
            if (isset($fk_enabled) && $fk_enabled) {
                 $pdo->exec("PRAGMA foreign_keys = ON;");
            }
        } catch (Exception $fk_e) {
        }
        return false;
    }
}

function check_and_remove_filepath_from_digital_products(PDO $pdo): void {
    
    
    $stmt = $pdo->query("PRAGMA table_info(digital_products)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $column_exists = false;
    foreach ($columns as $column) {
        if ($column['name'] === 'file_path') {
            $column_exists = true;
            break;
        }
    }

    if ($column_exists) {
        
        
        
        
        
        remove_filepath_from_digital_products_table();
    }
}

if (php_sapi_name() === 'cli' && realpath($argv[0]) === realpath(__FILE__)) {
    echo "Running migration: Remove file_path from digital_products table...\n";
    if (remove_filepath_from_digital_products_table()) {
        echo "Migration completed successfully.\n";
    } else {
        echo "Migration failed. Check error logs.\n";
    }
}

?>