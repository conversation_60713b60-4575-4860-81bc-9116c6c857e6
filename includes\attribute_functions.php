<?php

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/db.php';
require_once __DIR__ . '/functions.php';

function get_attribute_by_id(int $id): array|false
{
    if ($id <= 0) return false;
    $sql = "SELECT * FROM attributes WHERE id = :id";
    $attribute = db_query($sql, [':id' => $id], true); 
    return is_array($attribute) ? $attribute : false;
}

function get_attribute_values(int $attribute_id): array
{
    if ($attribute_id <= 0) return [];
    $sql = "SELECT * FROM attribute_values WHERE attribute_id = :aid ORDER BY value_pt ASC";
    $values = db_query($sql, [':aid' => $attribute_id], false, true); 
    return is_array($values) ? $values : [];
}
