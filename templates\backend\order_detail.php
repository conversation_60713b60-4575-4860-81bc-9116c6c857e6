<?php

require_once __DIR__ . '/../../includes/order_functions.php';
require_once __DIR__ . '/../../includes/order_statuses.php';

if (!isset($item_id)) {
    echo '<div class="alert alert-danger">ID da encomenda não especificado.</div>';
    return;
}

$order = db_query("SELECT * FROM orders WHERE id = :id", [':id' => $item_id], true);

if (!$order) {
     echo '<div class="alert alert-danger">Encomenda não encontrada.</div>';
    return;
}

$order_items = db_query("SELECT * FROM order_items WHERE order_id = :order_id", [':order_id' => $item_id], false, true);

$customer_info = json_decode($order['customer_info_json'] ?? '{}', true);
$terms_log = json_decode($order['terms_log_json'] ?? '{}', true);
$is_anonymized = isset($customer_info['anonymized']) && $customer_info['anonymized'];

$has_digital_products = false;
if (isset($order['has_digital_products']) && $order['has_digital_products']) {
    $has_digital_products = true;

    
    require_once __DIR__ . '/../../includes/digital_product_functions.php';

    
    $licenses = db_query("SELECT * FROM licenses WHERE order_id = :order_id", [':order_id' => $item_id], false, true);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    
    $submitted_csrf = $_POST['csrf_token'] ?? '';
    if (!validate_csrf_token($submitted_csrf)) {
        add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
        $_SESSION['redirect_to'] = 'admin.php?section=orders&action=detail&id=' . $item_id . '&' . get_session_id_param();
        return;
    }

    
    $new_status = sanitize_input($_POST['order_status'] ?? '');
    $tracking_number = sanitize_input($_POST['tracking_number'] ?? '');
    $tracking_url = sanitize_input($_POST['tracking_url'] ?? '');
    $admin_notes = sanitize_input($_POST['admin_notes'] ?? '');
    $notify_customer = isset($_POST['notify_customer']) && $_POST['notify_customer'] == '1';

    
    try {
        
        $pdo = get_db_connection();
        $notes_sql = "UPDATE orders SET admin_notes = :admin_notes WHERE id = :id";
        $notes_stmt = $pdo->prepare($notes_sql);
        $notes_stmt->execute([
            ':id' => $item_id,
            ':admin_notes' => $admin_notes
        ]);

        
        $result = update_order_status($item_id, $new_status, $notify_customer, $tracking_number, $tracking_url);

        if ($result) {
            $notification_msg = $notify_customer ? ' Cliente notificado por email.' : '';
            add_flash_message('Estado da encomenda atualizado com sucesso!' . $notification_msg, 'success');
        } else {
            add_flash_message('Nenhuma alteração foi feita no estado da encomenda.', 'info');
        }
    } catch (Exception $e) {
        add_flash_message('Erro ao atualizar o estado da encomenda.', 'danger');
    }

    
    $_SESSION['redirect_to'] = 'admin.php?section=orders&action=detail&id=' . $item_id . '&' . get_session_id_param();
    return; 
}

?>

<h1>Detalhes da Encomenda #<?= sanitize_input($order['order_ref']) ?></h1>
<a href="admin.php?section=orders&<?= get_session_id_param() ?>" class="btn btn-secondary mb-3">&laquo; Voltar à Lista</a>
<hr>

<!-- Toast container for notifications -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1100;"></div>

<div class="row">
    <!-- Order Details -->
    <div class="col-md-8">
        <div class="card mb-4 collapsible-card" id="customer-info-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <span>Informação do Cliente</span>
                <?php if (!$is_anonymized): ?>
                    <button type="button" class="btn btn-sm btn-primary" style="margin-right: 10px;" data-bs-toggle="collapse" data-bs-target="#editCustomerForm" aria-expanded="false" aria-controls="editCustomerForm">
                        <i class="bi bi-pencil"></i> Editar
                    </button>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if ($is_anonymized): ?>
                    <p class="text-danger"><strong>Dados do cliente foram anonimizados.</strong></p>
                <?php else: ?>
                    <div id="customerInfoDisplay">
                        <p><strong>Nome:</strong> <?= sanitize_input($customer_info['customer_name'] ?? 'N/D') ?></p>
                        <p><strong>Email:</strong> <?= sanitize_input($customer_info['customer_email'] ?? 'N/D') ?></p>
                        <p><strong>Telefone:</strong> <?= sanitize_input($customer_info['customer_phone'] ?? 'N/D') ?></p>
                        <p><strong>NIF:</strong> <?= sanitize_input($customer_info['customer_vat_id'] ?? 'N/D') ?></p>
                        <?php if (!empty($customer_info['order_notes'])): ?>
                        <p><strong>Notas do Cliente para este Pedido:</strong><br><i><?= nl2br(sanitize_input($customer_info['order_notes'])) ?></i></p>
                        <?php endif; ?>
                        <hr>
                        <h5>Morada de Envio</h5>
                        <p>
                            <?= sanitize_input($customer_info['shipping_address'] ?? 'N/D') ?><br>
                            <?= sanitize_input($customer_info['shipping_zip'] ?? '') ?> <?= sanitize_input($customer_info['shipping_city'] ?? '') ?><br>
                            <?= sanitize_input($customer_info['shipping_country'] ?? '') ?>
                        </p>
                        <?php if (!empty($customer_info['billing_name'])): ?>
                            <hr>
                            <h5>Morada de Faturação</h5>
                             <p>
                                <strong>Nome/Empresa:</strong> <?= sanitize_input($customer_info['billing_name'] ?? 'N/D') ?><br>
                                <strong>NIF:</strong> <?= sanitize_input($customer_info['billing_nif'] ?? 'N/D') ?><br>
                                <?= sanitize_input($customer_info['billing_address'] ?? 'N/D') ?><br>
                                <?= sanitize_input($customer_info['billing_zip'] ?? '') ?> <?= sanitize_input($customer_info['billing_city'] ?? '') ?><br>
                                <?= sanitize_input($customer_info['billing_country'] ?? '') ?>
                            </p>
                        <?php endif; ?>
                    </div>

                    <!-- Edit Customer Form -->
                    <div class="collapse mt-3" id="editCustomerForm">
                        <form method="POST" action="admin.php?section=orders&action=detail&id=<?= $item_id ?>&<?= get_session_id_param() ?>" class="ajax-form">
                            <?= csrf_input_field() ?>
                            <input type="hidden" name="section" value="orders">
                            <input type="hidden" name="id" value="<?= $item_id ?>">
                            <input type="hidden" name="update_customer_info" value="1">

                            <div class="mb-3">
                                <label for="customer_name" class="form-label">Nome</label>
                                <input type="text" class="form-control" id="customer_name" name="customer_name" value="<?= sanitize_input($customer_info['customer_name'] ?? '') ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="customer_email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="customer_email" name="customer_email" value="<?= sanitize_input($customer_info['customer_email'] ?? '') ?>" required>
                            </div>

                            <div class="mb-3">
                                <label for="customer_phone" class="form-label">Telefone</label>
                                <input type="text" class="form-control" id="customer_phone" name="customer_phone" value="<?= sanitize_input($customer_info['customer_phone'] ?? '') ?>">
                            </div>

                            <div class="mb-3">
                                <label for="customer_vat_id" class="form-label">NIF / Número de Identificação Fiscal</label>
                                <div class="input-group">
                                    <span class="input-group-text">PT</span>
                                    <input type="text" class="form-control" id="customer_vat_id" name="customer_vat_id"
                                           value="<?= preg_replace('/^PT/', '', sanitize_input($customer_info['customer_vat_id'] ?? '')) ?>"
                                           maxlength="9" placeholder="123456789">
                                </div>
                                <small class="form-text text-muted">Apenas os 9 dígitos numéricos, o prefixo PT é adicionado automaticamente.</small>
                            </div>

                            <div class="mb-3">
                                <label for="order_notes" class="form-label">Notas do Pedido</label>
                                <textarea class="form-control" id="order_notes" name="order_notes" rows="3"><?= sanitize_input($customer_info['order_notes'] ?? '') ?></textarea>
                            </div>

                            <h5>Morada de Envio</h5>

                            <div class="mb-3">
                                <label for="shipping_address" class="form-label">Endereço</label>
                                <input type="text" class="form-control" id="shipping_address" name="shipping_address" value="<?= sanitize_input($customer_info['shipping_address'] ?? '') ?>">
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="shipping_city" class="form-label">Cidade</label>
                                    <input type="text" class="form-control" id="shipping_city" name="shipping_city" value="<?= sanitize_input($customer_info['shipping_city'] ?? '') ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="shipping_zip" class="form-label">Código Postal</label>
                                    <input type="text" class="form-control" id="shipping_zip" name="shipping_zip" value="<?= sanitize_input($customer_info['shipping_zip'] ?? '') ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="shipping_country" class="form-label">País</label>
                                    <input type="text" class="form-control" id="shipping_country" name="shipping_country" value="<?= sanitize_input($customer_info['shipping_country'] ?? 'PT') ?>">
                                </div>
                            </div>

                            <?php if (!empty($customer_info['billing_name'])): ?>
                                <h5>Morada de Faturação</h5>

                                <div class="mb-3">
                                    <label for="billing_name" class="form-label">Nome/Empresa</label>
                                    <input type="text" class="form-control" id="billing_name" name="billing_name" value="<?= sanitize_input($customer_info['billing_name'] ?? '') ?>">
                                </div>

                                <div class="mb-3">
                                    <label for="billing_nif" class="form-label">NIF</label>
                                    <input type="text" class="form-control" id="billing_nif" name="billing_nif" value="<?= sanitize_input($customer_info['billing_nif'] ?? '') ?>">
                                </div>

                                <div class="mb-3">
                                    <label for="billing_address" class="form-label">Endereço</label>
                                    <input type="text" class="form-control" id="billing_address" name="billing_address" value="<?= sanitize_input($customer_info['billing_address'] ?? '') ?>">
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="billing_city" class="form-label">Cidade</label>
                                        <input type="text" class="form-control" id="billing_city" name="billing_city" value="<?= sanitize_input($customer_info['billing_city'] ?? '') ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="billing_zip" class="form-label">Código Postal</label>
                                        <input type="text" class="form-control" id="billing_zip" name="billing_zip" value="<?= sanitize_input($customer_info['billing_zip'] ?? '') ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="billing_country" class="form-label">País</label>
                                        <input type="text" class="form-control" id="billing_country" name="billing_country" value="<?= sanitize_input($customer_info['billing_country'] ?? 'PT') ?>">
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="d-flex justify-content-end">
                                <button type="button" class="btn btn-secondary me-2" data-bs-toggle="collapse" data-bs-target="#editCustomerForm">Cancelar</button>
                                <button type="submit" class="btn btn-primary">Guardar Alterações</button>
                            </div>
                        </form>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <?php if ($has_digital_products && !empty($licenses)): ?>
        <div class="card mb-4 collapsible-card" id="digital-products-card">
            <div class="card-header bg-primary text-white">Produtos Digitais</div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> Esta encomenda contém produtos digitais. Abaixo estão as licenças associadas.
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Código de Licença</th>
                                <th>Estado</th>
                                <th>Expiração</th>
                                <th>Downloads</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($licenses as $license):
                                
                                $download_count = get_license_download_count($license['id']);

                                
                                $status_label = '';
                                $status_class = '';
                                switch ($license['status']) {
                                    case 'active':
                                        $status_label = 'Ativa';
                                        $status_class = 'bg-success text-white';
                                        break;
                                    case 'waiting_payment':
                                        $status_label = 'Aguardando Pagamento';
                                        $status_class = 'bg-warning text-dark';
                                        break;
                                    case 'expired':
                                        $status_label = 'Expirada';
                                        $status_class = 'bg-secondary text-white';
                                        break;
                                    case 'revoked':
                                        $status_label = 'Revogada';
                                        $status_class = 'bg-danger text-white';
                                        break;
                                    default:
                                        $status_label = ucfirst($license['status']);
                                        $status_class = 'bg-light text-dark';
                                }
                            ?>
                            <tr>
                                <td><?= sanitize_input($license['license_code']) ?></td>
                                <td><span class="badge <?= $status_class ?>"><?= $status_label ?></span></td>
                                <td><?= date('d/m/Y', strtotime($license['expiry_date'])) ?></td>
                                <td><?= $download_count ?> / <?= $license['download_limit'] ?></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="admin.php?section=licenses&action=edit&id=<?= $license['id'] ?>" class="btn btn-outline-primary">
                                            <i class="bi bi-pencil"></i> Editar
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="card mb-4 collapsible-card" id="order-items-card">
            <div class="card-header">Itens da Encomenda</div>
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Produto</th>
                            <th>SKU</th>
                            <th>Qtd.</th>
                            <th class="text-end">Preço Unit.</th>
                            <th class="text-end">IVA</th>
                            <th class="text-end">Total Item</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($order_items): ?>
                            <?php foreach ($order_items as $item):
                                $details = json_decode($item['product_details_json'] ?? '{}', true);

                                
                                $is_digital = isset($details['product_type']) && $details['product_type'] === 'digital';
                            ?>
                                <tr>
                                    <td>
                                        <?= sanitize_input($details['name'] ?? 'N/D') ?>
                                        <?php if ($is_digital): ?>
                                            <span class="badge bg-primary">Digital</span>
                                        <?php endif; ?>
                                        <br>

                                        <?php if (!empty($details['attributes_display'])): ?>
                                            <small class="text-muted"><?= sanitize_input($details['attributes_display']) ?></small>
                                        <?php elseif (!empty($item['variation_id'])): ?>
                                            <?php
                                            
                                            require_once __DIR__ . '/../../includes/product_functions.php';
                                            
                                            $attributes_display = get_variation_attribute_string($item['variation_id']);
                                            if (!empty($attributes_display)): ?>
                                                <small class="text-muted"><?= sanitize_input($attributes_display) ?></small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <small class="text-muted"><?= sanitize_input($details['attributes'] ?? '') ?></small>
                                        <?php endif; ?>

                                        <?php if (!empty($details['custom_fields'])): ?>
                                            <?php
                                            
                                            require_once __DIR__ . '/../../includes/custom_field_functions.php';

                                            
                                            $custom_field_values = get_order_item_custom_fields($item['id']);
                                            $custom_field_values_by_id = [];

                                            
                                            foreach ($custom_field_values as $field_value) {
                                                $custom_field_values_by_id[$field_value['custom_field_id']] = $field_value;
                                            }

                                            foreach ($details['custom_fields'] as $custom_field):
                                                $field_id = $custom_field['field_id'] ?? 0;
                                                $field_type = $custom_field['field_type'] ?? '';
                                            ?>
                                                <br><small class="text-muted">
                                                    <?= sanitize_input(get_cart_custom_field_display($custom_field)) ?>

                                                    <?php if ($field_type === 'file-upload' && isset($custom_field_values_by_id[$field_id]) && !empty($custom_field_values_by_id[$field_id]['file_path'])): ?>
                                                        <a href="<?= BASE_URL . $custom_field_values_by_id[$field_id]['file_path'] ?>" target="_blank" class="btn btn-sm btn-outline-primary ms-2">
                                                            <i class="bi bi-download"></i> Download
                                                        </a>
                                                    <?php elseif ($field_type === 'file-upload' && isset($custom_field['file_path']) && !empty($custom_field['file_path'])): ?>
                                                        <a href="<?= BASE_URL . $custom_field['file_path'] ?>" target="_blank" class="btn btn-sm btn-outline-primary ms-2">
                                                            <i class="bi bi-download"></i> Download
                                                        </a>
                                                    <?php endif; ?>
                                                </small>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= sanitize_input($details['sku'] ?? 'N/D') ?></td>
                                    <td><?= $item['quantity'] ?></td>
                                    <td class="text-end"><?= format_price($item['price_at_purchase']) ?></td>
                                    <td class="text-end">
                                        <?php if (isset($details['vat_rate'])): ?>
                                            <small><?= number_format($details['vat_rate'], 1, ',', '.') ?>%</small>
                                            <?php if (isset($details['vat_description'])): ?>
                                                <br><small class="text-muted"><?= sanitize_input($details['vat_description']) ?></small>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <small>-</small>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-end"><?= format_price($item['price_at_purchase'] * $item['quantity']) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr><td colspan="6">Nenhum item encontrado para esta encomenda.</td></tr>
                        <?php endif; ?>
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="4" class="text-end">Subtotal:</td>
                            <td class="text-end"><?= format_price($order['total_amount'] - ($order['shipping_cost'] ?? 0) - ($order['tax_amount'] ?? 0) + ($order['discount_amount'] ?? 0)) ?></td>
                        </tr>
                        <?php if (!empty($order['discount_amount']) && $order['discount_amount'] > 0): ?>
                        <tr>
                            <td colspan="4" class="text-end">Desconto:
                                <?php if (!empty($order['coupon_code'])): ?>
                                <span class="badge bg-info"><?= sanitize_input($order['coupon_code']) ?></span>
                                <?php endif; ?>
                            </td>
                            <td class="text-end text-danger">-<?= format_price($order['discount_amount'] ?? 0) ?></td>
                        </tr>
                        <?php endif; ?>
                         <tr>
                            <td colspan="4" class="text-end">Envio:</td>
                            <td class="text-end"><?= format_price($order['shipping_cost'] ?? 0) ?></td>
                        </tr>
                         <?php
                        
                        $tax_details = json_decode($order['tax_details_json'] ?? '{}', true);

                        if (!empty($tax_details) && count($tax_details) > 1):
                        ?>
                            <tr>
                                <td colspan="4" class="text-end">IVA (Detalhado):</td>
                                <td class="text-end"><?= format_price($order['tax_amount'] ?? 0) ?></td>
                            </tr>
                            <?php foreach ($tax_details as $rate => $details): ?>
                            <tr>
                                <td colspan="4" class="text-end ps-5">
                                    <small><?= number_format($details['rate'], 1, ',', '.') ?>% - <?= sanitize_input($details['description']) ?></small>
                                </td>
                                <td class="text-end"><small><?= format_price($details['tax']) ?></small></td>
                            </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="4" class="text-end">IVA:</td>
                                <td class="text-end"><?= format_price($order['tax_amount'] ?? 0) ?></td>
                            </tr>
                        <?php endif; ?>
                         <tr class="fw-bold">
                            <td colspan="4" class="text-end">Total:</td>
                            <td class="text-end"><?= format_price($order['total_amount']) ?></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

    <!-- Order Status & Actions -->
    <div class="col-md-4">
        <div class="card mb-4 collapsible-card" id="order-access-card">
            <div class="card-header">Link Seguro</div>
            <div class="card-body">
                <?php
                
                $access_token = get_order_access_token($item_id);
                if (!$access_token) {
                    $access_token = create_order_access_token($item_id);
                }

                
                $secure_order_url = BASE_URL . '/index.php?view=order_success&token=' . $access_token;
                ?>

                <div class="alert alert-info">
                    <i class="bi bi-shield-lock"></i> Esta encomenda está protegida por um token de acesso seguro.
                </div>

                <div class="mb-3">
                    <label class="form-label">Link Seguro para o Cliente</label>
                    <div class="input-group">
                        <input type="text" class="form-control" value="<?= $secure_order_url ?>" id="secure-order-url" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('secure-order-url')">
                            <i class="bi bi-clipboard"></i>
                        </button>
                    </div>
                    <small class="text-muted">Este link permite ao cliente aceder aos detalhes da encomenda de forma segura.</small>
                </div>

                <div class="mb-3">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="regenerateToken(<?= $item_id ?>)">
                        <i class="bi bi-arrow-repeat"></i> Gerar Novo Token
                    </button>
                    <small class="text-muted ms-2">Isto invalidará todos os links anteriores.</small>
                </div>

                <script>
                function copyToClipboard(elementId) {
                    var copyText = document.getElementById(elementId);
                    copyText.select();
                    copyText.setSelectionRange(0, 99999);
                    document.execCommand("copy");

                    // Show feedback
                    alert("Link copiado para a área de transferência!");
                }

                // Handle NIF input formatting
                document.addEventListener('DOMContentLoaded', function() {
                    const vatIdField = document.getElementById('customer_vat_id');
                    if (vatIdField) {
                        // Only allow numbers in the NIF field
                        vatIdField.addEventListener('input', function(e) {
                            // Remove any non-numeric characters
                            this.value = this.value.replace(/\D/g, '');

                            // Limit to 9 digits
                            if (this.value.length > 9) {
                                this.value = this.value.slice(0, 9);
                            }
                        });
                    }
                });

                // Helper function to get session ID parameter
                function getSessionIdParam() {
                    // Try to extract from URL
                    const urlParams = new URLSearchParams(window.location.search);
                    const sidParam = urlParams.get('sid');
                    if (sidParam) {
                        return 'sid=' + sidParam;
                    }
                    return '';
                }

                function regenerateToken(orderId) {
                    if (confirm("Tem certeza que deseja gerar um novo token? Isto invalidará todos os links anteriores.")) {
                        // Make an AJAX request to regenerate the token
                        fetch('admin.php?section=orders&action=regenerate_token&id=' + orderId + '&' + getSessionIdParam(), {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: 'csrf_token=' + document.querySelector('input[name="csrf_token"]').value
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                document.getElementById('secure-order-url').value = data.url;
                                alert("Token regenerado com sucesso!");
                            } else {
                                alert("Erro ao regenerar o token: " + data.message);
                            }
                        })
                        .catch(error => {
                            alert("Erro ao regenerar o token: " + error);
                        });
                    }
                }
                </script>
            </div>
        </div>

        <div class="card mb-4 collapsible-card" id="order-status-card">
            <div class="card-header">Estado & Ações</div>
            <div class="card-body">
                <form method="POST" action="admin.php?section=orders&action=detail&id=<?= $item_id ?>&<?= get_session_id_param() ?>" class="ajax-form" data-section="orders" data-id="<?= $item_id ?>">
                     <?= csrf_input_field() ?>
                     <input type="hidden" name="section" value="orders">
                     <input type="hidden" name="id" value="<?= $item_id ?>">
                     <input type="hidden" name="update_status" value="1">
                     <!-- Debug info -->
                     <script>
                     </script>

                     <div class="mb-3">
                        <label for="order_status" class="form-label">Estado da Encomenda</label>
                        <select class="form-select" id="order_status" name="order_status">
                            <!-- Populate with possible statuses -->
                            <?php
                            $statuses = get_order_statuses();
                            foreach ($statuses as $status_code => $status_info):
                            ?>
                                <option value="<?= $status_code ?>" <?= ($order['status'] == $status_code) ? 'selected' : '' ?>>
                                    <?= $status_info['name_pt'] ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="notify_customer" name="notify_customer" value="1" checked>
                        <label class="form-check-label" for="notify_customer">Notificar cliente por email</label>
                    </div>
                     <div class="mb-3">
                        <label for="tracking_number" class="form-label">Nº de Tracking</label>
                        <input type="text" class="form-control" id="tracking_number" name="tracking_number" value="<?= sanitize_input($order['tracking_number'] ?? '') ?>">
                    </div>
                     <div class="mb-3">
                        <label for="tracking_url" class="form-label">URL de Tracking</label>
                        <input type="url" class="form-control" id="tracking_url" name="tracking_url" value="<?= sanitize_input($order['tracking_url'] ?? '') ?>" placeholder="https://exemplo.com/tracking">
                        <div class="form-text">Insira o URL geral do serviço de tracking da transportadora.</div>
                    </div>
                     <div class="mb-3">
                        <label for="admin_notes" class="form-label">Notas Internas</label>
                        <textarea class="form-control" id="admin_notes" name="admin_notes" rows="3"><?= sanitize_input($order['admin_notes'] ?? '') ?></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">Atualizar Estado</button>
                </form>
                <hr>
                 <p><strong>Data:</strong> <?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></p>
                 <p><strong>Método Pag.:</strong>
                    <?php
                    
                    require_once __DIR__ . '/../../includes/payment_methods.php';
                    ensure_payment_methods_table_exists();

                    
                    $payment_method_display = 'N/D';
                    $payment_method_id = $order['payment_method'];

                    if (is_numeric($payment_method_id)) {
                        $payment_method = get_payment_method($payment_method_id);
                        if ($payment_method) {
                            $payment_method_display = sanitize_input($payment_method['title']);
                        } else {
                            $payment_method_display = "Método #" . sanitize_input($payment_method_id);
                        }
                    } else {
                        $payment_method_display = sanitize_input($payment_method_id ?? 'N/D');
                    }

                    echo $payment_method_display;
                    ?>
                    <small class="text-muted">(ID: <?= sanitize_input($payment_method_id) ?>)</small>

                    <?php if (!$is_anonymized): ?>
                        <button type="button" class="btn btn-sm btn-outline-primary ms-2" data-bs-toggle="collapse" data-bs-target="#changePaymentMethodForm" aria-expanded="false" aria-controls="changePaymentMethodForm">
                            <i class="bi bi-pencil-square"></i>
                        </button>
                    <?php endif; ?>
                 </p>

                 <?php if (!$is_anonymized): ?>
                    <!-- Change Payment Method Form -->
                    <div class="collapse mb-3" id="changePaymentMethodForm">
                        <form method="POST" action="admin.php?section=orders&action=detail&id=<?= $item_id ?>&<?= get_session_id_param() ?>" class="ajax-form" data-section="orders" data-id="<?= $item_id ?>">
                            <?= csrf_input_field() ?>
                            <input type="hidden" name="section" value="orders">
                            <input type="hidden" name="id" value="<?= $item_id ?>">
                            <input type="hidden" name="update_payment_method" value="1">

                            <div class="mb-3">
                                <label for="payment_method" class="form-label">Método de Pagamento</label>
                                <select class="form-select" id="payment_method" name="payment_method" required>
                                    <?php
                                    
                                    require_once __DIR__ . '/../../includes/payment_methods.php';
                                    ensure_payment_methods_table_exists();

                                    
                                    $payment_methods = get_payment_methods();
                                    
                                    
                                    foreach ($payment_methods as $method):
                                    ?>
                                        <?php
                                            $is_selected = ($order['payment_method'] == $method['id'] || $order['payment_method'] === (string)$method['id']);
                                        ?>
                                        <option value="<?= $method['id'] ?>" <?= $is_selected ? 'selected' : '' ?>>
                                            <?= sanitize_input($method['title']) ?>
                                        </option>
                                    <?php endforeach; ?>

                                    <?php
                                    
                                    $current_method_in_list = false;
                                    foreach ($payment_methods as $method) {
                                        if ($order['payment_method'] == $method['id'] || $order['payment_method'] === (string)$method['id']) {
                                            $current_method_in_list = true;
                                            break;
                                        }
                                    }

                                    if (!$current_method_in_list && !empty($order['payment_method'])):
                                        
                                        $payment_method_name = $order['payment_method'];
                                        if (is_numeric($payment_method_name)) {
                                            $payment_method_name = "Método #" . $payment_method_name;
                                        }
                                    ?>
                                        <option value="<?= $order['payment_method'] ?>" selected>
                                            <?= sanitize_input($payment_method_name) ?> (atual)
                                        </option>
                                    <?php endif; ?>
                                </select>
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="button" class="btn btn-secondary me-2" data-bs-toggle="collapse" data-bs-target="#changePaymentMethodForm">Cancelar</button>
                                <button type="submit" class="btn btn-primary">Guardar</button>
                            </div>
                        </form>
                    </div>
                <?php endif; ?>

                 <p><strong>Termos Aceites:</strong> <?= !empty($terms_log) ? 'Sim' : 'Não' ?>
                    <?php if (!empty($terms_log)): ?>
                        <small class="text-muted">(<?= date('d/m/Y H:i', strtotime($terms_log['timestamp'] ?? '')) ?>, IP: <?= sanitize_input($terms_log['ip'] ?? '') ?>)</small>
                    <?php endif; ?>
                 </p>

                 <?php if (!$is_anonymized): ?>
                    <hr>
                    <div class="d-flex flex-wrap gap-2">
                        <form method="POST" action="admin.php?section=orders&action=detail&id=<?= $item_id ?>&<?= get_session_id_param() ?>" class="ajax-form" data-section="orders" data-id="<?= $item_id ?>">
                            <?= csrf_input_field() ?>
                            <input type="hidden" name="section" value="orders">
                            <input type="hidden" name="id" value="<?= $item_id ?>">
                            <input type="hidden" name="resend_order_details" value="1">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="bi bi-envelope"></i> Re-enviar Detalhes da Encomenda
                            </button>
                        </form>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Contact Client -->
        <?php if (!$is_anonymized): ?>
        <div class="card mb-4 collapsible-card" id="contact-client-card">
            <div class="card-header">Contactar Cliente</div>
            <div class="card-body">
                <form method="POST" action="admin.php?section=orders&action=detail&id=<?= $item_id ?>&<?= get_session_id_param() ?>" id="contact-client-form" class="ajax-form" data-section="orders" data-id="<?= $item_id ?>">
                    <?= csrf_input_field() ?>
                    <input type="hidden" name="section" value="orders">
                    <input type="hidden" name="id" value="<?= $item_id ?>">
                    <input type="hidden" name="contact_client" value="1">
                    <input type="hidden" name="client_email" value="<?= sanitize_input($customer_info['customer_email'] ?? '') ?>">
                    <input type="hidden" name="client_name" value="<?= sanitize_input($customer_info['customer_name'] ?? '') ?>">
                    <input type="hidden" name="order_ref" value="<?= sanitize_input($order['order_ref']) ?>">

                    <div class="mb-3">
                        <label for="message_subject" class="form-label">Assunto</label>
                        <input type="text" class="form-control" id="message_subject" name="message_subject" required>
                    </div>
                    <div class="mb-3">
                        <label for="message_body" class="form-label">Mensagem</label>
                        <textarea class="form-control" id="message_body" name="message_body" rows="4" required></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">Enviar Mensagem</button>
                </form>
            </div>
        </div>
        <?php endif; ?>

        <!-- Message History -->
        <div class="card mb-4 collapsible-card" id="message-history-card">
            <div class="card-header">Histórico de Mensagens</div>
            <div class="card-body">
                <?php
                
                $order_messages = db_query(
                    "SELECT c.*, mr.id as reply_id, mr.reply_body, mr.sent_at as reply_sent_at
                     FROM contacts c
                     LEFT JOIN message_replies mr ON c.id = mr.message_id
                     WHERE c.order_id = :order_id
                     ORDER BY c.created_at DESC, mr.sent_at ASC",
                    [':order_id' => $item_id],
                    false, true
                );

                
                $grouped_messages = [];
                if ($order_messages) {
                    foreach ($order_messages as $msg) {
                        $msg_id = $msg['id'];
                        if (!isset($grouped_messages[$msg_id])) {
                            $grouped_messages[$msg_id] = [
                                'message' => $msg,
                                'replies' => []
                            ];
                        }

                        if (!empty($msg['reply_id'])) {
                            $grouped_messages[$msg_id]['replies'][] = [
                                'id' => $msg['reply_id'],
                                'reply_body' => $msg['reply_body'],
                                'sent_at' => $msg['reply_sent_at']
                            ];
                        }
                    }
                }
                ?>

                <?php if (empty($grouped_messages)): ?>
                    <p class="text-muted">Nenhuma mensagem encontrada para esta encomenda.</p>
                <?php else: ?>
                    <div class="accordion" id="messageHistoryAccordion">
                        <?php foreach ($grouped_messages as $msg_id => $thread): ?>
                            <?php
                                $message = $thread['message'];
                                $replies = $thread['replies'];
                                $accordion_id = "message_" . $msg_id;
                                $header_id = "header_" . $msg_id;
                                $collapse_id = "collapse_" . $msg_id;

                                
                                $is_from_admin = isset($message['is_from_admin']) && $message['is_from_admin'] == 1;
                            ?>
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="<?= $header_id ?>">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#<?= $collapse_id ?>" aria-expanded="false" aria-controls="<?= $collapse_id ?>">
                                        <span class="me-2"><?= $is_from_admin ? '<span class="badge bg-primary">Admin</span>' : '<span class="badge bg-secondary">Cliente</span>' ?></span>
                                        <span class="fw-bold me-2"><?= sanitize_input($message['subject']) ?></span>
                                        <span class="text-muted small me-auto"><?= date('d/m/Y H:i', strtotime($message['created_at'])) ?></span>
                                        <span class="badge bg-secondary rounded-pill"><?= count($replies) ?> Resposta(s)</span>
                                    </button>
                                </h2>
                                <div id="<?= $collapse_id ?>" class="accordion-collapse collapse" aria-labelledby="<?= $header_id ?>" data-bs-parent="#messageHistoryAccordion">
                                    <div class="accordion-body">
                                        <!-- Original Message -->
                                        <div class="card mb-3">
                                            <div class="card-header small text-muted">
                                                <?php if ($is_from_admin): ?>
                                                    Mensagem enviada para <strong><?= sanitize_input($message['name']) ?></strong> (<?= sanitize_input($message['email']) ?>)
                                                <?php else: ?>
                                                    Mensagem de <strong><?= sanitize_input($message['name']) ?></strong> (<?= sanitize_input($message['email']) ?>)
                                                    <?php if ($message['phone']): ?> | Tel: <?= sanitize_input($message['phone']) ?><?php endif; ?>
                                                <?php endif; ?>
                                                - <?= date('d/m/Y H:i', strtotime($message['created_at'])) ?>
                                            </div>
                                            <div class="card-body">
                                                <p style="white-space: pre-wrap;"><?= sanitize_input($message['message']) ?></p>
                                            </div>
                                        </div>

                                        <!-- Replies -->
                                        <?php if (!empty($replies)): ?>
                                            <div class="replies-container mt-3 ms-md-4">
                                                <h5>Respostas:</h5>
                                                <?php foreach ($replies as $reply): ?>
                                                    <div class="reply-item border-top pt-2 pb-2 mt-2">
                                                        <p class="mb-1" style="white-space: pre-wrap;"><?= sanitize_input($reply['reply_body']) ?></p>
                                                        <small class="text-muted">
                                                            Resposta enviada em <?= date('d/m/Y H:i', strtotime($reply['sent_at'])) ?>
                                                        </small>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        <?php else: ?>
                                            <p class="ms-md-4 mt-3"><em>Nenhuma resposta enviada ainda.</em></p>
                                        <?php endif; ?>

                                        <!-- Reply Form -->
                                        <?php if (!$is_anonymized): ?>
                                        <hr>
                                        <form method="POST" action="admin.php?section=orders&action=detail&id=<?= $item_id ?>&<?= get_session_id_param() ?>" class="reply-form" data-section="orders" data-id="<?= $item_id ?>">
                                            <?= csrf_input_field() ?>
                                            <input type="hidden" name="section" value="orders">
                                            <input type="hidden" name="id" value="<?= $item_id ?>">
                                            <input type="hidden" name="reply_to_message" value="1">
                                            <input type="hidden" name="message_id" value="<?= $msg_id ?>">
                                            <div class="mb-3">
                                                <label for="reply_message_<?= $msg_id ?>" class="form-label">Responder</label>
                                                <textarea class="form-control" id="reply_message_<?= $msg_id ?>" name="reply_message" rows="3" required></textarea>
                                            </div>
                                            <button type="submit" class="btn btn-sm btn-primary">Enviar Resposta</button>
                                        </form>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- GDPR Anonymization -->
        <?php if (!$is_anonymized): ?>
        <div class="card mb-4 border-danger collapsible-card" id="gdpr-action-card">
            <div class="card-header text-bg-danger">Ação GDPR</div>
            <div class="card-body">
                 <p>Anonimizar os dados pessoais desta encomenda (irreversível).</p>
                 <form method="POST" action="admin.php?section=orders&action=detail&id=<?= $item_id ?>&<?= get_session_id_param() ?>" class="ajax-form" data-section="orders" data-id="<?= $item_id ?>" id="anonymize-form">
                     <?= csrf_input_field() ?>
                     <input type="hidden" name="section" value="orders">
                     <input type="hidden" name="id" value="<?= $item_id ?>">
                     <input type="hidden" name="anonymize_order" value="1">

                     <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="notify_anonymization" name="notify_anonymization" value="1" checked>
                        <label class="form-check-label" for="notify_anonymization">Notificar cliente por email</label>
                     </div>

                     <button type="submit" class="btn btn-danger" id="anonymize-btn">Anonimizar Encomenda</button>
                 </form>

                 <script>
                 document.addEventListener('DOMContentLoaded', function() {
                     const anonymizeForm = document.getElementById('anonymize-form');
                     const anonymizeBtn = document.getElementById('anonymize-btn');

                     if (anonymizeForm) {
                         anonymizeForm.addEventListener('submit', function(e) {
                             e.preventDefault();

                             if (confirm('Tem a certeza que pretende anonimizar permanentemente os dados desta encomenda? Esta ação não pode ser desfeita.')) {
                                 // Disable the button to prevent double submission
                                 if (anonymizeBtn) {
                                     anonymizeBtn.disabled = true;
                                     anonymizeBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processando...';
                                 }

                                 // Get form data
                                 const formData = new FormData(this);

                                 // Send AJAX request
                                 fetch('includes/admin_ajax_handler.php?' + getSessionIdParam(), {
                                     method: 'POST',
                                     body: formData,
                                     headers: {
                                         'X-Session-UUID': '<?= session_id() ?>'
                                     }
                                 })
                                 .then(response => response.json())
                                 .then(data => {
                                     if (data.success) {
                                         showToast('success', data.message);
                                         // Reload the page after a short delay
                                         setTimeout(() => {
                                             window.location.reload();
                                         }, 1500);
                                     } else {
                                         showToast('danger', data.message);
                                         // Re-enable the button
                                         if (anonymizeBtn) {
                                             anonymizeBtn.disabled = false;
                                             anonymizeBtn.innerHTML = 'Anonimizar Encomenda';
                                         }
                                     }
                                 })
                                 .catch(error => {
                                     console.error('Error:', error);
                                     showToast('danger', 'Ocorreu um erro ao processar o pedido.');
                                     // Re-enable the button
                                     if (anonymizeBtn) {
                                         anonymizeBtn.disabled = false;
                                         anonymizeBtn.innerHTML = 'Anonimizar Encomenda';
                                     }
                                 });
                             }
                         });
                     }
                 });
                 </script>
            </div>
        </div>
        <?php endif; ?>

        <!-- Delete Order -->
        <div class="card mb-4 border-danger collapsible-card" id="delete-order-card">
            <div class="card-header text-bg-danger">Eliminar Encomenda</div>
            <div class="card-body">
                <p>Eliminar permanentemente esta encomenda e todos os seus itens.</p>
                <p class="text-danger"><strong>Atenção:</strong> Esta ação não pode ser desfeita.</p>
                <button type="button" class="btn btn-danger" id="delete-order-btn"
                        data-order-id="<?= $item_id ?>"
                        data-order-ref="<?= sanitize_input($order['order_ref']) ?>"
                        data-csrf="<?= generate_csrf_token() ?>">
                    <i class="bi bi-trash"></i> Eliminar Encomenda
                </button>
            </div>
        </div>

    </div>
</div>

<!-- Delete Order Confirmation Modal -->
<div class="modal fade" id="deleteOrderModal" tabindex="-1" aria-labelledby="deleteOrderModalLabel" role="dialog">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteOrderModalLabel">Confirmar Eliminação</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
            </div>
            <div class="modal-body">
                <p>Tem a certeza que deseja eliminar a encomenda <strong id="orderRefToDelete"></strong>?</p>
                <p class="text-danger"><strong>Atenção:</strong> Esta ação não pode ser desfeita.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" autofocus>Cancelar</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteOrder">Eliminar</button>
            </div>
        </div>
    </div>
</div>

<script>
// Immediately-invoked function expression to avoid conflicts with other scripts
(function() {
    // Helper function to get session ID parameter (defined outside init)
    function getSessionIdParam() {
        const urlParams = new URLSearchParams(window.location.search);
        const sessionParam = urlParams.get('PHPSESSID') || urlParams.get('sid');
        return sessionParam ? (urlParams.get('PHPSESSID') ? 'PHPSESSID=' : 'sid=') + sessionParam : '';
    }

    // Helper function to show toast messages (defined outside init)
    function showToast(type, message) {
        const toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            console.error('Toast container not found');
            alert(message); // Fallback to alert if toast container not found
            return;
        }

        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Fechar"></button>
            </div>
        `;

        toastContainer.appendChild(toast);

        try {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();

            // Remove toast after it's hidden
            toast.addEventListener('hidden.bs.toast', function() {
                toast.remove();
            });
        } catch (error) {
            console.error('Error showing toast:', error);
            toast.remove();
            alert(message); // Fallback to alert if toast fails
        }
    }

    // Define the confirm delete handler function (outside init)
    function confirmDeleteHandler(deleteModal) {
        const orderId = this.getAttribute('data-order-id');
        const csrfToken = this.getAttribute('data-csrf');

        if (!orderId || !csrfToken) {
            console.error('Missing order ID or CSRF token');
            alert('Erro: Não foi possível identificar a encomenda a eliminar. Por favor, tente novamente.');
            return;
        }

        // Create form data
        const formData = new FormData();
        formData.append('section', 'orders');
        formData.append('delete_order', '1');
        formData.append('id', orderId);
        formData.append('csrf_token', csrfToken);

        // Send AJAX request with session ID in the URL
        fetch('includes/admin_ajax_handler.php?' + getSessionIdParam(), {
            method: 'POST',
            body: formData,
            headers: {
                'X-Session-UUID': '<?= session_id() ?>' // Add session ID as a custom header
            }
        })
        .then(response => response.json())
        .then(data => {
            // Hide the modal
            if (deleteModal) deleteModal.hide();

            if (data.success) {
                // Show success message
                showToast('success', data.message);

                // Redirect to orders list after a short delay
                setTimeout(() => {
                    window.location.href = 'admin.php?section=orders&' + getSessionIdParam();
                }, 1500);
            } else {
                // Show error message
                showToast('danger', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            if (deleteModal) deleteModal.hide();
            showToast('danger', 'Ocorreu um erro ao processar o pedido.');
        });
    }

    // Function to initialize delete button functionality
    function initDeleteButton() {
        const deleteButton = document.getElementById('delete-order-btn');
        const deleteModalElement = document.getElementById('deleteOrderModal');
        const orderRefElement = document.getElementById('orderRefToDelete');
        const confirmDeleteButton = document.getElementById('confirmDeleteOrder');

        // Only proceed if we have the necessary elements
        if (!deleteButton || !deleteModalElement || !orderRefElement || !confirmDeleteButton) {
            console.error('Missing required elements for delete functionality');
            return;
        }

        // Initialize the modal
        let deleteModal;
        try {
            deleteModal = new bootstrap.Modal(deleteModalElement, {
                backdrop: 'static',  // Prevent closing when clicking outside
                keyboard: false      // Prevent closing with keyboard
            });
        } catch (error) {
            console.error('Error initializing Bootstrap modal:', error);
            return;
        }

        // Add click event listener to the main delete button
        deleteButton.addEventListener('click', function(e) {
            e.preventDefault();
            const orderId = this.getAttribute('data-order-id');
            const orderRef = this.getAttribute('data-order-ref');
            const csrfToken = this.getAttribute('data-csrf');

            if (orderRefElement) {
                orderRefElement.textContent = '#' + orderRef;
            }

            // Set up the confirm button with the order data
            confirmDeleteButton.setAttribute('data-order-id', orderId);
            confirmDeleteButton.setAttribute('data-csrf', csrfToken);

            // Remove previous listener before adding a new one to prevent duplicates
            confirmDeleteButton.removeEventListener('click', boundConfirmHandler);
            // Bind the modal instance to the handler
            boundConfirmHandler = confirmDeleteHandler.bind(confirmDeleteButton, deleteModal);
            confirmDeleteButton.addEventListener('click', boundConfirmHandler);

            // Fix for modal focus issue
            deleteModalElement.setAttribute('aria-hidden', 'false');

            // Show the modal
            deleteModal.show();
        });

        // Define a variable to hold the bound handler function
        let boundConfirmHandler;

        // Fix for modal focus issue - add event listener to modal
        deleteModalElement.addEventListener('shown.bs.modal', function() {
            // Ensure aria-hidden is set correctly
            this.setAttribute('aria-hidden', 'false');

            // Set focus to the cancel button instead of the confirm button
            const cancelButton = this.querySelector('.btn-secondary');
            if (cancelButton) {
                cancelButton.focus();
            }
        });

        deleteModalElement.addEventListener('hidden.bs.modal', function() {
            // Reset focus to the delete button when modal is closed
            if (deleteButton) {
                deleteButton.focus();
            }
            // Clean up the confirm button listener when modal is hidden
            if (confirmDeleteButton && boundConfirmHandler) {
                 confirmDeleteButton.removeEventListener('click', boundConfirmHandler);
            }
        });
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initDeleteButton);
    } else {
        // DOM already loaded, run immediately
        initDeleteButton();
    }

    // Also initialize when the page is fully loaded (including all resources)
    // window.addEventListener('load', initDeleteButton); // Removed duplicate initialization
})();

// Initialize confirmation dialogs for forms with needs-confirmation class
document.addEventListener('DOMContentLoaded', function() {
    const confirmForms = document.querySelectorAll('form.needs-confirmation');

    confirmForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const message = this.getAttribute('data-confirm-message') || 'Tem certeza que deseja realizar esta ação?';

            if (confirm(message)) {
                this.submit();
            }
        });
    });
});

// Function to show toast notifications
function showToast(type, message) {
    const toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) return;

    // Create toast element
    const toastEl = document.createElement('div');
    toastEl.className = `toast align-items-center text-bg-${type} border-0`;
    toastEl.setAttribute('role', 'alert');
    toastEl.setAttribute('aria-live', 'assertive');
    toastEl.setAttribute('aria-atomic', 'true');

    // Create toast content
    toastEl.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    // Add toast to container
    toastContainer.appendChild(toastEl);

    // Initialize Bootstrap toast
    const toast = new bootstrap.Toast(toastEl, {
        autohide: true,
        delay: 5000
    });

    // Show toast
    toast.show();

    // Remove toast from DOM after it's hidden
    toastEl.addEventListener('hidden.bs.toast', function() {
        toastEl.remove();
    });
}
</script>
