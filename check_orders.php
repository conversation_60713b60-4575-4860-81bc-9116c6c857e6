<?php

require_once __DIR__ . '/includes/db.php';

echo "Checking orders in the database...\n";

$pdo = get_db_connection();

if ($pdo) {
    echo "Database connection successful.\n";
    
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM orders;");
    $count = $stmt->fetchColumn();
    
    echo "Number of orders in the database: " . $count . "\n";
    
    if ($count > 0) {
        
        $stmt = $pdo->query("SELECT * FROM orders ORDER BY id DESC LIMIT 1;");
        $order = $stmt->fetch();
        
        echo "Most recent order:\n";
        echo "ID: " . $order['id'] . "\n";
        echo "Order Reference: " . $order['order_ref'] . "\n";
        echo "Status: " . $order['status'] . "\n";
        echo "Created At: " . $order['created_at'] . "\n";
        
        
        if (!empty($order['tax_details_json'])) {
            echo "Tax Details JSON: " . $order['tax_details_json'] . "\n";
        } else {
            echo "Tax Details JSON is empty.\n";
        }
    }
} else {
    echo "ERROR: Failed to connect to the database.\n";
}

echo "Check completed.\n";
?>
