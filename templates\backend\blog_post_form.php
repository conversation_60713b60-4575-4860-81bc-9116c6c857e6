<?php

$is_edit_mode = ($action === 'edit' && !empty($post_data));
$form_action_url = 'admin.php?' . get_session_id_param();

$title = $form_data['title'] ?? ($post_data['title'] ?? '');
$slug = $form_data['slug'] ?? ($post_data['slug'] ?? '');
$post_type = $form_data['post_type'] ?? ($post_data['post_type'] ?? 'article');
$content = $form_data['content'] ?? ($post_data['content'] ?? '');
$link_url = $form_data['link_url'] ?? ($post_data['link_url'] ?? '');
$link_description = $form_data['link_description'] ?? ($post_data['link_description'] ?? '');
$code_content = $form_data['code_content'] ?? ($post_data['code_content'] ?? ''); // Added for CODE type
$selected_category_ids = $form_data['category_ids'] ?? array_column($post_data['categories'] ?? [], 'id');
$current_image_path = $post_data['image_path'] ?? null;
$image_description = $form_data['image_description'] ?? ($post_data['image_description'] ?? '');
$is_published = isset($form_data['is_published']) ? (int)$form_data['is_published'] : (isset($post_data['is_published']) ? (int)$post_data['is_published'] : 0);
$published_at = $form_data['published_at'] ?? ($post_data['published_at'] ?? '');

$auto_fill_seo = !$is_edit_mode && empty($form_data);
$plain_content = '';

if ($auto_fill_seo) {
    
    if (!empty($content)) {
        $plain_content = strip_tags($content);
    } elseif (!empty($link_description)) {
        $plain_content = $link_description;
    }

    
    $short_description = mb_substr($plain_content, 0, 160);
    if (mb_strlen($plain_content) > 160) {
        $short_description .= '...';
    }
}

$seo_title = $form_data['seo_title'] ?? ($post_data['seo_title'] ?? ($auto_fill_seo ? $title : ''));
$seo_description = $form_data['seo_description'] ?? ($post_data['seo_description'] ?? ($auto_fill_seo ? $short_description : ''));
$seo_keywords = $form_data['seo_keywords'] ?? ($post_data['seo_keywords'] ?? '');
$og_title = $form_data['og_title'] ?? ($post_data['og_title'] ?? ($auto_fill_seo ? $title : ''));
$og_description = $form_data['og_description'] ?? ($post_data['og_description'] ?? ($auto_fill_seo ? $short_description : ''));
$og_image = $form_data['og_image'] ?? ($post_data['og_image'] ?? $current_image_path ?? ''); 
$twitter_card = $form_data['twitter_card'] ?? ($post_data['twitter_card'] ?? 'summary_large_image');
$twitter_title = $form_data['twitter_title'] ?? ($post_data['twitter_title'] ?? ($auto_fill_seo ? $title : ''));
$twitter_description = $form_data['twitter_description'] ?? ($post_data['twitter_description'] ?? ($auto_fill_seo ? $short_description : ''));
$twitter_image = $form_data['twitter_image'] ?? ($post_data['twitter_image'] ?? $current_image_path ?? ''); 

?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
             <form action="<?php echo $form_action_url; ?>" method="post" enctype="multipart/form-data" id="blogPostForm">
                <input type="hidden" name="section" value="blog_posts">
                <input type="hidden" name="action" value="<?php echo $is_edit_mode ? 'edit' : 'new'; ?>">
                <?php if ($is_edit_mode): ?>
                    <input type="hidden" name="id" value="<?php echo htmlspecialchars($item_id); ?>">
                <?php endif; ?>
                <?php echo csrf_input_field(); ?>

                <div class="card card-primary card-tabs">
                    <div class="card-header p-0 pt-1">
                        <ul class="nav nav-tabs" id="custom-tabs-one-tab" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="custom-tabs-content-tab" data-bs-toggle="pill" href="#custom-tabs-content" role="tab" aria-controls="custom-tabs-content" aria-selected="true">Conteúdo Principal</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="custom-tabs-seo-tab" data-bs-toggle="pill" href="#custom-tabs-seo" role="tab" aria-controls="custom-tabs-seo" aria-selected="false">SEO & Social</a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <?php display_flash_messages(); ?>
                        <div class="tab-content" id="custom-tabs-one-tabContent">
                            <!-- Content Tab -->
                            <div class="tab-pane fade show active" id="custom-tabs-content" role="tabpanel" aria-labelledby="custom-tabs-content-tab">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="form-group mb-3">
                                            <label for="title">Título do Post <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($title); ?>" required>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="slug">Slug (URL)</label>
                                            <input type="text" class="form-control" id="slug" name="slug" value="<?php echo htmlspecialchars($slug); ?>" aria-describedby="slugHelpPost">
                                            <small id="slugHelpPost" class="form-text text-muted">Deixe em branco para gerar automaticamente a partir do título.</small>
                                        </div>

                                        <div class="form-group mb-3">
                                            <label for="post_type">Tipo de Post <span class="text-danger">*</span></label>
                                            <select class="form-select" id="post_type" name="post_type">
                                                <option value="article" <?php echo ($post_type === 'article') ? 'selected' : ''; ?>>Artigo</option>
                                                <option value="link" <?php echo ($post_type === 'link') ? 'selected' : ''; ?>>Link Externo</option>
                                                <option value="CODE" <?php echo ($post_type === 'CODE') ? 'selected' : ''; ?>>Código Executável</option>
                                            </select>
                                        </div>

                                        <div id="article-content-fields" style="<?php echo ($post_type === 'link' || $post_type === 'CODE') ? 'display: none;' : ''; ?>">
                                            <div class="form-group mb-3">
                                                <label for="content">Conteúdo do Artigo</label>
                                                <textarea class="form-control summernote-editor" id="content" name="content" rows="15"><?php echo htmlspecialchars($content); ?></textarea>
                                                <small class="form-text text-muted">Use o editor para formatar o conteúdo.</small>
                                            </div>
                                        </div>

                                        <div id="link-url-field" style="<?php echo ($post_type !== 'link') ? 'display: none;' : ''; ?>">
                                            <div class="form-group mb-3">
                                                <label for="link_url">URL do Link <span class="text-danger">*</span></label>
                                                <input type="url" class="form-control" id="link_url" name="link_url" value="<?php echo htmlspecialchars($link_url); ?>">
                                                <small class="form-text text-muted">O endereço URL completo para onde o post irá apontar.</small>
                                            </div>
                                        </div>

                                        <div id="short-description-field" style="<?php echo ($post_type === 'article') ? 'display: none;' : ''; ?>">
                                            <div class="form-group mb-3">
                                                <label for="link_description">Descrição Curta</label>
                                                <textarea class="form-control" id="link_description" name="link_description" rows="3"><?php echo htmlspecialchars($link_description); ?></textarea>
                                                <small class="form-text text-muted">Uma breve descrição que aparecerá com o post (para tipos Link e Código).</small>
                                            </div>
                                        </div>

                                        <div id="code-content-fields" style="<?php echo ($post_type !== 'CODE') ? 'display: none;' : ''; ?>">
                                            <div class="form-group mb-3">
                                                <label for="code_content">Código (PHP/HTML/JS) <span class="text-danger">*</span></label>
                                                <textarea class="form-control" id="code_content" name="code_content" rows="15" placeholder="Insira o seu código PHP, HTML e/ou JavaScript aqui. Certifique-se de que é seguro!"><?php echo htmlspecialchars($code_content); ?></textarea>
                                                <small class="form-text text-warning"><strong>Atenção:</strong> O código inserido será executado diretamente no servidor. Use com extrema cautela e apenas insira código de fontes confiáveis.</small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="card card-secondary mb-3">
                                            <div class="card-header"><h3 class="card-title">Publicação</h3></div>
                                            <div class="card-body">
                                                 <div class="form-group form-check mb-3">
                                                    <!-- Fix: Ensure checkbox state is preserved correctly -->
                                                    <input type="checkbox" class="form-check-input" id="is_published" name="is_published" value="1" <?php echo $is_published ? 'checked' : ''; ?>>
                                                    <label class="form-check-label" for="is_published">Publicado</label>
                                                    <!-- Add a hidden field to ensure the checkbox value is always submitted -->
                                                    <input type="hidden" name="is_published_submitted" value="1">
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label for="published_at">Data de Publicação</label>
                                                    <input type="datetime-local" class="form-control" id="published_at" name="published_at" value="<?php echo !empty($published_at) ? date('Y-m-d\TH:i', strtotime($published_at)) : ''; ?>">
                                                    <small class="form-text text-muted">Opcional. Deixe em branco para publicar imediatamente ao marcar "Publicado".</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="card card-secondary mb-3">
                                             <div class="card-header"><h3 class="card-title">Categorias <span class="text-danger">*</span></h3></div>
                                             <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                                                 <?php if (empty($categories)): ?>
                                                     <p>Nenhuma categoria de blog encontrada. <a href="admin.php?section=blog_categories&action=new&<?php echo get_session_id_param(); ?>">Crie uma primeiro.</a></p>
                                                 <?php else: ?>
                                                     <?php foreach ($categories as $category): ?>
                                                         <div class="form-check">
                                                             <input class="form-check-input" type="checkbox" name="category_ids[]" value="<?php echo $category['id']; ?>" id="category_<?php echo $category['id']; ?>"
                                                                 <?php echo in_array($category['id'], $selected_category_ids) ? 'checked' : ''; ?>>
                                                             <label class="form-check-label" for="category_<?php echo $category['id']; ?>">
                                                                 <?php echo htmlspecialchars($category['name']); ?>
                                                             </label>
                                                         </div>
                                                     <?php endforeach; ?>
                                                <?php endif; ?>
                                             </div>
                                         </div>

                                        <div class="card card-secondary mb-3">
                                            <div class="card-header"><h3 class="card-title">Imagem Destacada <span class="text-danger">*</span></h3></div>
                                            <div class="card-body">
                                                <div class="form-group mb-3">
                                                    <label for="image_path">Carregar Nova Imagem</label>
                                                    <input type="file" class="form-control" id="image_path" name="image_path" accept="image/jpeg, image/png, image/gif, image/webp">
                                                    <small class="form-text text-muted">Substituirá a imagem atual, se existir.</small>
                                                </div>
                                                <div class="form-group mb-3">
                                                    <label for="image_description">Descrição da Imagem / Créditos</label>
                                                    <input type="text" class="form-control" id="image_description" name="image_description" value="<?php echo htmlspecialchars($image_description); ?>">
                                                    <small class="form-text text-muted">Opcional. Será exibido no canto inferior direito da imagem em itálico.</small>
                                                </div>
                                                <?php if ($is_edit_mode && $current_image_path && file_exists(__DIR__ . '/../../' . $current_image_path)): ?>
                                                    <div class="mt-3">
                                                        <p><strong>Imagem Atual:</strong></p>
                                                        <img src="<?php echo htmlspecialchars(BASE_URL . "/" . $current_image_path); ?>" alt="Imagem Atual" class="img-thumbnail mb-2" style="max-width: 200px;">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="delete_current_image" value="1" id="delete_current_image">
                                                            <label class="form-check-label" for="delete_current_image">
                                                                Remover Imagem Atual
                                                            </label>
                                                        </div>
                                                    </div>
                                                <?php elseif ($is_edit_mode && $current_image_path): ?>
                                                    <p class="text-warning mt-3">Imagem atual (<?php echo htmlspecialchars($current_image_path); ?>) não encontrada no servidor.</p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div> <!-- /#custom-tabs-content -->

                            <!-- SEO & Social Tab -->
                            <div class="tab-pane fade" id="custom-tabs-seo" role="tabpanel" aria-labelledby="custom-tabs-seo-tab">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h4 class="mb-0">Otimização para Motores de Busca (SEO)</h4>
                                    <button type="button" id="prefill-seo-button" class="btn btn-outline-primary">
                                        <i class="bi bi-magic"></i> Preencher Automaticamente
                                    </button>
                                </div>
                                <div class="alert alert-info mb-3">
                                    <i class="bi bi-info-circle"></i> Clique no botão "Preencher Automaticamente" para gerar automaticamente os campos SEO e Social com base no conteúdo principal.
                                </div>
                                <div class="form-group mb-3">
                                    <label for="seo_title">Título SEO</label>
                                    <input type="text" class="form-control" id="seo_title" name="seo_title" value="<?php echo htmlspecialchars($seo_title); ?>">
                                    <small class="form-text text-muted">Opcional. Título que aparecerá nos resultados de busca. Se vazio, usa o título do post.</small>
                                </div>
                                <div class="form-group mb-3">
                                    <label for="seo_description">Descrição SEO</label>
                                    <textarea class="form-control" id="seo_description" name="seo_description" rows="2"><?php echo htmlspecialchars($seo_description); ?></textarea>
                                    <small class="form-text text-muted">Opcional. Breve descrição para resultados de busca.</small>
                                </div>
                                <div class="form-group mb-3">
                                    <label for="seo_keywords">Palavras-chave SEO</label>
                                    <input type="text" class="form-control" id="seo_keywords" name="seo_keywords" value="<?php echo htmlspecialchars($seo_keywords); ?>">
                                    <small class="form-text text-muted">Opcional. Separadas por vírgula.</small>
                                </div>

                                <hr>

                                <h4>Redes Sociais (Open Graph & Twitter Cards)</h4>
                                <p class="text-muted"><small>Opcional. Se deixado em branco, tentará usar os dados de SEO ou do post principal.</small></p>

                                <div class="row">
                                    <div class="col-md-6">
                                        <h5>Facebook / Open Graph</h5>
                                        <div class="form-group mb-3">
                                            <label for="og_title">Título Open Graph</label>
                                            <input type="text" class="form-control" id="og_title" name="og_title" value="<?php echo htmlspecialchars($og_title); ?>">
                                        </div>
                                         <div class="form-group mb-3">
                                            <label for="og_description">Descrição Open Graph</label>
                                            <textarea class="form-control" id="og_description" name="og_description" rows="2"><?php echo htmlspecialchars($og_description); ?></textarea>
                                        </div>
                                        <div class="form-group mb-3">
                                            <label for="og_image">URL da Imagem Open Graph</label>
                                            <!-- Changed from type="url" to type="text" to prevent browser URL validation -->
                                            <input type="text" class="form-control" id="og_image" name="og_image" value="<?php echo htmlspecialchars($og_image); ?>">
                                             <small class="form-text text-muted">URL completo ou caminho relativo. Se vazio, usa a imagem destacada.</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                         <h5>Twitter Card</h5>
                                        <div class="form-group mb-3">
                                            <label for="twitter_card">Tipo de Card</label>
                                            <select class="form-select" id="twitter_card" name="twitter_card">
                                                <option value="summary" <?php echo ($twitter_card === 'summary') ? 'selected' : ''; ?>>Summary Card</option>
                                                <option value="summary_large_image" <?php echo ($twitter_card === 'summary_large_image') ? 'selected' : ''; ?>>Summary Card with Large Image</option>
                                                <!-- Add other card types if needed -->
                                            </select>
                                        </div>
                                         <div class="form-group mb-3">
                                            <label for="twitter_title">Título Twitter</label>
                                            <input type="text" class="form-control" id="twitter_title" name="twitter_title" value="<?php echo htmlspecialchars($twitter_title); ?>">
                                        </div>
                                         <div class="form-group mb-3">
                                            <label for="twitter_description">Descrição Twitter</label>
                                            <textarea class="form-control" id="twitter_description" name="twitter_description" rows="2"><?php echo htmlspecialchars($twitter_description); ?></textarea>
                                        </div>
                                         <div class="form-group mb-3">
                                            <label for="twitter_image">URL da Imagem Twitter</label>
                                            <!-- Changed from type="url" to type="text" to prevent browser URL validation -->
                                            <input type="text" class="form-control" id="twitter_image" name="twitter_image" value="<?php echo htmlspecialchars($twitter_image); ?>">
                                            <small class="form-text text-muted">URL completo ou caminho relativo. Se vazio, usa a imagem destacada.</small>
                                        </div>
                                    </div>
                                </div>
                            </div> <!-- /#custom-tabs-seo -->
                        </div>
                    </div>
                    <!-- /.card-body -->
                    <div class="card-footer">
                         <button type="submit" name="save_action" value="save_and_return" class="btn btn-primary">Guardar e Voltar</button>
                         <button type="submit" name="save_action" value="save_and_continue" class="btn btn-info">Guardar e Continuar a Editar</button>
                         <a href="admin.php?section=blog_posts&<?php echo get_session_id_param(); ?>" class="btn btn-secondary">Cancelar</a>
                    </div>
                </div>
                <!-- /.card -->
            </form>
        </div>
    </div>
</div>

<!-- Include Summernote JS (if not already in footer) -->
<!-- <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.20/dist/summernote-bs5.min.js"></script> -->
<script>
// Global flag to track if jQuery initialization has happened
let jQueryInitialized = false;

// Wait for jQuery to be fully loaded before executing code
document.addEventListener('DOMContentLoaded', function() {
    // Set up a fallback for the prefill button in case jQuery fails to load
    // Only use the fallback if jQuery doesn't load within a reasonable time
    const fallbackTimeout = setTimeout(() => {
        if (!jQueryInitialized) {
            setupPrefillButtonFallback();
        }
    }, 2000); // Wait 2 seconds before using fallback

    // Check if jQuery is loaded
    if (typeof jQuery === 'undefined') {
        console.error('jQuery is not loaded. Waiting for it to load...');

        // Try again in 500ms
        setTimeout(function checkJQuery() {
            if (typeof jQuery !== 'undefined') {
                clearTimeout(fallbackTimeout); // Cancel fallback setup
                jQueryInitialized = true;
                initializeBlogPostForm();
            } else {
                setTimeout(checkJQuery, 500);
            }
        }, 500);
    } else {
        clearTimeout(fallbackTimeout); // Cancel fallback setup
        jQueryInitialized = true;
        initializeBlogPostForm();
    }
});

// Fallback function to handle the prefill button using vanilla JavaScript
function setupPrefillButtonFallback() {
    const prefillButton = document.getElementById('prefill-seo-button');
    if (prefillButton) {
        prefillButton.addEventListener('click', function(e) {
            // If jQuery is initialized, don't use the fallback
            if (jQueryInitialized) {
                return;
            }

            e.preventDefault();

            // Get main content values
            const title = document.getElementById('title').value.trim();
            let content = '';

            // Get content based on post type
            const postType = document.getElementById('post_type').value;
            if (postType === 'article') {
                // For article type, try to get content directly from textarea
                const contentTextarea = document.getElementById('content');
                content = contentTextarea ? contentTextarea.value : '';
            } else {
                // For link type, use link description
                const linkDesc = document.getElementById('link_description');
                content = linkDesc ? linkDesc.value.trim() : '';
            }

            // Extract plain text from HTML content for descriptions
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            const plainText = tempDiv.textContent || tempDiv.innerText || '';

            // Create a short description (first 160 characters)
            let shortDescription = plainText.substring(0, 160).trim();
            if (plainText.length > 160) {
                shortDescription += '...';
            }

            // Extract keywords from content (simplified version)
            const words = plainText.toLowerCase()
                .replace(/[^\w\s]/g, ' ')
                .split(/\s+/)
                .filter(word => word.length > 3);

            const uniqueWords = [...new Set(words)];
            const keywords = uniqueWords.slice(0, 8).join(', ');

            // Fill SEO fields
            const fields = {
                'seo_title': title,
                'seo_description': shortDescription,
                'seo_keywords': keywords,
                'og_title': title,
                'og_description': shortDescription,
                'twitter_title': title,
                'twitter_description': shortDescription
            };

            // Update all fields
            Object.keys(fields).forEach(id => {
                const element = document.getElementById(id);
                if (element) element.value = fields[id];
            });

            // Show success message
            alert('Campos SEO e Social preenchidos automaticamente com sucesso!');
        });
    } else {
        console.error('Prefill button not found for fallback setup!');
    }
}

// Main initialization function
function initializeBlogPostForm() {
    // Use jQuery now that we know it's available
    const $ = jQuery;
    const postTypeSelect = $('#post_type'); // Use jQuery selector
    const articleFields = $('#article-content-fields');
    const linkUrlField = $('#link-url-field'); // Changed from linkFields
    const shortDescriptionField = $('#short-description-field'); // Added
    const codeFields = $('#code-content-fields'); // Added for CODE type
    const contentTextarea = $('#content');

    // Function to initialize Summernote using the centralized configuration
    function initializeEditor() {
        // Check if the textarea exists
        if (contentTextarea.length) {
            // Use the centralized Summernote initialization with custom options for this editor
            if (typeof window.initSummernoteEditors === 'function') {
                window.initSummernoteEditors('#content', {
                    placeholder: 'Escreva o conteúdo do artigo aqui...',
                    height: 350 // Increased height for blog posts
                });
            } else {
                console.warn('Centralized Summernote initialization not available');
            }
        } else {
            console.warn('Content textarea not found');
        }
    }

    // Function to destroy Summernote
    function destroyEditor() {
        // Check if the textarea exists
        if (contentTextarea.length) {
            try {
                contentTextarea.summernote('destroy');
            } catch (e) {
                console.warn('Error destroying Summernote:', e);
            }
        }
    }

    // Function to toggle fields based on post type
    function toggleFields() {
        const selectedType = postTypeSelect.val();

        if (selectedType === 'article') {
            articleFields.show();
            linkUrlField.hide();
            shortDescriptionField.hide();
            codeFields.hide();
            setTimeout(function() {
                initializeEditor();
            }, 100);
        } else if (selectedType === 'link') {
            articleFields.hide();
            linkUrlField.show();
            shortDescriptionField.show();
            codeFields.hide();
            destroyEditor();
        } else if (selectedType === 'CODE') {
            articleFields.hide();
            linkUrlField.hide();
            shortDescriptionField.show();
            codeFields.show();
            destroyEditor();
        } else { // Default or unknown
            articleFields.hide();
            linkUrlField.hide();
            shortDescriptionField.hide();
            codeFields.hide();
            destroyEditor();
        }
    }

    // Initial toggle on page load
    toggleFields();

    // Toggle when post type changes
    postTypeSelect.on('change', toggleFields);

    // --- Keep the rest of the original JS (slug generation, image preview) ---

    // Slug generation/validation
    const titleInput = document.getElementById('title'); // Keep using getElementById for these if preferred
    const slugInput = document.getElementById('slug');
    if (titleInput && slugInput) {
        titleInput.addEventListener('blur', function() {
            if (slugInput.value.trim() === '') {
                let slug = this.value.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\-]+/g, '').replace(/\-\-+/g, '-').replace(/^-+/, '').replace(/-+$/, '');
                slugInput.value = slug;
            }
        });
        slugInput.addEventListener('blur', function() {
            let slug = this.value.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\-]+/g, '').replace(/\-\-+/g, '-').replace(/^-+/, '').replace(/-+$/, '');
            if (this.value !== slug) { this.value = slug; }
        });
    }

    // Image preview/delete logic
    const imageInput = document.getElementById('image_path');
    const deleteCheckbox = document.getElementById('delete_current_image');
    const currentImage = document.querySelector('.img-thumbnail');
    if (imageInput && deleteCheckbox && currentImage) {
        imageInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                deleteCheckbox.checked = false;
            }
        });
        deleteCheckbox.addEventListener('change', function() {
            if (this.checked) {
                imageInput.value = '';
            }
        });
    }

    // Fix: Add form submission handler to ensure the form submits correctly
    const blogPostForm = document.getElementById('blogPostForm');
    if (blogPostForm) {
        blogPostForm.addEventListener('submit', function(e) {
            // Log form submission

            // Ensure the is_published checkbox state is correctly captured
            const isPublishedCheckbox = document.getElementById('is_published');
            if (isPublishedCheckbox) {
            }

            // Fix for Open Graph and Twitter image URLs
            // If they contain relative paths, ensure they're properly formatted
            const ogImageInput = document.getElementById('og_image');
            const twitterImageInput = document.getElementById('twitter_image');

            // Function to check if a string is a valid URL
            function isValidUrl(string) {
                try {
                    new URL(string);
                    return true;
                } catch (_) {
                    return false;
                }
            }

            // Function to handle image paths
            function normalizeImagePath(input) {
                if (!input || !input.value || input.value.trim() === '') {
                    return; // Empty value is fine
                }

                // If it's already a valid URL, leave it as is
                if (isValidUrl(input.value)) {
                    return;
                }

                // If it's a relative path starting with public/, it's a local file
                if (input.value.startsWith('public/')) {
                    // We'll keep it as is - the server will handle it
                }
            }

            // Normalize both image paths
            normalizeImagePath(ogImageInput);
            normalizeImagePath(twitterImageInput);

            // Continue with form submission
            return true;
        });
    }

    // SEO & Social Auto-fill functionality
    const prefillSeoButton = document.getElementById('prefill-seo-button');
    if (prefillSeoButton) {
        prefillSeoButton.addEventListener('click', function(e) {
            e.preventDefault();

            // Get main content values
            const title = document.getElementById('title').value.trim();
            let content = '';

            // Get content based on post type
            if (postTypeSelect.val() === 'article') {
                // For article type, get content from Summernote editor
                try {
                    content = contentTextarea.summernote('code');
                } catch (e) {
                    console.warn('Error getting Summernote content:', e);
                    content = contentTextarea.val() || '';
                }
            } else {
                // For link type, use link description
                content = document.getElementById('link_description').value.trim();
            }

            // Extract plain text from HTML content for descriptions
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            const plainText = tempDiv.textContent || tempDiv.innerText || '';

            // Create a short description (first 160 characters)
            let shortDescription = plainText.substring(0, 160).trim();
            if (plainText.length > 160) {
                shortDescription += '...';
            }

            // Extract keywords from content
            let keywords = extractKeywords(plainText, title);

            // Fill SEO fields
            document.getElementById('seo_title').value = title;
            document.getElementById('seo_description').value = shortDescription;
            document.getElementById('seo_keywords').value = keywords;

            // Fill Open Graph fields
            document.getElementById('og_title').value = title;
            document.getElementById('og_description').value = shortDescription;

            // Fill Twitter Card fields
            document.getElementById('twitter_title').value = title;
            document.getElementById('twitter_description').value = shortDescription;

            // Show success message
            alert('Campos SEO e Social preenchidos automaticamente com sucesso!');
        });
    } else {
        console.error('SEO auto-fill button not found!');
    }

    // Function to extract potential keywords from content
    function extractKeywords(text, title) {
        if (!text) return '';

        // Remove common words and punctuation
        const commonWords = ['a', 'o', 'e', 'de', 'da', 'do', 'em', 'para', 'com', 'um', 'uma', 'os', 'as', 'que', 'por', 'na', 'no'];

        // Convert to lowercase and remove special characters
        let words = text.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 3 && !commonWords.includes(word));

        // Count word frequency
        const wordCount = {};
        words.forEach(word => {
            wordCount[word] = (wordCount[word] || 0) + 1;
        });

        // Sort by frequency
        const sortedWords = Object.keys(wordCount).sort((a, b) => wordCount[b] - wordCount[a]);

        // Get top 5-8 words
        const keywordCount = Math.min(8, sortedWords.length);
        let keywords = sortedWords.slice(0, keywordCount).join(', ');

        // Add title words if they're not already included
        const titleWords = title.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 3 && !commonWords.includes(word));

        titleWords.forEach(word => {
            if (!keywords.includes(word)) {
                keywords = word + ', ' + keywords;
            }
        });

        return keywords;
    }
}

// End of initializeBlogPostForm function
</script>