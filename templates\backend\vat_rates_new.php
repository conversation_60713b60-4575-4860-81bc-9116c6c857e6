<?php

if (!defined('ADMIN_CONTEXT') || !ADMIN_CONTEXT) {
    die('Access Denied');
}
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Gestão de Taxas de IVA</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="admin.php?<?= get_session_id_param() ?>">Dashboard</a></li>
        <li class="breadcrumb-item active">Taxas de IVA</li>
    </ol>

    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <div><i class="fas fa-percentage me-1"></i> Gestão de Taxas de IVA</div>
            <button id="btn-add-vat-rate" class="btn btn-primary btn-sm">
                <i class="fas fa-plus"></i> Adicionar Nova Taxa de IVA
            </button>
        </div>
        <div class="card-body">
            <p class="mb-3">Configure as diferentes taxas de IVA disponíveis para seleção nos produtos.</p>

            <?php if (empty($vat_rates)): ?>
                <div class="alert alert-info">
                    Não existem taxas de IVA definidas. Clique em "Adicionar Nova Taxa de IVA" para adicionar.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>Taxa (%)</th>
                                <th>Descrição</th>
                                <th>Padrão</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody id="vat-rates-table">
                            <?php foreach ($vat_rates as $rate): ?>
                                <tr>
                                    <td><?= number_format($rate['rate'], 1, ',', '.') ?>%</td>
                                    <td><?= htmlspecialchars($rate['description']) ?></td>
                                    <td>
                                        <?php if ($rate['is_default']): ?>
                                            <span class="badge bg-success">Sim</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Não</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-primary btn-edit-vat-rate"
                                                    data-id="<?= $rate['id'] ?>"
                                                    data-rate="<?= $rate['rate'] ?>"
                                                    data-description="<?= htmlspecialchars($rate['description']) ?>">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <?php if (!$rate['is_default']): ?>
                                                <button type="button" class="btn btn-sm btn-success btn-set-default-vat-rate"
                                                        data-id="<?= $rate['id'] ?>">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger btn-delete-vat-rate"
                                                        data-id="<?= $rate['id'] ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add/Edit VAT Rate Modal -->
<div class="modal fade" id="modal-vat-rate" tabindex="-1" aria-labelledby="modal-vat-rate-label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-vat-rate-label">Adicionar Taxa de IVA</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="form-vat-rate">
                    <input type="hidden" id="vat-rate-id" name="id" value="">
                    <input type="hidden" name="csrf_token" value="<?= get_csrf_token() ?>">

                    <div class="mb-3">
                        <label for="vat-rate-value" class="form-label">Taxa (%)</label>
                        <input type="number" class="form-control" id="vat-rate-value" name="rate" min="0" max="100" step="0.1" required>
                    </div>

                    <div class="mb-3">
                        <label for="vat-rate-description" class="form-label">Descrição</label>
                        <input type="text" class="form-control" id="vat-rate-description" name="description" required>
                        <div class="form-text">Ex: "IVA Normal (23%)", "IVA Reduzido (6%)", "Isento de IVA (0%)"</div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="vat-rate-default" name="is_default" value="1">
                        <label class="form-check-label" for="vat-rate-default">Definir como taxa padrão</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="btn-save-vat-rate">Guardar</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="modal-delete-vat-rate" tabindex="-1" aria-labelledby="modal-delete-vat-rate-label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-delete-vat-rate-label">Confirmar Eliminação</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Tem a certeza que pretende eliminar esta taxa de IVA? Esta ação não pode ser desfeita.</p>
                <input type="hidden" id="delete-vat-rate-id" value="">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-danger" id="btn-confirm-delete-vat-rate">Eliminar</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize modals
    const vatRateModal = new bootstrap.Modal(document.getElementById('modal-vat-rate'));
    const deleteVatRateModal = new bootstrap.Modal(document.getElementById('modal-delete-vat-rate'));

    // Get form elements
    const vatRateForm = document.getElementById('form-vat-rate');
    const vatRateId = document.getElementById('vat-rate-id');
    const vatRateValue = document.getElementById('vat-rate-value');
    const vatRateDescription = document.getElementById('vat-rate-description');
    const vatRateDefault = document.getElementById('vat-rate-default');

    // Get buttons
    const btnAddVatRate = document.getElementById('btn-add-vat-rate');
    const btnSaveVatRate = document.getElementById('btn-save-vat-rate');
    const btnConfirmDeleteVatRate = document.getElementById('btn-confirm-delete-vat-rate');

    // Add VAT Rate button click handler
    btnAddVatRate.addEventListener('click', function() {
        // Reset form
        vatRateForm.reset();
        vatRateId.value = '';

        // Set modal title
        document.getElementById('modal-vat-rate-label').textContent = 'Adicionar Taxa de IVA';

        // Show modal
        vatRateModal.show();
    });

    // Edit VAT Rate buttons click handler (using event delegation)
    document.getElementById('vat-rates-table').addEventListener('click', function(e) {
        const editButton = e.target.closest('.btn-edit-vat-rate');
        if (!editButton) return;

        // Get data from button
        const id = editButton.dataset.id;
        const rate = editButton.dataset.rate;
        const description = editButton.dataset.description;

        // Set form values
        vatRateId.value = id;
        vatRateValue.value = rate;
        vatRateDescription.value = description;
        vatRateDefault.checked = false; // Default to unchecked for editing

        // Set modal title
        document.getElementById('modal-vat-rate-label').textContent = 'Editar Taxa de IVA';

        // Show modal
        vatRateModal.show();
    });

    // Set Default VAT Rate buttons click handler (using event delegation)
    document.getElementById('vat-rates-table').addEventListener('click', function(e) {
        const defaultButton = e.target.closest('.btn-set-default-vat-rate');
        if (!defaultButton) return;

        // Get VAT rate ID
        const id = defaultButton.dataset.id;

        // Show loading notification
        if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
            AdminUtils.showNotification('A definir como taxa padrão...', 'info');
        }

        // Send AJAX request
        const formData = new FormData();
        formData.append('action', 'set_default_vat_rate');
        formData.append('id', id);
        formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);

        fetch('admin_ajax.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                    AdminUtils.showNotification(data.message || 'Taxa de IVA definida como padrão com sucesso.', 'success');
                }

                // Reload the page to show updated data
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                    AdminUtils.showNotification(data.message || 'Ocorreu um erro ao definir a taxa como padrão.', 'danger');
                } else {
                    alert(data.message || 'Ocorreu um erro ao definir a taxa como padrão.');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                AdminUtils.showNotification('Ocorreu um erro ao processar o pedido.', 'danger');
            } else {
                alert('Ocorreu um erro ao processar o pedido.');
            }
        });
    });

    // Delete VAT Rate buttons click handler (using event delegation)
    document.getElementById('vat-rates-table').addEventListener('click', function(e) {
        const deleteButton = e.target.closest('.btn-delete-vat-rate');
        if (!deleteButton) return;

        // Get VAT rate ID
        const id = deleteButton.dataset.id;

        // Set hidden input value
        document.getElementById('delete-vat-rate-id').value = id;

        // Show modal
        deleteVatRateModal.show();
    });

    // Save VAT Rate button click handler
    btnSaveVatRate.addEventListener('click', function() {
        // Validate form
        if (!vatRateForm.checkValidity()) {
            vatRateForm.reportValidity();
            return;
        }

        // Get form values
        const id = vatRateId.value;
        const rate = vatRateValue.value;
        const description = vatRateDescription.value;
        const isDefault = vatRateDefault.checked ? 1 : 0;

        // Show loading notification
        if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
            AdminUtils.showNotification('A guardar taxa de IVA...', 'info');
        }

        // Send AJAX request
        const formData = new FormData();
        formData.append('action', id ? 'update_vat_rate' : 'create_vat_rate');
        if (id) formData.append('id', id);
        formData.append('rate', rate);
        formData.append('description', description);
        formData.append('is_default', isDefault);
        formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);

        fetch('admin_ajax.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Hide modal
                vatRateModal.hide();

                if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                    AdminUtils.showNotification(data.message || 'Taxa de IVA guardada com sucesso.', 'success');
                }

                // Reload the page to show updated data
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                    AdminUtils.showNotification(data.message || 'Ocorreu um erro ao guardar a taxa de IVA.', 'danger');
                } else {
                    alert(data.message || 'Ocorreu um erro ao guardar a taxa de IVA.');
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                AdminUtils.showNotification('Ocorreu um erro ao processar o pedido.', 'danger');
            } else {
                alert('Ocorreu um erro ao processar o pedido.');
            }
        });
    });

    // Confirm Delete VAT Rate button click handler
    btnConfirmDeleteVatRate.addEventListener('click', function() {
        // Get VAT rate ID
        const id = document.getElementById('delete-vat-rate-id').value;

        if (!id) {
            console.error('No VAT rate ID found for deletion');
            return;
        }

        // Show loading notification
        if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
            AdminUtils.showNotification('A eliminar taxa de IVA...', 'info');
        }

        // Send AJAX request
        const formData = new FormData();
        formData.append('action', 'delete_vat_rate');
        formData.append('id', id);
        formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);

        fetch('admin_ajax.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // Hide modal
            deleteVatRateModal.hide();

            if (data.success) {
                if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                    AdminUtils.showNotification(data.message || 'Taxa de IVA eliminada com sucesso.', 'success');
                }

                // Reload the page to show updated data
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                    AdminUtils.showNotification(data.message || 'Ocorreu um erro ao eliminar a taxa de IVA.', 'danger');
                } else {
                    alert(data.message || 'Ocorreu um erro ao eliminar a taxa de IVA.');
                }
            }
        })
        .catch(error => {
            // Hide modal
            deleteVatRateModal.hide();

            console.error('Error:', error);
            if (typeof AdminUtils !== 'undefined' && AdminUtils.showNotification) {
                AdminUtils.showNotification('Ocorreu um erro ao processar o pedido.', 'danger');
            } else {
                alert('Ocorreu um erro ao processar o pedido.');
            }
        });
    });
});
</script>
