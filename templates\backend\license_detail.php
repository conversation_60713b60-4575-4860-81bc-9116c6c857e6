<?php

require_once __DIR__ . '/../../includes/digital_product_functions.php';
require_once __DIR__ . '/../../includes/license_encryption_functions.php';

$license_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

if (!$license_id) {
    add_flash_message('ID de licença inválido.', 'danger');
    set_admin_redirect('admin.php?section=licenses');
    return;
}

$license = get_license_by_id($license_id);

if (!$license) {
    add_flash_message('Licença não encontrada.', 'danger');
    set_admin_redirect('admin.php?section=licenses');
    return;
}

$digital_products = get_license_digital_products($license_id);

$downloads = db_query(
    "SELECT * FROM downloads WHERE license_id = :license_id ORDER BY download_date DESC",
    [':license_id' => $license_id],
    false, true
);
$downloads = is_array($downloads) ? $downloads : [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_download_link'])) {
    if (send_license_download_email($license)) {
        add_flash_message('Link de download enviado com sucesso.', 'success');
    } else {
        add_flash_message('Erro ao enviar link de download.', 'danger');
    }

    
    header('Location: admin.php?section=licenses&action=detail&id=' . $license_id . '&' . get_session_id_param());
    exit;
}

if (isset($_GET['resend_email']) && $_GET['resend_email'] == '1') {
    if (resend_license_email($license_id)) {
        add_flash_message('Email de licença reenviado com sucesso.', 'success');
    } else {
        add_flash_message('Erro ao reenviar o email de licença.', 'danger');
    }

    
    header('Location: admin.php?section=licenses&action=detail&id=' . $license_id . '&' . get_session_id_param());
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $new_status = sanitize_input($_POST['status'] ?? '');

    if (in_array($new_status, ['active', 'waiting_payment', 'disabled', 'canceled'])) {
        $result = db_query(
            "UPDATE licenses SET
                status = :status,
                updated_at = datetime('now', 'localtime')
            WHERE id = :id",
            [
                ':id' => $license_id,
                ':status' => $new_status
            ]
        );

        if ($result !== false) {
            add_flash_message('Status da licença atualizado com sucesso.', 'success');

            
            if ($new_status === 'active' && $license['status'] !== 'active') {
                
                $expiry_days = get_setting('digital_download_expiry_days', 5);
                $expiry_date = date('Y-m-d H:i:s', strtotime("+{$expiry_days} days"));

                
                db_query(
                    "UPDATE licenses SET
                        expiry_date = :expiry_date,
                        updated_at = datetime('now', 'localtime')
                    WHERE id = :id",
                    [
                        ':id' => $license_id,
                        ':expiry_date' => $expiry_date
                    ]
                );

                
                $license = get_license_by_id($license_id);

                
                if (isset($_POST['send_email']) && $_POST['send_email'] === '1') {
                    if (send_license_download_email($license)) {
                        add_flash_message('Link de download enviado com sucesso.', 'success');
                    } else {
                        add_flash_message('Erro ao enviar link de download.', 'danger');
                    }
                }
            }
        } else {
            add_flash_message('Erro ao atualizar status da licença.', 'danger');
        }

        
        header('Location: admin.php?section=licenses&action=detail&id=' . $license_id . '&' . get_session_id_param());
        exit;
    }
}

display_flash_messages();

function get_license_status_badge_class($status) {
    switch ($status) {
        case 'active':
            return 'bg-success';
        case 'waiting_payment':
            return 'bg-warning text-dark';
        case 'disabled':
            return 'bg-secondary';
        case 'canceled':
            return 'bg-danger';
        default:
            return 'bg-secondary';
    }
}

function get_license_status_name($status) {
    switch ($status) {
        case 'active':
            return 'Ativo';
        case 'waiting_payment':
            return 'Aguardando Pagamento';
        case 'disabled':
            return 'Desativado';
        case 'canceled':
            return 'Cancelado';
        default:
            return 'Desconhecido';
    }
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Detalhes da Licença</h1>
        <div>
            <a href="admin.php?section=licenses&<?= get_session_id_param() ?>" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Voltar
            </a>
            <a href="admin.php?section=licenses&action=edit&id=<?= $license_id ?>&<?= get_session_id_param() ?>" class="btn btn-primary">
                <i class="bi bi-pencil"></i> Editar
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informações da Licença</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="fw-bold">Código de Licença</h6>
                        <p><code class="fs-5"><?= sanitize_input($license['license_code']) ?></code></p>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Status</h6>
                            <p>
                                <span class="badge <?= get_license_status_badge_class($license['status']) ?>">
                                    <?= get_license_status_name($license['status']) ?>
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Data de Criação</h6>
                            <p><?= date('d/m/Y H:i', strtotime($license['created_at'])) ?></p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Data de Expiração</h6>
                            <p><?= $license['expiry_date'] ? date('d/m/Y H:i', strtotime($license['expiry_date'])) : 'N/A' ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Downloads</h6>
                            <p><?= (int)$license['downloads_used'] ?> / <?= (int)$license['download_limit'] ?></p>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6 class="fw-bold">Cliente</h6>
                            <p><?= sanitize_input(get_decrypted_license_name($license)) ?></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">Email</h6>
                            <p><?= sanitize_input(get_decrypted_license_email($license)) ?></p>
                        </div>
                    </div>

                    <?php if ($license['order_id']): ?>
                        <div class="mb-3">
                            <h6 class="fw-bold">Pedido</h6>
                            <p>
                                <a href="admin.php?section=orders&action=detail&id=<?= (int)$license['order_id'] ?>&<?= get_session_id_param() ?>"
                                   class="btn btn-sm btn-outline-info">
                                    Ver Pedido #<?= (int)$license['order_id'] ?>
                                </a>
                            </p>
                        </div>
                    <?php endif; ?>

                    <div class="mt-4">
                        <h6 class="fw-bold">Ações</h6>
                        <div class="d-flex gap-2 flex-wrap">
                            <?php
                            
                            $is_active = $license['status'] === 'active';
                            $is_expired = !empty($license['expiry_date']) && strtotime($license['expiry_date']) < time();
                            $has_downloads_available = $license['downloads_used'] < $license['download_limit'];
                            $can_send_download = $is_active && !$is_expired && $has_downloads_available;

                            if ($can_send_download):
                            ?>
                            <form method="POST" action="admin.php?section=licenses&action=detail&id=<?= $license_id ?>&<?= get_session_id_param() ?>">
                                <?= csrf_input_field() ?>
                                <button type="submit" name="send_download_link" value="1" class="btn btn-success">
                                    <i class="bi bi-envelope"></i> Enviar Link de Download
                                </button>
                            </form>
                            <?php else: ?>
                            <button type="button" class="btn btn-success" disabled title="<?= !$is_active ? 'Licença não está ativa' : ($is_expired ? 'Licença expirada' : 'Limite de downloads atingido') ?>">
                                <i class="bi bi-envelope"></i> Enviar Link de Download
                            </button>
                            <?php endif; ?>

                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#updateStatusModal">
                                <i class="bi bi-arrow-repeat"></i> Atualizar Status
                            </button>

                            <button type="button" class="btn btn-warning" id="editExpiryBtn" data-bs-toggle="modal" data-bs-target="#editExpiryModal">
                                <i class="bi bi-calendar"></i> Editar Data de Expiração
                            </button>

                            <a href="admin.php?section=licenses&action=reset_downloads&id=<?= $license_id ?>&return_to=detail&<?= get_session_id_param() ?>"
                               class="btn btn-secondary"
                               onclick="return confirm('Tem certeza que deseja zerar a contagem de downloads desta licença?')">
                                <i class="bi bi-arrow-counterclockwise"></i> Zerar Downloads
                            </a>

                            <a href="admin.php?section=licenses&action=toggle_status&id=<?= $license_id ?>&status=<?= $license['status'] === 'active' ? 'disabled' : 'active' ?>&return_to=detail&<?= get_session_id_param() ?>"
                               class="btn <?= $license['status'] === 'active' ? 'btn-danger' : 'btn-success' ?>"
                               onclick="return confirm('Tem certeza que deseja <?= $license['status'] === 'active' ? 'desativar' : 'ativar' ?> esta licença?')">
                                <i class="bi <?= $license['status'] === 'active' ? 'bi-x-circle' : 'bi-check-circle' ?>"></i>
                                <?= $license['status'] === 'active' ? 'Desativar Licença' : 'Ativar Licença' ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Produtos Digitais</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($digital_products)): ?>
                        <div class="alert alert-info" role="alert">
                            Nenhum produto digital associado a esta licença.
                        </div>
                    <?php else: ?>
                        <div class="list-group">
                            <?php foreach ($digital_products as $product): ?>
                                <?php
                                
                                $product_info = db_query(
                                    "SELECT * FROM products WHERE id = :id",
                                    [':id' => $product['product_id']],
                                    true
                                );

                                
                                $file_types = get_digital_product_file_types($product['id']);
                                ?>
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-1"><?= sanitize_input($product_info['name_pt'] ?? 'Produto #' . $product['product_id']) ?></h6>
                                        <a href="admin.php?section=products&action=edit&id=<?= (int)$product['product_id'] ?>&<?= get_session_id_param() ?>"
                                           class="btn btn-sm btn-outline-primary">
                                            Ver Produto
                                        </a>
                                    </div>
                                    <p class="mb-1">
                                        <small class="text-muted">Arquivo: <?= basename($product['file_path']) ?></small>
                                    </p>
                                    <?php if (!empty($file_types)): ?>
                                        <div class="mt-2">
                                            <small class="text-muted">Extensões:</small>
                                            <div class="d-flex flex-wrap gap-1 mt-1">
                                                <?php foreach ($file_types as $type): ?>
                                                    <span class="badge bg-secondary"><?= sanitize_input($type['extension']) ?></span>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Histórico de Downloads</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($downloads)): ?>
                        <div class="alert alert-info" role="alert">
                            Nenhum download registrado para esta licença.
                        </div>
                    <?php else: ?>
                        <div class="d-flex justify-content-end mb-2">
                            <a href="admin.php?section=licenses&action=delete_download_history&id=<?= $license_id ?>&<?= get_session_id_param() ?>"
                               class="btn btn-sm btn-danger"
                               onclick="return confirm('Tem certeza que deseja excluir todo o histórico de downloads desta licença?')">
                                <i class="bi bi-trash"></i> Excluir Todo Histórico
                            </a>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Data</th>
                                        <th>IP</th>
                                        <th>Navegador</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($downloads as $download): ?>
                                        <tr>
                                            <td><?= date('d/m/Y H:i', strtotime($download['download_date'])) ?></td>
                                            <td><?= sanitize_input($download['ip_address']) ?></td>
                                            <td>
                                                <small class="text-muted"><?= sanitize_input(substr($download['user_agent'], 0, 50)) . (strlen($download['user_agent']) > 50 ? '...' : '') ?></small>
                                            </td>
                                            <td>
                                                <a href="admin.php?section=licenses&action=delete_download_history&id=<?= $license_id ?>&download_id=<?= $download['id'] ?>&<?= get_session_id_param() ?>"
                                                   class="btn btn-sm btn-danger"
                                                   onclick="return confirm('Tem certeza que deseja excluir este registro de download?')">
                                                    <i class="bi bi-trash"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="admin.php?section=licenses&action=detail&id=<?= $license_id ?>&<?= get_session_id_param() ?>">
                <?= csrf_input_field() ?>
                <div class="modal-header">
                    <h5 class="modal-title" id="updateStatusModalLabel">Atualizar Status da Licença</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="active" <?= $license['status'] === 'active' ? 'selected' : '' ?>>Ativo</option>
                            <option value="waiting_payment" <?= $license['status'] === 'waiting_payment' ? 'selected' : '' ?>>Aguardando Pagamento</option>
                            <option value="disabled" <?= $license['status'] === 'disabled' ? 'selected' : '' ?>>Desativado</option>
                            <option value="canceled" <?= $license['status'] === 'canceled' ? 'selected' : '' ?>>Cancelado</option>
                        </select>
                    </div>

                    <div class="form-check mb-3" id="sendEmailContainer" style="display: none;">
                        <input class="form-check-input" type="checkbox" value="1" id="send_email" name="send_email" checked>
                        <label class="form-check-label" for="send_email">
                            Enviar email com link de download ao ativar
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" name="update_status" value="1" class="btn btn-primary">Atualizar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Expiry Date Modal -->
<div class="modal fade" id="editExpiryModal" tabindex="-1" aria-labelledby="editExpiryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="admin.php?section=licenses&action=update_expiry&id=<?= $license_id ?>&return_to=detail" id="editExpiryForm">
                <?= csrf_input_field() ?>
                <div class="modal-header">
                    <h5 class="modal-title" id="editExpiryModalLabel">Editar Data de Expiração</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="expiry_date" class="form-label">Nova Data de Expiração</label>
                        <input type="date" class="form-control" id="expiry_date" name="expiry_date"
                               value="<?= $license['expiry_date'] ? date('Y-m-d', strtotime($license['expiry_date'])) : '' ?>" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Salvar</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Initialize modals immediately to ensure they're ready
let updateStatusModalInstance = null;
let editExpiryModalInstance = null;

// Function to initialize modals
function initModals() {
    if (typeof bootstrap !== 'undefined') {
        const updateStatusModal = document.getElementById('updateStatusModal');
        const editExpiryModal = document.getElementById('editExpiryModal');

        if (updateStatusModal) {
            updateStatusModalInstance = new bootstrap.Modal(updateStatusModal);
        }

        if (editExpiryModal) {
            editExpiryModalInstance = new bootstrap.Modal(editExpiryModal);
        }
    }
}

// Try to initialize modals immediately
initModals();

document.addEventListener('DOMContentLoaded', function() {
    // Show/hide send email option based on status selection
    const statusSelect = document.getElementById('status');
    const sendEmailContainer = document.getElementById('sendEmailContainer');
    const currentStatus = '<?= $license['status'] ?>';

    function updateSendEmailVisibility() {
        if (statusSelect && sendEmailContainer) {
            if (statusSelect.value === 'active' && currentStatus !== 'active') {
                sendEmailContainer.style.display = 'block';
            } else {
                sendEmailContainer.style.display = 'none';
            }
        }
    }

    // Re-initialize modals if they weren't initialized earlier
    if (!updateStatusModalInstance || !editExpiryModalInstance) {
        initModals();
    }

    // Add click handlers for modal buttons
    const updateStatusBtn = document.querySelector('button[data-bs-toggle="modal"][data-bs-target="#updateStatusModal"]');
    if (updateStatusBtn && updateStatusModalInstance) {
        updateStatusBtn.addEventListener('click', function(e) {
            e.preventDefault();
            updateStatusModalInstance.show();
        });
    }

    // Use direct ID selector for the expiry button
    const editExpiryBtn = document.getElementById('editExpiryBtn');
    if (editExpiryBtn && editExpiryModalInstance) {
        editExpiryBtn.addEventListener('click', function(e) {
            e.preventDefault();
            editExpiryModalInstance.show();
        });
    }

    // Handle form submission for the expiry date form
    const editExpiryForm = document.getElementById('editExpiryForm');
    if (editExpiryForm) {
        editExpiryForm.addEventListener('submit', function(e) {
            // Add session ID parameter to the form action
            const currentAction = editExpiryForm.getAttribute('action');
            const sessionParam = '<?= get_session_id_param() ?>';

            if (currentAction.indexOf(sessionParam) === -1) {
                editExpiryForm.setAttribute('action', currentAction + '&' + sessionParam);
            }
        });
    }

    updateSendEmailVisibility();
    if (statusSelect) {
        statusSelect.addEventListener('change', updateSendEmailVisibility);
    }
});
</script>
