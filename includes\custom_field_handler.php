<?php

function handle_custom_field_form($action, $field_id = null) {
    
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $field_type_id = filter_input(INPUT_POST, 'field_type_id', FILTER_VALIDATE_INT);
    $min_chars = filter_input(INPUT_POST, 'min_chars', FILTER_VALIDATE_INT) ?: 0;
    $max_chars = filter_input(INPUT_POST, 'max_chars', FILTER_VALIDATE_INT) ?: 255;
    $price_modifier = filter_input(INPUT_POST, 'price_modifier', FILTER_VALIDATE_FLOAT) ?: 0.0;
    $is_required = isset($_POST['is_required']) ? (int)$_POST['is_required'] : 0;
    $is_active = isset($_POST['is_active']) ? (int)$_POST['is_active'] : 1;
    $config = $_POST['config'] ?? [];
    
    
    $errors = [];
    if (empty($name)) {
        $errors[] = "Nome do campo é obrigatório.";
    }
    
    if (!$field_type_id) {
        $errors[] = "Tipo de campo é obrigatório.";
    }
    
    if (!empty($errors)) {
        foreach ($errors as $error) {
            add_flash_message($error, 'danger');
        }
        $_SESSION['form_data'] = $_POST;
        $redirect_url = 'admin.php?section=custom_fields&action=';
        $redirect_url .= ($action === 'create') ? 'new' : 'edit&id=' . $field_id;
        header('Location: ' . $redirect_url . '&' . get_session_id_param());
        exit;
    }
    
    try {
        
        $field_data = [
            'name' => $name,
            'description' => $description,
            'field_type_id' => $field_type_id,
            'min_chars' => $min_chars,
            'max_chars' => $max_chars,
            'price_modifier' => $price_modifier,
            'is_required' => $is_required,
            'is_active' => $is_active
        ];
        
        
        if (!empty($config)) {
            $field_data['config'] = $config;
        }
        
        
        if ($action === 'create') {
            $result = create_custom_field($field_data);
            if ($result) {
                add_flash_message('Campo personalizado criado com sucesso!', 'success');
            } else {
                throw new Exception("Falha ao criar campo personalizado.");
            }
        } else { 
            if (!$field_id) {
                throw new Exception("ID do campo inválido.");
            }
            
            $result = update_custom_field($field_id, $field_data);
            if ($result) {
                add_flash_message('Campo personalizado atualizado com sucesso!', 'success');
            } else {
                throw new Exception("Falha ao atualizar campo personalizado.");
            }
        }
        
        header('Location: admin.php?section=custom_fields&' . get_session_id_param());
        exit;
        
    } catch (Exception $e) {
        add_flash_message('Erro ao guardar campo personalizado: ' . $e->getMessage(), 'danger');
        $_SESSION['form_data'] = $_POST;
        $redirect_url = 'admin.php?section=custom_fields&action=';
        $redirect_url .= ($action === 'create') ? 'new' : 'edit&id=' . $field_id;
        header('Location: ' . $redirect_url . '&' . get_session_id_param());
        exit;
    }
}
