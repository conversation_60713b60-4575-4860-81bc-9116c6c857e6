

(function() {
    
    const commonIcons = {
        'Business': [
            'ri-store-line', 'ri-shopping-bag-line', 'ri-shopping-cart-line', 'ri-price-tag-3-line',
            'ri-coupon-line', 'ri-gift-line', 'ri-percent-line', 'ri-bank-card-line',
            'ri-wallet-line', 'ri-coin-line', 'ri-money-euro-circle-line', 'ri-exchange-line'
        ],
        'Shipping & Logistics': [
            'ri-truck-line', 'ri-ship-line', 'ri-plane-line', 'ri-rocket-line',
            'ri-bike-line', 'ri-car-line', 'ri-map-pin-line', 'ri-route-line',
            'ri-box-line', 'ri-archive-line', 'ri-package-line', 'ri-timer-line'
        ],
        'Communication': [
            'ri-mail-line', 'ri-phone-line', 'ri-chat-1-line', 'ri-message-2-line',
            'ri-question-line', 'ri-information-line', 'ri-customer-service-line', 'ri-feedback-line'
        ],
        'User & Account': [
            'ri-user-line', 'ri-user-settings-line', 'ri-lock-line', 'ri-shield-check-line',
            'ri-login-circle-line', 'ri-logout-circle-line', 'ri-profile-line', 'ri-account-circle-line'
        ],
        'Interface': [
            'ri-check-line', 'ri-close-line', 'ri-add-line', 'ri-subtract-line',
            'ri-arrow-left-line', 'ri-arrow-right-line', 'ri-arrow-up-line', 'ri-arrow-down-line',
            'ri-refresh-line', 'ri-settings-line', 'ri-search-line', 'ri-filter-line'
        ],
        'Misc': [
            'ri-heart-line', 'ri-star-line', 'ri-time-line', 'ri-calendar-line',
            'ri-file-list-line', 'ri-download-line', 'ri-upload-line', 'ri-share-line',
            'ri-link', 'ri-global-line', 'ri-home-line', 'ri-bookmark-line'
        ]
    };

    

    function createIconSelector(inputElement) {
        
        if (inputElement.closest('.icon-selector-container')) {
            
            return;
        }

        
        const container = document.createElement('div');
        container.className = 'icon-selector-container position-relative';

        
        inputElement.parentNode.insertBefore(container, inputElement);
        container.appendChild(inputElement);

        
        const inputGroup = document.createElement('div');
        inputGroup.className = 'input-group';
        container.appendChild(inputGroup);

        
        inputElement.classList.add('icon-selector-input');
        inputGroup.appendChild(inputElement);

        
        const dropdownButton = document.createElement('button');
        dropdownButton.className = 'btn btn-outline-secondary dropdown-toggle';
        dropdownButton.type = 'button';
        dropdownButton.innerHTML = '<i class="ri-list-settings-line"></i>';
        dropdownButton.setAttribute('data-bs-toggle', 'dropdown');
        dropdownButton.setAttribute('aria-expanded', 'false');
        dropdownButton.setAttribute('data-bs-auto-close', 'outside'); 

        
        dropdownButton.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation(); 
        });

        inputGroup.appendChild(dropdownButton);

        
        const dropdownMenu = document.createElement('div');
        dropdownMenu.className = 'dropdown-menu p-0 icon-selector-dropdown';
        dropdownMenu.style.width = '320px';

        
        dropdownButton.addEventListener('shown.bs.dropdown', function() {
            
            dropdownMenu.classList.add('keep-open');
        });

        inputGroup.appendChild(dropdownMenu);

        
        const tabsContainer = document.createElement('div');
        tabsContainer.className = 'nav nav-tabs';
        tabsContainer.setAttribute('role', 'tablist');
        dropdownMenu.appendChild(tabsContainer);

        
        const tabContent = document.createElement('div');
        tabContent.className = 'tab-content p-2';
        tabContent.style.maxHeight = '250px';
        tabContent.style.overflowY = 'auto';
        dropdownMenu.appendChild(tabContent);

        
        let isFirst = true;
        Object.keys(commonIcons).forEach(category => {
            
            const uniqueId = `icon-${Math.random().toString(36).substring(2, 10)}`;
            const tabId = `${uniqueId}-tab-${category.toLowerCase().replace(/\s+/g, '-')}`;

            
            const tab = document.createElement('button');
            tab.className = `nav-link ${isFirst ? 'active' : ''}`;
            tab.id = `${tabId}-tab`;
            tab.setAttribute('data-bs-toggle', 'tab');
            tab.setAttribute('data-bs-target', `#${tabId}`);
            tab.setAttribute('role', 'tab');
            tab.setAttribute('aria-controls', tabId);
            tab.setAttribute('aria-selected', isFirst ? 'true' : 'false');
            tab.textContent = category;
            tab.type = 'button'; 

            
            tab.addEventListener('click', function(event) {
                
                if (event.target.form && event.target.form.checkValidity &&
                    typeof event.target.form.checkValidity === 'function') {
                    event.preventDefault();
                }
                
            });

            tabsContainer.appendChild(tab);

            
            const tabPane = document.createElement('div');
            tabPane.className = `tab-pane fade ${isFirst ? 'show active' : ''}`;
            tabPane.id = tabId;
            tabPane.setAttribute('role', 'tabpanel');
            tabPane.setAttribute('aria-labelledby', `${tabId}-tab`);

            
            const iconsGrid = document.createElement('div');
            iconsGrid.className = 'd-flex flex-wrap gap-2';

            commonIcons[category].forEach(iconClass => {
                const iconButton = document.createElement('button');
                iconButton.className = 'btn btn-outline-secondary icon-btn';
                iconButton.type = 'button';
                iconButton.setAttribute('data-icon', iconClass);
                iconButton.innerHTML = `<i class="${iconClass}"></i>`;
                iconButton.title = iconClass;
                iconButton.style.width = '40px';
                iconButton.style.height = '40px';
                iconButton.style.padding = '0';
                iconButton.style.display = 'flex';
                iconButton.style.alignItems = 'center';
                iconButton.style.justifyContent = 'center';

                
                iconButton.addEventListener('click', function(event) {
                    
                    event.preventDefault();

                    
                    inputElement.value = iconClass;

                    
                    const previewIcon = inputGroup.querySelector('.input-group-text i');
                    if (previewIcon) {
                        previewIcon.className = iconClass;
                    }

                    
                    const changeEvent = new Event('change', { bubbles: true });
                    inputElement.dispatchEvent(changeEvent);

                    
                    dropdownMenu.classList.remove('keep-open');
                    dropdownMenu.classList.remove('show'); 
                    dropdownButton.setAttribute('aria-expanded', 'false'); 
                    dropdownButton.classList.remove('show'); 

                    
                    try {
                        const dropdownInstance = bootstrap.Dropdown.getInstance(dropdownButton);
                        if (dropdownInstance) {
                            dropdownInstance.hide();
                        } else {
                             
                            if (typeof $ !== 'undefined' && $.fn.dropdown) {
                                $(dropdownButton).dropdown('hide');
                            }
                        }
                    } catch (error) {
                        console.error('Error closing dropdown via Bootstrap API:', error);
                         if (typeof $ !== 'undefined' && $.fn.dropdown) {
                            try {
                                $(dropdownButton).dropdown('hide');
                            } catch (e) {
                                console.error('Failed to close dropdown with jQuery fallback:', e);
                            }
                        }
                    }
                });

                iconsGrid.appendChild(iconButton);
            });

            tabPane.appendChild(iconsGrid);
            tabContent.appendChild(tabPane);

            isFirst = false;
        });

        
        const searchContainer = document.createElement('div');
        searchContainer.className = 'p-2 border-bottom';

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'form-control form-control-sm';
        searchInput.placeholder = 'Pesquisar ícones...';

        
        searchInput.addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                event.stopPropagation();
                return false;
            }
        });

        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const iconButtons = dropdownMenu.querySelectorAll('.icon-btn');

            iconButtons.forEach(button => {
                const iconClass = button.getAttribute('data-icon');
                if (iconClass.toLowerCase().includes(searchTerm)) {
                    button.style.display = 'flex';
                } else {
                    button.style.display = 'none';
                }
            });
        });

        searchContainer.appendChild(searchInput);
        dropdownMenu.insertBefore(searchContainer, tabsContainer);

        
        const footerContainer = document.createElement('div');
        footerContainer.className = 'p-2 border-top text-center';

        const allIconsLink = document.createElement('a');
        allIconsLink.href = 'https://remixicon.com/';
        allIconsLink.target = '_blank';
        allIconsLink.className = 'text-decoration-none small';
        allIconsLink.textContent = 'Ver todos os ícones';

        
        allIconsLink.addEventListener('click', function(event) {
            
            
            event.stopPropagation();
        });

        footerContainer.appendChild(allIconsLink);
        dropdownMenu.appendChild(footerContainer);

        
        dropdownMenu.addEventListener('click', function(event) {
            if (event.target.classList.contains('icon-btn') || event.target.closest('.icon-btn')) {
                
                return;
            }
            
            event.stopPropagation();
        });

        
        const currentIcon = document.createElement('span');
        currentIcon.className = 'input-group-text';
        currentIcon.innerHTML = inputElement.value ? `<i class="${inputElement.value}"></i>` : '<i class="ri-question-line text-muted"></i>';
        inputGroup.insertBefore(currentIcon, inputElement);

        
        inputElement.addEventListener('change', function() {
            currentIcon.innerHTML = this.value ? `<i class="${this.value}"></i>` : '<i class="ri-question-line text-muted"></i>';
        });

        
        
        if (typeof bootstrap !== 'undefined' && bootstrap.Dropdown) {
            
            
            if (!bootstrap.Dropdown.getInstance(dropdownButton)) {
                try {
                    new bootstrap.Dropdown(dropdownButton);
                } catch (e) {
                    console.error('Error manually initializing Bootstrap dropdown for dynamic button:', e, dropdownButton);
                }
            } else {
            }
        } else {
            console.warn('Bootstrap Dropdown API not available for manual initialization of dynamic button.');
        }
    }

    

    function initIconSelectors() {
        document.querySelectorAll('.info-field-icon').forEach(input => {
            createIconSelector(input);
        });

        
        
    }

    
    document.addEventListener('DOMContentLoaded', function() {
        initIconSelectors();

        
        
        
        
    });

    

    function closeAllIconDropdowns() {
        document.querySelectorAll('.icon-selector-dropdown').forEach(dropdown => {
            dropdown.classList.remove('keep-open');
            dropdown.classList.add('closing');

            
            const dropdownButton = dropdown.closest('.input-group').querySelector('[data-bs-toggle="dropdown"]');
            if (dropdownButton) {
                try {
                    const dropdownInstance = bootstrap.Dropdown.getInstance(dropdownButton);
                    if (dropdownInstance) {
                        dropdownInstance.hide();
                    }
                } catch (error) {
                    console.error('Error closing dropdown:', error);
                }
            }

            
            setTimeout(() => {
                dropdown.classList.remove('closing');
            }, 300);
        });
    }

    
    document.addEventListener('click', function(event) {
        
        if (!event.target.closest('.icon-selector-container')) {
            closeAllIconDropdowns();
        }
    });

    
    window.AdminIconSelector = {
        createIconSelector,
        initIconSelectors,
        closeAllIconDropdowns
    };
})();
