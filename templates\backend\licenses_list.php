<?php

require_once __DIR__ . '/../../includes/digital_product_functions.php';
require_once __DIR__ . '/../../includes/license_encryption_functions.php';

$status_filter = sanitize_input($_GET['status'] ?? '');

$licenses = get_all_licenses($status_filter ?: null);

$status_counts = [
    'all' => 0,
    'active' => 0,
    'waiting_payment' => 0,
    'disabled' => 0,
    'canceled' => 0
];

$all_licenses = get_all_licenses();
foreach ($all_licenses as $license) {
    $status_counts['all']++;
    $status = $license['status'] ?? 'unknown';
    if (isset($status_counts[$status])) {
        $status_counts[$status]++;
    }
}

display_flash_messages();

function get_license_status_badge_class($status) {
    switch ($status) {
        case 'active':
            return 'bg-success';
        case 'waiting_payment':
            return 'bg-warning text-dark';
        case 'disabled':
            return 'bg-secondary';
        case 'canceled':
            return 'bg-danger';
        default:
            return 'bg-secondary';
    }
}

function get_license_status_name($status) {
    switch ($status) {
        case 'active':
            return 'Ativo';
        case 'waiting_payment':
            return 'Aguardando Pagamento';
        case 'disabled':
            return 'Desativado';
        case 'canceled':
            return 'Cancelado';
        default:
            return 'Desconhecido';
    }
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Licenças</h1>
        <a href="admin.php?section=licenses&action=new&<?= get_session_id_param() ?>" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Nova Licença
        </a>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li class="nav-item">
                    <a class="nav-link <?= $status_filter === '' ? 'active' : '' ?>"
                       href="admin.php?section=licenses&<?= get_session_id_param() ?>">
                        Todas <span class="badge bg-secondary"><?= $status_counts['all'] ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= $status_filter === 'active' ? 'active' : '' ?>"
                       href="admin.php?section=licenses&status=active&<?= get_session_id_param() ?>">
                        Ativas <span class="badge bg-success"><?= $status_counts['active'] ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= $status_filter === 'waiting_payment' ? 'active' : '' ?>"
                       href="admin.php?section=licenses&status=waiting_payment&<?= get_session_id_param() ?>">
                        Aguardando Pagamento <span class="badge bg-warning text-dark"><?= $status_counts['waiting_payment'] ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= $status_filter === 'disabled' ? 'active' : '' ?>"
                       href="admin.php?section=licenses&status=disabled&<?= get_session_id_param() ?>">
                        Desativadas <span class="badge bg-secondary"><?= $status_counts['disabled'] ?></span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?= $status_filter === 'canceled' ? 'active' : '' ?>"
                       href="admin.php?section=licenses&status=canceled&<?= get_session_id_param() ?>">
                        Canceladas <span class="badge bg-danger"><?= $status_counts['canceled'] ?></span>
                    </a>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <?php if (empty($licenses)): ?>
                <div class="alert alert-info" role="alert">
                    Nenhuma licença encontrada.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Código</th>
                                <th>Cliente</th>
                                <th>Email</th>
                                <th>Status</th>
                                <th>Expiração</th>
                                <th>Downloads</th>
                                <th>Pedido</th>
                                <th class="text-end">Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($licenses as $license): ?>
                                <tr>
                                    <td><code><?= sanitize_input($license['license_code']) ?></code></td>
                                    <td><?= sanitize_input(get_decrypted_license_name($license)) ?></td>
                                    <td><?= sanitize_input(get_decrypted_license_email($license)) ?></td>
                                    <td>
                                        <span class="badge <?= get_license_status_badge_class($license['status']) ?>">
                                            <?= get_license_status_name($license['status']) ?>
                                        </span>
                                    </td>
                                    <td><?= $license['expiry_date'] ? date('d/m/Y', strtotime($license['expiry_date'])) : 'N/A' ?></td>
                                    <td><?= (int)$license['downloads_used'] ?> / <?= (int)$license['download_limit'] ?></td>
                                    <td>
                                        <?php if ($license['order_id']): ?>
                                            <a href="admin.php?section=orders&action=detail&id=<?= (int)$license['order_id'] ?>&<?= get_session_id_param() ?>"
                                               class="btn btn-sm btn-outline-info">
                                                #<?= (int)$license['order_id'] ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Manual</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-end">
                                        <div class="btn-group">
                                            <a href="admin.php?section=licenses&action=edit&id=<?= (int)$license['id'] ?>&<?= get_session_id_param() ?>"
                                               class="btn btn-sm btn-info" title="Editar">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <a href="admin.php?section=licenses&action=detail&id=<?= (int)$license['id'] ?>&<?= get_session_id_param() ?>"
                                               class="btn btn-sm btn-primary" title="Ver Detalhes">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="admin.php?section=licenses&action=delete&id=<?= (int)$license['id'] ?>&csrf_token=<?= $csrf_token ?? '' ?>&<?= get_session_id_param() ?>"
                                               class="btn btn-sm btn-danger"
                                               title="Excluir"
                                               onclick="return confirm('Tem certeza que deseja excluir a licença <?= sanitize_input($license['license_code']) ?>?\n\nEsta ação não pode ser desfeita.');">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- No JavaScript needed for direct link approach -->
