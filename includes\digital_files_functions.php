<?php

function get_all_digital_files(array $filters = [], int $page = 0, int $per_page = 0): array
{
    $conditions = [];
    $params = [];

    // Apply filters
    if (!empty($filters['name'])) {
        $conditions[] = "(original_filename LIKE :name OR display_name LIKE :name OR short_name LIKE :name)";
        $params[':name'] = '%' . $filters['name'] . '%';
    }

    if (!empty($filters['file_type'])) {
        $conditions[] = "file_type LIKE :file_type";
        $params[':file_type'] = '%' . $filters['file_type'] . '%';
    }

    // Build the SQL query
    $sql = "SELECT * FROM digital_files";

    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }

    $sql .= " ORDER BY created_at DESC";

    // Add pagination if requested
    if ($page > 0 && $per_page > 0) {
        $offset = ($page - 1) * $per_page;
        $sql .= " LIMIT :limit OFFSET :offset";
        $params[':limit'] = $per_page;
        $params[':offset'] = $offset;
    }

    $result = db_query($sql, $params, false, true);

    if (is_array($result)) {
        // For each file, get its file type associations
        foreach ($result as &$file) {
            $file['file_type_ids'] = get_digital_file_file_type_ids((int)$file['id']);
        }
        return $result;
    }

    return [];
}

function get_orphaned_digital_files(): array
{
    $sql = "SELECT df.* FROM digital_files df
            LEFT JOIN digital_products dp ON df.id = dp.digital_file_id
            WHERE dp.id IS NULL
            ORDER BY df.created_at DESC";
    $result = db_query($sql, [], false, true);
    return is_array($result) ? $result : [];
}

function get_digital_file_by_id(int $file_id): array|false
{
    if ($file_id <= 0) return false;

    $sql = "SELECT * FROM digital_files WHERE id = :id";
    return db_query($sql, [':id' => $file_id], true);
}

function create_digital_file(array $data): int|false
{
    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        // Convert absolute path to relative path if needed
        $file_path = $data['file_path'];
        if (strpos($file_path, 'C:\\') === 0 || strpos($file_path, 'C:/') === 0) {
            // Extract the filename from the absolute path
            $filename = basename($file_path);
            // Create the relative path
            $file_path = '../digital_products/' . $filename;
            error_log("Converted absolute path to relative path: {$data['file_path']} -> {$file_path}");
        }

        $sql = "INSERT INTO digital_files (
                    original_filename, display_name, short_name, file_path, file_size, file_type, description,
                    created_at, updated_at
                ) VALUES (
                    :original_filename, :display_name, :short_name, :file_path, :file_size, :file_type, :description,
                    datetime('now', 'localtime'), datetime('now', 'localtime')
                )";

        $params = [
            ':original_filename' => $data['original_filename'],
            ':display_name' => $data['display_name'] ?? $data['original_filename'],
            ':short_name' => $data['short_name'] ?? '',
            ':file_path' => $file_path,
            ':file_size' => $data['file_size'],
            ':file_type' => $data['file_type'],
            ':description' => $data['description'] ?? ''
        ];

        $result = db_query($sql, $params);

        if ($result === false) {
            throw new Exception("Failed to insert digital file record");
        }

        $last_id = 0;
        if ($pdo instanceof PDO) {
            $last_id = (int)$pdo->lastInsertId();
            if ($last_id === 0) {
                throw new Exception("Failed to get last insert ID");
            }
        } else {
            throw new Exception("Invalid PDO connection");
        }

        // Save file type associations if provided
        if (isset($data['file_types']) && is_array($data['file_types']) && !empty($data['file_types'])) {
            error_log("create_digital_file: Saving file type associations for file ID $last_id: " . implode(', ', $data['file_types']));
            // Use the update_digital_file_file_types function to save file type associations
            // Pass false for manage_transaction since we're already in a transaction
            update_digital_file_file_types($last_id, $data['file_types'], false);
        } else {
            error_log("create_digital_file: No file types to save for file ID $last_id");
        }

        $pdo->commit();
        return $last_id;

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Error creating digital file: " . $e->getMessage());
        return false;
    }
}

function update_digital_file(int $file_id, array $data): bool
{
    if ($file_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        $sql = "UPDATE digital_files SET updated_at = datetime('now', 'localtime')";
        $params = [':id' => $file_id];

        // Update basic file information
        if (isset($data['description'])) {
            $sql .= ", description = :description";
            $params[':description'] = $data['description'];
        }
        if (isset($data['display_name'])) {
            $sql .= ", display_name = :display_name";
            $params[':display_name'] = $data['display_name'];
        }
        if (isset($data['short_name'])) {
            $sql .= ", short_name = :short_name";
            $params[':short_name'] = $data['short_name'];
        }

        $sql .= " WHERE id = :id";

        $result = db_query($sql, $params);
        if ($result === false) {
            throw new Exception("Failed to update digital file record");
        }

        // Update file type associations if provided
        if (isset($data['file_types'])) {
            // Use the new function to update file type associations
            update_digital_file_file_types($file_id, $data['file_types']);
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Error updating digital file: " . $e->getMessage());
        return false;
    }
}

function delete_digital_file(int $file_id): bool
{
    if ($file_id <= 0) return false;


    $file = get_digital_file_by_id($file_id);
    if (!$file) return false;


    $usage = get_digital_file_usage($file_id);
    if (count($usage) > 0) {
        return false;
    }

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();


        $sql = "DELETE FROM digital_files WHERE id = :id";
        $result = db_query($sql, [':id' => $file_id]);

        if ($result === false) {
            throw new Exception("Failed to delete digital file record");
        }


        if (file_exists($file['file_path'])) {
            if (!@unlink($file['file_path'])) {
            }
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function get_digital_file_usage(int $file_id): array
{
    if ($file_id <= 0) return [];

    $sql = "SELECT p.id, p.name_pt FROM products p
            JOIN digital_products dp ON p.id = dp.product_id
            WHERE dp.digital_file_id = :file_id";

    $result = db_query($sql, [':file_id' => $file_id], false, true);
    return is_array($result) ? $result : [];
}

function get_all_digital_products(): array
{

    $pdo = get_db_connection();
    $stmt = $pdo->query("PRAGMA table_info(digital_files)");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);


    $sql = "SELECT p.id as product_id, p.name_pt, p.slug, p.description_pt, p.base_price, p.is_active, p.created_at AS product_created_at, p.updated_at AS product_updated_at, p.product_type, p.seo_title, p.seo_description, p.seo_keywords, p.og_title, p.og_description, p.og_image, p.twitter_card, p.twitter_title, p.twitter_description, p.twitter_image,
                   dp.id as digital_product_id, dp.expiry_days, dp.download_limit, dp.digital_file_id,
                   df.file_path, df.original_filename AS df_original_filename";


    if (in_array('display_name', $columns)) {
        $sql .= ", df.display_name AS df_display_name";
    } else if (in_array('short_name', $columns)) {

        $sql .= ", df.short_name AS df_display_name";
    } else {

        $sql .= ", df.original_filename AS df_display_name";
    }

    $sql .= " FROM products p
              JOIN digital_products dp ON p.id = dp.product_id
              LEFT JOIN digital_files df ON dp.digital_file_id = df.id
              WHERE p.product_type = 'digital'
              ORDER BY p.name_pt ASC";

    $result = db_query($sql, [], false, true);
    return is_array($result) ? $result : [];
}

function format_file_size(int $size): string
{
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    $i = 0;
    while ($size >= 1024 && $i < count($units) - 1) {
        $size /= 1024;
        $i++;
    }
    return round($size, 2) . ' ' . $units[$i];
}

/**
 * Count total digital files with optional filters
 */
function count_digital_files(array $filters = []): int
{
    $conditions = [];
    $params = [];

    // Apply filters
    if (!empty($filters['name'])) {
        $conditions[] = "(original_filename LIKE :name OR display_name LIKE :name OR short_name LIKE :name)";
        $params[':name'] = '%' . $filters['name'] . '%';
    }

    if (!empty($filters['file_type'])) {
        $conditions[] = "file_type LIKE :file_type";
        $params[':file_type'] = '%' . $filters['file_type'] . '%';
    }

    // Build the SQL query
    $sql = "SELECT COUNT(*) as total FROM digital_files";

    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }

    $result = db_query($sql, $params, true);
    return $result ? (int)$result['total'] : 0;
}

/**
 * Get file type associations for a digital file
 */
function get_digital_file_file_types(int $digital_file_id): array
{
    if ($digital_file_id <= 0) return [];

    // Get file types directly from the digital_file_type_associations table
    $sql = "SELECT dfft.* FROM digital_files_file_types dfft
            JOIN digital_file_type_associations dfta ON dfft.id = dfta.file_type_id
            WHERE dfta.digital_file_id = :digital_file_id";

    $result = db_query($sql, [':digital_file_id' => $digital_file_id], false, true);
    return is_array($result) ? $result : [];
}

/**
 * Get file type IDs associated with a digital file
 *
 * This function gets file type IDs directly from the digital_file_type_associations table
 */
function get_digital_file_file_type_ids(int $digital_file_id): array
{
    if ($digital_file_id <= 0) {
        error_log("Invalid digital_file_id: $digital_file_id");
        return [];
    }

    // Get file type IDs directly from the digital_file_type_associations table
    $sql = "SELECT file_type_id FROM digital_file_type_associations WHERE digital_file_id = :digital_file_id";
    $result = db_query($sql, [':digital_file_id' => $digital_file_id], false, true);

    if (is_array($result) && !empty($result)) {
        // Extract just the IDs
        $ids = array_map(function($row) {
            return (int)$row['file_type_id'];
        }, $result);

        error_log("Found " . count($ids) . " file type associations for file ID $digital_file_id in digital_file_type_associations table");
        return $ids;
    }

    // If no associations found, just return an empty array
    error_log("No file type associations found for file ID $digital_file_id");
    return [];
}

/**
 * Update file type associations for a digital file
 *
 * This function updates file type associations directly in the digital_file_type_associations table
 *
 * @param int $digital_file_id The ID of the digital file
 * @param array $file_type_ids Array of file type IDs to associate with the file
 * @param bool $manage_transaction Whether this function should manage its own transaction
 * @return bool Success or failure
 */
function update_digital_file_file_types(int $digital_file_id, array $file_type_ids, bool $manage_transaction = true): bool
{
    error_log("update_digital_file_file_types: Starting with file ID: $digital_file_id, file types: " . json_encode($file_type_ids));

    if ($digital_file_id <= 0) {
        error_log("update_digital_file_file_types: Invalid digital_file_id: $digital_file_id");
        return false;
    }

    // Verify file exists
    $file = get_digital_file_by_id($digital_file_id);
    if (!$file) {
        error_log("update_digital_file_file_types: File not found with ID: $digital_file_id");
        return false;
    }
    error_log("update_digital_file_file_types: File found: " . json_encode($file));

    // If file_type_ids is empty, check if we should preserve existing associations
    // This is a special case when saving from the product edit form
    if (empty($file_type_ids) && defined('PRESERVE_FILE_TYPE_ASSOCIATIONS') && PRESERVE_FILE_TYPE_ASSOCIATIONS) {
        // Get existing file type associations
        $existing_file_type_ids = get_digital_file_file_type_ids($digital_file_id);
        if (!empty($existing_file_type_ids)) {
            error_log("update_digital_file_file_types: Preserving existing file type associations: " . json_encode($existing_file_type_ids));
            $file_type_ids = $existing_file_type_ids;
        }
    }

    // Verify file types exist
    if (!empty($file_type_ids)) {
        $valid_types = [];
        $pdo = get_db_connection();
        if (!$pdo) {
            error_log("update_digital_file_file_types: Failed to get database connection");
            return false;
        }

        // Check if the file types table exists
        try {
            $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_files_file_types'");
            $table_exists = $stmt->fetch();
            if (!$table_exists) {
                error_log("update_digital_file_file_types: digital_files_file_types table does not exist");
                return false;
            }

            // Check if the associations table exists
            $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_file_type_associations'");
            $assoc_table_exists = $stmt->fetch();
            if (!$assoc_table_exists) {
                error_log("update_digital_file_file_types: digital_file_type_associations table does not exist");
                return false;
            }

            // Verify each file type ID exists
            foreach ($file_type_ids as $type_id) {
                $stmt = $pdo->prepare("SELECT id FROM digital_files_file_types WHERE id = :id");
                $stmt->execute([':id' => $type_id]);
                $type = $stmt->fetch();
                if ($type) {
                    $valid_types[] = (int)$type_id;
                    error_log("update_digital_file_file_types: Verified file type ID: $type_id");
                } else {
                    error_log("update_digital_file_file_types: Invalid file type ID: $type_id - not found in database");
                }
            }

            // Update file_type_ids to only include valid types
            $file_type_ids = $valid_types;
            error_log("update_digital_file_file_types: Valid file type IDs: " . json_encode($file_type_ids));
        } catch (Exception $e) {
            error_log("update_digital_file_file_types: Error verifying file types: " . $e->getMessage());
            return false;
        }
    }

    $pdo = get_db_connection();
    if (!$pdo) {
        error_log("update_digital_file_file_types: Failed to get database connection");
        return false;
    }

    try {
        // Only start a transaction if we're managing it
        $transaction_started = false;
        if ($manage_transaction && !$pdo->inTransaction()) {
            $pdo->beginTransaction();
            $transaction_started = true;
            error_log("update_digital_file_file_types: Started new transaction");
        } else {
            error_log("update_digital_file_file_types: Using existing transaction");
        }

        // Delete existing associations for this file
        $sql = "DELETE FROM digital_file_type_associations WHERE digital_file_id = :digital_file_id";
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([':digital_file_id' => $digital_file_id]);
        error_log("update_digital_file_file_types: Deleted existing associations for file ID $digital_file_id, result: " . ($result ? 'success' : 'failure'));

        if (!$result) {
            error_log("update_digital_file_file_types: Error deleting existing associations: " . json_encode($stmt->errorInfo()));
        }

        // Add new associations
        if (!empty($file_type_ids)) {
            error_log("update_digital_file_file_types: Adding " . count($file_type_ids) . " file type associations for file ID $digital_file_id");
            foreach ($file_type_ids as $file_type_id) {
                if (empty($file_type_id) || !is_numeric($file_type_id)) {
                    error_log("update_digital_file_file_types: Skipping invalid file type ID: " . print_r($file_type_id, true));
                    continue;
                }

                $sql = "INSERT INTO digital_file_type_associations (
                        digital_file_id, file_type_id, created_at
                    ) VALUES (
                        :digital_file_id, :file_type_id, datetime('now', 'localtime')
                    )";

                $params = [
                    ':digital_file_id' => $digital_file_id,
                    ':file_type_id' => $file_type_id
                ];

                $stmt = $pdo->prepare($sql);
                $result = $stmt->execute($params);

                if ($result) {
                    error_log("update_digital_file_file_types: Added association for file ID $digital_file_id, type ID $file_type_id");
                } else {
                    error_log("update_digital_file_file_types: Failed to add association for file ID $digital_file_id, type ID $file_type_id");
                    error_log("update_digital_file_file_types: Error info: " . json_encode($stmt->errorInfo()));
                }
            }
        } else {
            error_log("update_digital_file_file_types: No file type IDs provided for file ID $digital_file_id");
        }

        // Only commit if we started the transaction
        if ($transaction_started) {
            $pdo->commit();
            error_log("update_digital_file_file_types: Transaction committed");
        }

        // Verify the associations were saved
        $saved_types = get_digital_file_file_type_ids($digital_file_id);
        error_log("update_digital_file_file_types: Saved file type IDs: " . json_encode($saved_types));

        return true;
    } catch (Exception $e) {
        // Only roll back if we started the transaction
        if ($transaction_started && $pdo->inTransaction()) {
            $pdo->rollBack();
            error_log("update_digital_file_file_types: Transaction rolled back");
        }
        error_log("update_digital_file_file_types: Error updating file type associations: " . $e->getMessage());
        error_log("update_digital_file_file_types: Error trace: " . $e->getTraceAsString());

        // Re-throw the exception if we're not managing the transaction
        if (!$manage_transaction) {
            throw $e;
        }
        return false;
    }
}

function get_upload_error_message(int $error_code): string
{
    switch ($error_code) {
        case UPLOAD_ERR_INI_SIZE:
            return 'O arquivo excede o tamanho máximo permitido pelo servidor.';
        case UPLOAD_ERR_FORM_SIZE:
            return 'O arquivo excede o tamanho máximo permitido pelo formulário.';
        case UPLOAD_ERR_PARTIAL:
            return 'O arquivo foi apenas parcialmente carregado.';
        case UPLOAD_ERR_NO_FILE:
            return 'Nenhum arquivo foi carregado.';
        case UPLOAD_ERR_NO_TMP_DIR:
            return 'Pasta temporária não encontrada.';
        case UPLOAD_ERR_CANT_WRITE:
            return 'Falha ao gravar o arquivo no disco.';
        case UPLOAD_ERR_EXTENSION:
            return 'Uma extensão PHP interrompeu o upload do arquivo.';
        default:
            return 'Erro desconhecido ao carregar o arquivo.';
    }
}

function update_digital_product_file(int $digital_product_id, int $digital_file_id): bool
{
    if ($digital_product_id <= 0 || $digital_file_id <= 0) {
        return false;
    }

    $digital_file = get_digital_file_by_id($digital_file_id);
    if (!$digital_file) {
        return false;
    }

    $digital_product = db_query("SELECT * FROM digital_products WHERE id = :id", [':id' => $digital_product_id], true);
    if (!$digital_product) {
        return false;
    }

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        // Update the digital product to use the new file
        $sql = "UPDATE digital_products SET
                    digital_file_id = :digital_file_id,
                    updated_at = datetime('now', 'localtime')
                WHERE id = :id";

        $params = [
            ':id' => $digital_product_id,
            ':digital_file_id' => $digital_file_id
        ];

        $stmt = $pdo->prepare($sql);
        if (!$stmt->execute($params)) {
            throw new Exception("Failed to update digital product file");
        }

        // No need to update file type associations here as they are now directly associated with the digital file
        // The file type associations are stored in digital_file_type_associations table
        // and are accessed through the digital_file_id

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Error updating digital product file: " . $e->getMessage());
        return false;
    }
}
