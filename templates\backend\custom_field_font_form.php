<?php

$is_editing = isset($font) && !empty($font);
$form_title = $is_editing ? "Editar Fonte" : "Adicionar Nova Fonte";
$form_action = "admin.php";

$font_id = $is_editing ? $font['id'] : '';
$font_name = $is_editing ? $font['name'] : '';
$font_file_path = $is_editing ? $font['file_path'] : '';
$font_google_font_name = $is_editing ? $font['google_font_name'] : '';
$font_is_active = $is_editing ? $font['is_active'] : 1;

if (isset($form_data)) {
    $font_name = $form_data['name'] ?? $font_name;
    $font_google_font_name = $form_data['google_font_name'] ?? $font_google_font_name;
    $font_is_active = $form_data['is_active'] ?? $font_is_active;
}

$font_type = 'none';
if (!empty($font_file_path)) {
    $font_type = 'file';
} elseif (!empty($font_google_font_name)) {
    $font_type = 'google';
}

if (isset($form_data['font_type'])) {
    $font_type = $form_data['font_type'];
}
?>

<h1><?= $form_title ?></h1>

<div class="card">
    <div class="card-body">
        <form id="fontForm" method="post" action="<?= $form_action ?>" enctype="multipart/form-data">
            <input type="hidden" name="csrf_token" value="<?= generate_csrf_token() ?>">
            <input type="hidden" name="section" value="custom_field_fonts">
            <input type="hidden" name="action" value="<?= $is_editing ? 'update' : 'create' ?>">
            <?php if ($is_editing): ?>
                <input type="hidden" name="id" value="<?= $font_id ?>">
            <?php endif; ?>

            <div class="mb-3">
                <label for="name" class="form-label">Nome da Fonte *</label>
                <input type="text" class="form-control" id="name" name="name" value="<?= sanitize_input($font_name) ?>" required>
                <div class="form-text">Nome para identificar esta fonte no backoffice e frontend.</div>
            </div>

            <div class="mb-3">
                <label class="form-label">Tipo de Fonte *</label>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="font_type" id="fontTypeGoogle" value="google" <?= $font_type === 'google' ? 'checked' : '' ?>>
                    <label class="form-check-label" for="fontTypeGoogle">
                        Google Fonts
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="font_type" id="fontTypeFile" value="file" <?= $font_type === 'file' ? 'checked' : '' ?>>
                    <label class="form-check-label" for="fontTypeFile">
                        Carregar Ficheiro de Fonte
                    </label>
                </div>
            </div>

            <div id="googleFontSection" class="mb-3" style="display: <?= $font_type === 'google' ? 'block' : 'none' ?>;">
                <label for="google_font_name" class="form-label">Nome da Fonte no Google Fonts *</label>
                <input type="text" class="form-control" id="google_font_name" name="google_font_name" value="<?= sanitize_input($font_google_font_name) ?>">
                <div class="form-text">
                    Introduza o nome exato da fonte no Google Fonts (ex: "Roboto", "Open Sans", "Lato").
                    <a href="https://fonts.google.com/" target="_blank">Ver fontes disponíveis</a>
                </div>
                <div class="mt-2">
                    <button type="button" id="previewGoogleFont" class="btn btn-sm btn-outline-primary">Pré-visualizar Fonte</button>
                </div>
                <div id="googleFontPreview" class="mt-2 p-3 border rounded" style="display: none;">
                    <p style="font-size: 18px;">Exemplo de texto com a fonte <span id="previewFontName"></span></p>
                    <p style="font-size: 14px;">ABCDEFGHIJKLMNOPQRSTUVWXYZ<br>abcdefghijklmnopqrstuvwxyz<br>0123456789</p>
                </div>
            </div>

            <div id="fontFileSection" class="mb-3" style="display: <?= $font_type === 'file' ? 'block' : 'none' ?>;">
                <?php if ($is_editing && !empty($font_file_path)): ?>
                    <div class="alert alert-info">
                        <strong>Ficheiro atual:</strong> <?= basename($font_file_path) ?>
                    </div>
                <?php endif; ?>

                <label for="font_file" class="form-label">Ficheiro de Fonte <?= ($is_editing && !empty($font_file_path)) ? '(Opcional)' : '*' ?></label>
                <input type="file" class="form-control" id="font_file" name="font_file" accept=".ttf,.otf,.woff,.woff2">
                <div class="form-text">Formatos suportados: TTF, OTF, WOFF, WOFF2. Tamanho máximo: 2MB.</div>
            </div>

            <div class="mb-3">
                <label for="is_active" class="form-label">Estado</label>
                <select class="form-select" id="is_active" name="is_active">
                    <option value="1" <?= $font_is_active == 1 ? 'selected' : '' ?>>Ativo</option>
                    <option value="0" <?= $font_is_active == 0 ? 'selected' : '' ?>>Inativo</option>
                </select>
            </div>

            <div class="mt-4">
                <button type="submit" class="btn btn-primary">Guardar</button>
                <a href="admin.php?section=custom_field_fonts&<?= get_session_id_param() ?>" class="btn btn-secondary ms-2">Cancelar</a>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fontTypeGoogle = document.getElementById('fontTypeGoogle');
    const fontTypeFile = document.getElementById('fontTypeFile');
    const googleFontSection = document.getElementById('googleFontSection');
    const fontFileSection = document.getElementById('fontFileSection');
    const googleFontNameInput = document.getElementById('google_font_name');
    const previewGoogleFontBtn = document.getElementById('previewGoogleFont');
    const googleFontPreview = document.getElementById('googleFontPreview');
    const previewFontName = document.getElementById('previewFontName');

    // Function to toggle font type sections
    function toggleFontTypeSections() {
        if (fontTypeGoogle.checked) {
            googleFontSection.style.display = 'block';
            fontFileSection.style.display = 'none';
        } else if (fontTypeFile.checked) {
            googleFontSection.style.display = 'none';
            fontFileSection.style.display = 'block';
        }
    }

    // Toggle sections on radio button change
    fontTypeGoogle.addEventListener('change', toggleFontTypeSections);
    fontTypeFile.addEventListener('change', toggleFontTypeSections);

    // Preview Google Font
    previewGoogleFontBtn.addEventListener('click', function() {
        const fontName = googleFontNameInput.value.trim();
        if (!fontName) {
            alert('Por favor, introduza o nome da fonte.');
            return;
        }

        // Load the font
        const fontLink = document.createElement('link');
        fontLink.rel = 'stylesheet';
        fontLink.href = `https://fonts.googleapis.com/css2?family=${fontName.replace(/ /g, '+')}:wght@400;700&display=swap`;
        document.head.appendChild(fontLink);

        // Update preview
        previewFontName.textContent = fontName;
        googleFontPreview.style.fontFamily = `"${fontName}", sans-serif`;
        googleFontPreview.style.display = 'block';
    });

    // Form validation
    document.getElementById('fontForm').addEventListener('submit', function(e) {
        if (fontTypeGoogle.checked && !googleFontNameInput.value.trim()) {
            e.preventDefault();
            alert('Por favor, introduza o nome da fonte do Google Fonts.');
            googleFontNameInput.focus();
        } else if (fontTypeFile.checked) {
            const fontFileInput = document.getElementById('font_file');
            const isEditing = <?= $is_editing ? 'true' : 'false' ?>;
            const hasExistingFile = <?= !empty($font_file_path) ? 'true' : 'false' ?>;

            if (!isEditing || !hasExistingFile) {
                if (!fontFileInput.files || fontFileInput.files.length === 0) {
                    e.preventDefault();
                    alert('Por favor, selecione um ficheiro de fonte.');
                }
            }
        }
    });
});
</script>
