

(function() {
    
    document.addEventListener('click', function(event) {
        
        
        if (event.target.closest('.note-editor') !== null ||
            event.target.closest('.summernote-dropdown') !== null ||
            event.target.closest('.note-dropdown-menu') !== null ||
            event.target.closest('.note-btn-group') !== null ||
            event.target.classList.contains('summernote-dropdown-toggle') ||
            event.target.closest('.summernote-dropdown-toggle') !== null) {
            return;
        }

        
        const keepOpenDropdowns = document.querySelectorAll('.dropdown-menu.keep-open');

        keepOpenDropdowns.forEach(function(dropdown) {
            
            if (dropdown.classList.contains('summernote-dropdown') ||
                dropdown.closest('.note-editor') !== null ||
                dropdown.classList.contains('note-dropdown-menu')) {
                return;
            }

            
            if (dropdown.contains(event.target)) {
                
                if (event.target.classList.contains('icon-btn') ||
                    event.target.closest('.icon-btn') ||
                    event.target.classList.contains('nav-link')) {
                    
                    return;
                }

                
                event.stopPropagation();
            }
        });
    }, true);

    
    document.addEventListener('hidden.bs.dropdown', function(event) {
        
        if (event.target.closest('.note-editor') !== null) {
            return;
        }

        
        const toggleButton = event.target;
        let dropdownMenu = null;

        
        if (toggleButton.nextElementSibling && toggleButton.nextElementSibling.classList.contains('dropdown-menu')) {
            dropdownMenu = toggleButton.nextElementSibling;
        } else if (toggleButton.parentNode && toggleButton.parentNode.classList.contains('input-group')) {
            
            dropdownMenu = toggleButton.parentNode.querySelector('.dropdown-menu.icon-selector-dropdown');
        }
        

        if (dropdownMenu) {
            dropdownMenu.classList.remove('keep-open');
        }
    });
})();
