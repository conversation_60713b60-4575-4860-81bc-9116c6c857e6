<?php

/**
 * Handler for file types AJAX requests
 */

if (!defined('ADMIN_AJAX_HANDLER')) {
    die('Direct access not allowed');
}

// Handle get file types request
if ($action === 'get_file_types' && isset($_GET['file_id'])) {
    require_once __DIR__ . '/digital_files_functions.php';
    
    $file_id = (int)$_GET['file_id'];
    if ($file_id <= 0) {
        echo '<span class="text-muted">ID de arquivo inválido</span>';
        exit;
    }
    
    try {
        // Get file types for this file
        $file_types = get_digital_file_file_types($file_id);
        
        if (!empty($file_types)) {
            foreach ($file_types as $type) {
                echo '<span class="badge bg-info text-dark">' . sanitize_input($type['extension']) . '</span> ';
            }
        } else {
            echo '<span class="text-muted">Nenhum tipo definido</span>';
        }
    } catch (Exception $e) {
        echo '<span class="text-danger">Erro ao obter tipos de arquivo</span>';
    }
    exit;
}
