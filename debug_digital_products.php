<?php

require_once 'includes/db.php';
require_once 'includes/digital_files_functions.php';

$products = get_all_digital_products();

echo "Digital Products Count: " . count($products) . PHP_EOL;

if (count($products) > 0) {
    echo "First product: " . print_r($products[0], true) . PHP_EOL;
} else {
    echo "No digital products found." . PHP_EOL;
    
    
    echo "Debugging SQL query:" . PHP_EOL;
    
    
    $pdo = get_db_connection();
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM products WHERE product_type = 'digital'");
    $result = $stmt->fetch();
    echo "Digital products in products table: " . $result['count'] . PHP_EOL;
    
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM digital_products");
    $result = $stmt->fetch();
    echo "Records in digital_products table: " . $result['count'] . PHP_EOL;
    
    
    $stmt = $pdo->query("SELECT p.id, p.name_pt, dp.id as dp_id 
                         FROM products p 
                         JOIN digital_products dp ON p.id = dp.product_id 
                         WHERE p.product_type = 'digital' 
                         LIMIT 5");
    $results = $stmt->fetchAll();
    echo "Join results: " . print_r($results, true) . PHP_EOL;
}
