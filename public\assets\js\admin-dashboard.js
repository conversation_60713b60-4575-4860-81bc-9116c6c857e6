

(function() {
    'use strict';

    
    window.dashboardInitialized = false;

    
    window.initDashboardButtons = function() {

        
        document.querySelectorAll('.widget-collapse-icon').forEach(button => {

            
            button.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();

                
                const widget = this.closest('.dashboard-widget');
                if (!widget) {
                    console.error('Could not find parent widget');
                    return false;
                }

                const body = widget.querySelector('.widget-body');
                if (!body) {
                    console.error('Could not find widget body');
                    return false;
                }

                
                if (body.style.display === 'none') {
                    body.style.display = 'block';
                    
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.className = 'bi bi-chevron-up';
                    }
                } else {
                    body.style.display = 'none';
                    
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.className = 'bi bi-chevron-down';
                    }
                }

                return false;
            };
        });

        
        document.querySelectorAll('.refresh-widget').forEach(button => {

            
            button.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();

                const widgetId = this.getAttribute('data-widget');

                
                const widget = document.getElementById(widgetId);
                if (widget) {
                    const widgetBody = widget.querySelector('.widget-body');
                    if (widgetBody) {
                        widgetBody.innerHTML = '<div class="text-center p-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">A carregar...</span></div></div>';

                        
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    }
                }

                return false;
            };
        });
    };

    
    window.initDashboard = function() {
        
        if (!window.dashboardInitialized) {
            initDashboardCore();
            window.dashboardInitialized = true;
        } else {
            refreshCharts();
        }

        
        window.initDashboardButtons();
    };

    
    function initDashboardCore() {
        initCharts();
        initCollapsibleWidgets();
        initDateRangeSelector();
        initQuickActions();
        setupRefreshButtons();
    }

    
    function initCharts() {
        initSalesChart();
        initOrderStatusChart();
        initProductCategoryChart();
    }

    
    function refreshCharts() {
        
        if (window.salesChart) window.salesChart.destroy();
        if (window.orderStatusChart) window.orderStatusChart.destroy();
        if (window.productCategoryChart) window.productCategoryChart.destroy();

        
        initCharts();
    }

    
    function initSalesChart() {
        const ctx = document.getElementById('salesChart');
        if (!ctx) return;

        // Destroy existing chart instance if it exists
        if (window.salesChart instanceof Chart) {
            window.salesChart.destroy();
        }
        
        const salesData = JSON.parse(ctx.getAttribute('data-sales') || '[]');
        if (!salesData.length) return;

        
        const labels = salesData.map(item => item.date);
        const data = salesData.map(item => item.amount);

        
        window.salesChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Vendas',
                    data: data,
                    backgroundColor: 'rgba(13, 110, 253, 0.3)', 
                    borderColor: 'rgba(13, 110, 253, 1)', 
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false,
                        labels: {
                            color: '#f8f9fa' 
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return new Intl.NumberFormat('pt-PT', {
                                    style: 'currency',
                                    currency: 'EUR'
                                }).format(context.raw);
                            }
                        },
                        backgroundColor: 'rgba(33, 37, 41, 0.9)', 
                        titleColor: '#f8f9fa', 
                        bodyColor: '#e9ecef' 
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)' 
                        },
                        ticks: {
                            color: '#adb5bd', 
                            callback: function(value) {
                                return new Intl.NumberFormat('pt-PT', {
                                    style: 'currency',
                                    currency: 'EUR',
                                    maximumFractionDigits: 0
                                }).format(value);
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)' 
                        },
                        ticks: {
                            color: '#adb5bd' 
                        }
                    }
                }
            }
        });
    }

    
    function initOrderStatusChart() {
        const ctx = document.getElementById('orderStatusChart');
        if (!ctx) return;

        // Destroy existing chart instance if it exists
        if (window.orderStatusChart instanceof Chart) {
            window.orderStatusChart.destroy();
        }
        
        const statusData = JSON.parse(ctx.getAttribute('data-status') || '[]');
        if (!statusData.length) return;

        
        const labels = statusData.map(item => item.status);
        const data = statusData.map(item => item.count);

        
        const backgroundColors = [
            'rgba(255, 193, 7, 0.9)',
            'rgba(13, 110, 253, 0.9)',
            'rgba(25, 135, 84, 0.9)',
            'rgba(220, 53, 69, 0.9)',
            'rgba(173, 181, 189, 0.9)'
        ];

        
        window.orderStatusChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: backgroundColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            color: '#f8f9fa', 
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(33, 37, 41, 0.9)', 
                        titleColor: '#f8f9fa', 
                        bodyColor: '#e9ecef' 
                    }
                }
            }
        });
    }

    
    function initProductCategoryChart() {
        const ctx = document.getElementById('productCategoryChart');
        if (!ctx) return;

        // Destroy existing chart instance if it exists
        if (window.productCategoryChart instanceof Chart) {
            window.productCategoryChart.destroy();
        }
        
        const categoryData = JSON.parse(ctx.getAttribute('data-categories') || '[]');
        if (!categoryData.length) return;

        
        const labels = categoryData.map(item => item.name);
        const data = categoryData.map(item => item.count);

        
        window.productCategoryChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Produtos',
                    data: data,
                    backgroundColor: 'rgba(32, 201, 151, 0.8)', 
                    borderColor: 'rgba(32, 201, 151, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(33, 37, 41, 0.9)', 
                        titleColor: '#f8f9fa', 
                        bodyColor: '#e9ecef' 
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)' 
                        },
                        ticks: {
                            precision: 0,
                            color: '#adb5bd' 
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)' 
                        },
                        ticks: {
                            color: '#adb5bd' 
                        }
                    }
                }
            }
        });
    }

    
    function initCollapsibleWidgets() {

        
        const collapseButtons = document.querySelectorAll('.widget-collapse-icon');

        collapseButtons.forEach(button => {
            
            const newButton = button.cloneNode(true);
            if (button.parentNode) {
                button.parentNode.replaceChild(newButton, button);
            }
        });

        
        document.querySelectorAll('.widget-collapse-icon').forEach(button => {

            
            button.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();

                
                const widget = this.closest('.dashboard-widget');
                if (!widget) {
                    console.error('Could not find parent widget');
                    return;
                }

                const body = widget.querySelector('.widget-body');
                if (!body) {
                    console.error('Could not find widget body');
                    return;
                }

                
                if (body.style.display === 'none') {
                    body.style.display = 'block';
                    
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.className = 'bi bi-chevron-up';
                    }
                } else {
                    body.style.display = 'none';
                    
                    const icon = this.querySelector('i');
                    if (icon) {
                        icon.className = 'bi bi-chevron-down';
                    }
                }

                return false; 
            };
        });

        
        setupRefreshButtons();
    }

    
    function initDateRangeSelector() {
        const selector = document.getElementById('dashboardDateRange');
        if (!selector) return;

        selector.addEventListener('change', function() {
            const form = this.closest('form');
            if (form) form.submit();
        });
    }

    
    function initQuickActions() {
        
    }

    
    function setupRefreshButtons() {

        
        const refreshButtons = document.querySelectorAll('.refresh-widget');

        refreshButtons.forEach(button => {
            
            const newButton = button.cloneNode(true);
            if (button.parentNode) {
                button.parentNode.replaceChild(newButton, button);
            }
        });

        
        document.querySelectorAll('.refresh-widget').forEach(button => {

            
            button.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();

                const widgetId = this.getAttribute('data-widget');
                refreshWidget(widgetId);

                return false; 
            };
        });
    }

    
    function refreshWidget(widgetId) {
        
        const widget = document.getElementById(widgetId);
        if (!widget) return;

        const widgetBody = widget.querySelector('.widget-body');
        if (widgetBody) {
            widgetBody.innerHTML = '<div class="text-center p-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">A carregar...</span></div></div>';
        }

        
        
        setTimeout(() => {
            
            window.location.reload();
        }, 1000);
    }

    
    document.addEventListener('DOMContentLoaded', function() {
        if (!window.dashboardInitialized) {
            initDashboardCore();
            window.dashboardInitialized = true;
        }

        
        window.initDashboardButtons();

        
        setupMutationObserver();
    });

    
    document.addEventListener('contentLoaded', function() {
        initDashboardCore();

        
        window.initDashboardButtons();
    });

    
    function setupMutationObserver() {

        
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    
                    let hasButtons = false;
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { 
                            if (node.querySelector('.widget-collapse-icon, .refresh-widget')) {
                                hasButtons = true;
                            }
                        }
                    });

                    
                    if (hasButtons) {
                        window.initDashboardButtons();
                    }
                }
            });
        });

        
        observer.observe(document.body, { childList: true, subtree: true });
    }
})();
