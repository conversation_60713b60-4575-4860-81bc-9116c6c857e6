<?php

if (!function_exists('send_email')) {
    require_once __DIR__ . '/functions.php';
}

if (!function_exists('send_email_wrapper')) {
    
    function send_email_wrapper(
        string $to_email,
        string $to_name,
        string $subject,
        string $body_html,
        string $body_plain = '',
        ?string $reply_to_email = null,
        ?string $reply_to_name = null,
        array $attachments = [] // Added attachments parameter
    ): bool {
        
        return send_email($to_email, $to_name, $subject, $body_html, $body_plain, $reply_to_email, $reply_to_name, $attachments); // Pass attachments
    }
}

if (!function_exists('send_digital_product_terms_email')) {
    function send_digital_product_terms_email(int $order_id): bool {
        error_log("[send_digital_product_terms_email] Called for order_id: " . $order_id);
        if (get_setting('digital_terms_email_enabled', '0') !== '1') {
            error_log("[send_digital_product_terms_email] Exiting: digital_terms_email_enabled is not '1'. Value: " . get_setting('digital_terms_email_enabled', '0'));
            return false; // Setting is disabled
        }

        $order = get_order_by_id($order_id);
        if (!$order) {
            error_log("[send_digital_product_terms_email] Exiting: Order not found for order_id: " . $order_id);
            return false;
        }
        if (!$order['has_digital_products']) {
            error_log("[send_digital_product_terms_email] Exiting: Order order_id: " . $order_id . " does not have digital products.");
            return false;
        }
        error_log("[send_digital_product_terms_email] Order has_digital_products: " . $order['has_digital_products']);


        $customer_info = json_decode($order['customer_info_json'] ?? '{}', true);
        $customer_email = $customer_info['customer_email'] ?? null;
        $customer_name = $customer_info['customer_name'] ?? 'Cliente';
        $customer_first_name = explode(' ', $customer_name)[0] ?? $customer_name;

        if (empty($customer_email)) {
            error_log("[send_digital_product_terms_email] Exiting: Customer email is empty for order_id: " . $order_id);
            return false;
        }
        error_log("[send_digital_product_terms_email] Customer email: " . $customer_email);

        $terms_log_json = $order['terms_log_json'] ?? null;
        if (empty($terms_log_json)) {
            error_log("[send_digital_product_terms_email] Exiting: terms_log_json is empty for order_id: " . $order_id);
            return false; // No terms agreed upon
        }
        error_log("[send_digital_product_terms_email] terms_log_json: " . $terms_log_json);

        $terms_log = json_decode($terms_log_json, true);
        $agreed_page_ids = $terms_log['agreed_pages'] ?? [];

        if (empty($agreed_page_ids)) {
            error_log("[send_digital_product_terms_email] Exiting: agreed_page_ids is empty after decoding terms_log_json for order_id: " . $order_id);
            return false; // No page IDs found in log
        }
        error_log("[send_digital_product_terms_email] Agreed page IDs: " . implode(', ', $agreed_page_ids));

        // Fetch admin-selected T&C pages
        $admin_selected_pages_json = get_setting('digital_terms_email_selected_pages', '[]');
        $admin_selected_page_ids = json_decode($admin_selected_pages_json, true);
        if (!is_array($admin_selected_page_ids)) {
            $admin_selected_page_ids = [];
        }
        // Ensure IDs are integers for comparison
        $admin_selected_page_ids = array_map('intval', $admin_selected_page_ids);
        error_log("[send_digital_product_terms_email] Admin selected page IDs for email: " . implode(', ', $admin_selected_page_ids));

        // Filter agreed_page_ids by admin selection
        $final_page_ids_to_send = array_intersect($agreed_page_ids, $admin_selected_page_ids);

        if (empty($final_page_ids_to_send)) {
            error_log("[send_digital_product_terms_email] Exiting: final_page_ids_to_send is empty after intersecting agreed and admin-selected pages for order_id: " . $order_id);
            error_log("[send_digital_product_terms_email] Agreed: " . implode(',', $agreed_page_ids) . " Admin selected: " . implode(',', $admin_selected_page_ids));
            return false; // No terms to send after filtering by admin selection
        }
        error_log("[send_digital_product_terms_email] Final page IDs to send: " . implode(', ', $final_page_ids_to_send));

        // $all_terms_content_plain = ""; // No longer needed to build a single string for body
        $email_attachments = [];
        $created_temp_files = [];

        foreach ($final_page_ids_to_send as $page_id) {
            $page_data = get_page_by_id((int)$page_id);
            if ($page_data && !empty($page_data['content_pt'])) {
                // Convert HTML to plain text while preserving line breaks
                $content_html = $page_data['content_pt'] ?? '';
                $text = $content_html;

                // Step 1: Basic structural tag replacements with newlines
                $text = preg_replace('/<br\s?\/?>/i', "\n", $text);
                $block_elements_double_nl = ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'div', 'blockquote', 'address', 'section', 'article', 'aside', 'header', 'footer', 'nav', 'main', 'figure', 'figcaption', 'hr', 'form', 'fieldset', 'pre'];
                foreach ($block_elements_double_nl as $tag) {
                    $text = preg_replace('/<' . $tag . '[^>]*>/i', "\n\n", $text);
                    $text = preg_replace('/<\/' . $tag . '>/i', "\n\n", $text);
                }
                $text = preg_replace('/<ul[^>]*>/i', "\n\n", $text);
                $text = preg_replace('/<\/ul>/i', "\n\n", $text);
                $text = preg_replace('/<ol[^>]*>/i', "\n\n", $text);
                $text = preg_replace('/<\/ol>/i', "\n\n", $text);
                $text = preg_replace('/<li[^>]*>/i', "\n* ", $text);
                $text = preg_replace('/<\/li>/i', "\n", $text);
                $text = preg_replace('/<table[^>]*>/i', "\n\n", $text);
                $text = preg_replace('/<\/table>/i', "\n\n", $text);
                $text = preg_replace('/<tr[^>]*>/i', "\n", $text);
                $text = preg_replace('/<\/tr>/i', "\n", $text);
                $text = preg_replace('/<(td|th)[^>]*>/i', "\t", $text);
                $text = preg_replace('/<\/(td|th)>/i', "", $text);
                $text = strip_tags($text);
                $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                $text = str_replace("\xC2\xA0", " ", $text);
                $text = preg_replace('/[ \t]+/', ' ', $text);
                $text = preg_replace('/^[ \t]*(.*?)[ \t]*$/m', '$1', $text);
                $text = preg_replace('/\n{3,}/s', "\n\n", $text);
                $text = trim($text);
                $trimmed_plain_text_content = $text;
                
                error_log("[send_digital_product_terms_email] Processing page_id: " . $page_id . ", Title: " . sanitize_input($page_data['title_pt']));
                // error_log("[send_digital_product_terms_email] Raw content_pt for page_id " . $page_id . ": " . $page_data['content_pt']);
                // error_log("[send_digital_product_terms_email] Intermediate plain_text_content for page_id " . $page_id . " (length " . strlen($text) . "): '" . $text . "'");
                error_log("[send_digital_product_terms_email] Trimmed plain_text_content for page_id " . $page_id . " (length " . strlen($trimmed_plain_text_content) . ")");

                // Create attachment
                $sanitized_page_title = preg_replace('/[^a-zA-Z0-9_-]/', '_', $page_data['title_pt']);
                $filename = $sanitized_page_title . ".txt";
                // Ensure temp dir is writable or use a project-specific one
                $temp_file_path = sys_get_temp_dir() . DIRECTORY_SEPARATOR . uniqid('terms_') . '_' . $filename;
                
                if (file_put_contents($temp_file_path, $trimmed_plain_text_content) !== false) {
                    $email_attachments[] = ['path' => $temp_file_path, 'name' => $filename];
                    $created_temp_files[] = $temp_file_path; // Keep track for cleanup
                    error_log("[send_digital_product_terms_email] Created temp file for attachment: " . $temp_file_path . " with name " . $filename);
                } else {
                    error_log("[send_digital_product_terms_email] Failed to create temp file for attachment: " . $temp_file_path);
                }
                
            } else {
                error_log("[send_digital_product_terms_email] Skipping page_id: " . $page_id . " because page_data is invalid or content_pt is empty.");
            }
        }

        if (empty($email_attachments)) { // Check if any attachments were actually created
            error_log("[send_digital_product_terms_email] Exiting: No attachments created after processing pages for order_id: " . $order_id);
            // Clean up any temp files that might have been created if some failed later
            foreach ($created_temp_files as $file_path) {
                if (file_exists($file_path)) {
                    unlink($file_path);
                }
            }
            return false;
        }
        error_log("[send_digital_product_terms_email] Successfully generated " . count($email_attachments) . " attachments for terms.");

        $store_name = get_setting('store_name', 'A Nossa Loja');
        $email_subject_template = get_setting('digital_terms_email_subject', 'Termos e Condições da sua Encomenda Digital #{order_number}');
        
        $subject_replacements = [
            '{customer_first_name}' => $customer_first_name,
            '{customer_last_name}' => $customer_info['last_name'] ?? '',
            '{order_number}' => $order['order_ref'],
            '{store_name}' => $store_name
        ];
        $email_subject = str_replace(array_keys($subject_replacements), array_values($subject_replacements), $email_subject_template);

        $user_ip = $order['ip_address'] ?? 'N/A';
        $order_date_formatted = format_date($order['created_at'], 'd/m/Y \à\s H:i:s');

        $footer_note_plain = "\n\n------------------------------------------------------\n";
        $footer_note_plain .= "Os termos de uso, enviados em anexo, foram aceites pelo utilizador " . sanitize_input($customer_name) . " ";
        $footer_note_plain .= "com email " . sanitize_input($customer_email) . " no dia " . $order_date_formatted . " ";
        $footer_note_plain .= "com IP: " . sanitize_input($user_ip) . ".";
        $footer_note_plain .= "\n------------------------------------------------------\n";

        $email_body_plain = "Termos e Condições Acordados\n";
        $email_body_plain .= "=========\n\n";
        $email_body_plain .= "Olá " . sanitize_input($customer_first_name) . ",\n";
        $email_body_plain .= "  conforme os registos online, durante a finalização da sua encomenda com produtos digitais de entrega imediata por meios imateriais #" . sanitize_input($order['order_ref']) . ", concordou com os termos e condições que se encontram em anexo neste email.\n";
        $email_body_plain .= "  Serve este email apenas para lhe ceder uma cópia dos mesmos e não requer qualquer resposta.\n";
        // $email_body_plain .= $all_terms_content_plain; // Removed, content is now in attachments
        $email_body_plain .= $footer_note_plain;
        $email_body_plain .= "\nCom os melhores cumprimentos,\n" . $store_name . "\n";
        
        $simple_html_version = nl2br($email_body_plain);

        error_log("[send_digital_product_terms_email] Attempting to send email to: " . $customer_email . " with subject: " . $email_subject . " and " . count($email_attachments) . " attachments.");
        $email_sent_status = send_email_wrapper(
            $customer_email,
            $customer_name,
            $email_subject,
            $simple_html_version,
            $email_body_plain,
            null, // reply_to_email
            null, // reply_to_name
            $email_attachments // Pass attachments array
        );

        // Cleanup temporary files
        foreach ($created_temp_files as $file_path) {
            if (file_exists($file_path)) {
                if (unlink($file_path)) {
                    error_log("[send_digital_product_terms_email] Successfully deleted temp file: " . $file_path);
                } else {
                    error_log("[send_digital_product_terms_email] Failed to delete temp file: " . $file_path);
                }
            }
        }

        if (!$email_sent_status) {
            error_log("[send_digital_product_terms_email] send_email_wrapper returned false for order_id: " . $order_id . " to " . $customer_email);
        } else {
            error_log("[send_digital_product_terms_email] send_email_wrapper returned true for order_id: " . $order_id . " to " . $customer_email);
        }
        return $email_sent_status;
    }
}
