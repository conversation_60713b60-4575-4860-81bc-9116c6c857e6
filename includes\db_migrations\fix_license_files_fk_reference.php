<?php

function migrate_fix_license_files_fk_reference(PDO $pdo): bool
{
    $migration_name = 'fix_license_files_fk_reference';
    error_log("Running migration: {$migration_name}");

    try {
        // Check if this migration has already run
        $check_sql = "SELECT 1 FROM migrations WHERE name = :name";
        $stmt_check = $pdo->prepare($check_sql);
        $stmt_check->execute([':name' => $migration_name]);
        if ($stmt_check->fetch()) {
            error_log("Migration {$migration_name} has already been applied.");
            return true; // Migration already applied
        }

        $pdo->exec('PRAGMA foreign_keys=OFF;');
        $pdo->beginTransaction();

        // 1. Create the new table with the correct schema
        $pdo->exec("
            CREATE TABLE license_files_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                license_id INTEGER NOT NULL,
                digital_product_id INTEGER NOT NULL,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE,
                FOREIGN KEY (digital_product_id) REFERENCES digital_products(id) ON DELETE CASCADE
            );
        ");
        error_log("{$migration_name}: Table license_files_new created.");

        // 2. Copy data from the old table to the new table
        // Ensure column names match exactly if they differ, though here they are the same
        $pdo->exec("INSERT INTO license_files_new (id, license_id, digital_product_id, created_at)
                      SELECT id, license_id, digital_product_id, created_at FROM license_files;");
        error_log("{$migration_name}: Data copied from license_files to license_files_new.");

        // 3. Drop the old table
        $pdo->exec("DROP TABLE license_files;");
        error_log("{$migration_name}: Old table license_files dropped.");

        // 4. Rename the new table to the original table name
        $pdo->exec("ALTER TABLE license_files_new RENAME TO license_files;");
        error_log("{$migration_name}: Table license_files_new renamed to license_files.");

        $pdo->commit();
        $pdo->exec('PRAGMA foreign_keys=ON;');
        error_log("{$migration_name}: Foreign keys re-enabled.");

        // Record migration
        $stmt_insert_migration = $pdo->prepare("INSERT INTO migrations (name, executed_at) VALUES (:name, datetime('now'))");
        $stmt_insert_migration->execute([':name' => $migration_name]);
        
        error_log("Migration {$migration_name} completed successfully.");
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Migration {$migration_name} failed: " . $e->getMessage());
        $pdo->exec('PRAGMA foreign_keys=ON;'); // Re-enable FKs on failure too
        return false;
    }
}

// If run directly, execute the migration
if (basename(__FILE__) == basename($_SERVER["SCRIPT_FILENAME"])) {
    require_once __DIR__ . '/../db.php'; // Adjust path as necessary to get $pdo
    $pdo = get_db_connection();
    if ($pdo) {
        // Ensure migrations table exists
        $pdo->exec("CREATE TABLE IF NOT EXISTS migrations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            executed_at TEXT NOT NULL
        )");
        
        if (migrate_fix_license_files_fk_reference($pdo)) {
            echo "Migration fix_license_files_fk_reference executed successfully.\n";
        } else {
            echo "Migration fix_license_files_fk_reference failed.\n";
        }
    } else {
        echo "Failed to connect to the database.\n";
    }
}
?>