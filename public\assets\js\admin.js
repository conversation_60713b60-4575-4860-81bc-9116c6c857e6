document.addEventListener('DOMContentLoaded', () => {
    function generateSlug(text) {
        if (!text) return '';
        return text.toString().toLowerCase()
            .normalize('NFD')
            .replace(/[\u0300-\u036f]/g, '')
            .replace(/\s+/g, '-')
            .replace(/[^\w\-]+/g, '')
            .replace(/\-\-+/g, '-')
            .replace(/^-+/, '')
            .replace(/-+$/, '');
    }

    function setupSlugGeneration(sourceInputId, slugInputId) {
        const sourceInput = document.getElementById(sourceInputId);
        const slugInput = document.getElementById(slugInputId);

        if (sourceInput && slugInput && !slugInput.readOnly) {
            const updateSlug = () => {
                slugInput.value = generateSlug(sourceInput.value);
            };

            sourceInput.addEventListener('input', updateSlug);
            sourceInput.addEventListener('change', updateSlug);
        }
    }

    function initPageSpecificFeatures() {
        const urlParams = new URLSearchParams(window.location.search);
        const section = urlParams.get('section');
        const action = urlParams.get('action');

        if (section === 'pages' && (action === 'new' || action === 'edit')) {
            setupSlugGeneration('title_pt', 'slug');

            const titleInput = document.getElementById('title_pt');
            if (titleInput) {
                titleInput.addEventListener('input', function() {
                    const slugInput = document.getElementById('slug');
                    if (slugInput && !slugInput.readOnly) {
                        slugInput.value = generateSlug(this.value);
                    }
                });
            }
        } else if (section === 'products' && (action === 'new' || action === 'edit')) {
            setupSlugGeneration('name_pt', 'slug');
        } else {
            setupSlugGeneration('name_pt', 'slug');
            setupSlugGeneration('title_pt', 'slug');
        }

        initConfirmationDialogs();
    }

    function initConfirmationDialogs() {
        const confirmationForms = document.querySelectorAll('.needs-confirmation');
        confirmationForms.forEach(form => {
            form.addEventListener('submit', function(event) {
                const message = this.getAttribute('data-confirm-message') || 'Tem a certeza que deseja executar esta ação?';
                if (!confirm(message)) {
                    event.preventDefault();
                }
            });
        });
    }

    initPageSpecificFeatures();
    window.initPageSpecificFeatures = initPageSpecificFeatures;

    const messageModal = document.getElementById('viewMessageModal');
    if (messageModal) {
        messageModal.addEventListener('show.bs.modal', event => {
            const button = event.relatedTarget;

            const messageId = button.getAttribute('data-message-id');
            const messageFrom = button.getAttribute('data-message-from');
            const messageSubject = button.getAttribute('data-message-subject');
            const messageDate = button.getAttribute('data-message-date');
            const messageBody = button.getAttribute('data-message-body');

            const modalFrom = messageModal.querySelector('#modal-message-from');
            const modalSubject = messageModal.querySelector('#modal-message-subject');
            const modalDate = messageModal.querySelector('#modal-message-date');
            const modalBody = messageModal.querySelector('#modal-message-body');
            const modalMessageIdInput = messageModal.querySelector('#modal-message-id');
            const modalReplyTextarea = messageModal.querySelector('#reply_message_body');
            const modalReplyStatus = messageModal.querySelector('#reply-status');

            if (modalFrom) modalFrom.textContent = messageFrom || 'N/A';
            if (modalSubject) modalSubject.textContent = messageSubject || 'N/A';
            if (modalDate) modalDate.textContent = messageDate || 'N/A';
            if (modalBody) modalBody.innerHTML = messageBody ? messageBody.replace(/\n/g, '<br>') : 'Mensagem não disponível.';
            if (modalMessageIdInput) modalMessageIdInput.value = messageId || '';
            if (modalReplyTextarea) modalReplyTextarea.value = '';
            if (modalReplyStatus) {
                modalReplyStatus.textContent = '';
                modalReplyStatus.className = 'mt-3';
            }
        });
    }

    const replyForm = document.getElementById('reply-message-form');
    if(replyForm) {
        replyForm.addEventListener('submit', async (event) => {
            event.preventDefault();
            const formData = new FormData(replyForm);
            const replyStatus = document.getElementById('reply-status');
            replyStatus.textContent = 'Enviando...';

            try {
                const response = await fetch('admin.php', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    replyStatus.textContent = result.message || 'Resposta enviada com sucesso!';
                    replyStatus.className = 'alert alert-success mt-3';

                    const replyTextarea = replyForm.querySelector('#reply_message_body');
                    if (replyTextarea) {
                        replyTextarea.value = '';
                    }
                } else {
                    replyStatus.textContent = 'Erro ao enviar resposta: ' + (result.message || 'Erro desconhecido');
                    replyStatus.className = 'alert alert-danger mt-3';
                }
            } catch (error) {
                replyStatus.textContent = 'Erro de comunicação ao enviar resposta.';
                replyStatus.className = 'alert alert-danger';
            }

            initConfirmationDialogs();
        });
    }
});
