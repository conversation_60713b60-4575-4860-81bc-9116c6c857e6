<?php

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/payment_methods.php';

ensure_payment_methods_table_exists();

$payment_methods = get_payment_methods();

echo "Payment methods table check completed.\n";
echo "Number of payment methods found: " . count($payment_methods) . "\n";

if (count($payment_methods) > 0) {
    echo "Existing payment methods:\n";
    foreach ($payment_methods as $method) {
        echo "- ID: {$method['id']}, Title: {$method['title']}, Active: {$method['is_active']}\n";
    }
} else {
    echo "No payment methods found. Adding a default one...\n";
    
    $data = [
        'title' => 'Transferência Bancária',
        'instructions' => "Por favor, transfira o valor total da sua encomenda para a seguinte conta bancária:\n\nBanco: [Nome do Banco]\nIBAN: [Número IBAN]\nBIC/SWIFT: [Código BIC/SWIFT]\nTitular: [Nome do Titular]\n\nPor favor, inclua o número da sua encomenda na descrição da transferência.",
        'is_active' => 1,
        'sort_order' => 0
    ];
    
    $new_id = add_payment_method($data);
    if ($new_id) {
        echo "Default payment method added with ID: $new_id\n";
    } else {
        echo "Failed to add default payment method.\n";
    }
}

echo "\nDone.\n";
