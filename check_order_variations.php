<?php

require_once 'includes/db.php';
require_once 'includes/functions.php';
require_once 'includes/product_functions.php';

$pdo = get_db_connection();

$stmt = $pdo->query('SELECT COUNT(*) as count FROM orders');
$result = $stmt->fetch(PDO::FETCH_ASSOC);
echo 'Number of orders: ' . $result['count'] . PHP_EOL;

$stmt = $pdo->query('
    SELECT oi.id, oi.order_id, oi.variation_id, oi.product_details_json, o.order_ref
    FROM order_items oi
    JOIN orders o ON oi.order_id = o.id
    WHERE oi.variation_id IS NOT NULL
    LIMIT 5
');

$items = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($items)) {
    echo 'No order items with variations found.' . PHP_EOL;
} else {
    echo 'Found ' . count($items) . ' order items with variations:' . PHP_EOL;
    
    foreach ($items as $item) {
        echo '------------------------------' . PHP_EOL;
        echo 'Order Ref: ' . $item['order_ref'] . PHP_EOL;
        echo 'Order Item ID: ' . $item['id'] . PHP_EOL;
        echo 'Variation ID: ' . $item['variation_id'] . PHP_EOL;
        
        
        $attributes_display = get_variation_attribute_string($item['variation_id']);
        echo 'Variation Attributes: ' . ($attributes_display ?: 'None') . PHP_EOL;
        
        
        $details = json_decode($item['product_details_json'], true);
        echo 'Product Name: ' . ($details['name'] ?? 'N/A') . PHP_EOL;
        echo 'Attributes in JSON: ' . ($details['attributes'] ?? 'N/A') . PHP_EOL;
        echo 'Attributes Display in JSON: ' . ($details['attributes_display'] ?? 'N/A') . PHP_EOL;
    }
}
