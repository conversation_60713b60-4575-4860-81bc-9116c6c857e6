<?php
echo "Testing database connection...\n";
require_once 'includes/db.php';
$pdo = get_db_connection();
if ($pdo) {
    echo "Database connection successful.\n";
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Tables in database: " . implode(', ', $tables) . "\n";
} else {
    echo "Database connection failed.\n";
}
