<?php

/**
 * Migration to drop the digital_file_type_associations table
 * We'll use the existing digital_product_file_type_associations table instead
 */
function migrate_drop_digital_file_type_associations()
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        // Check if foreign keys are enabled
        $fk_enabled_before = $pdo->query("PRAGMA foreign_keys")->fetchColumn();
        
        // Temporarily disable foreign keys for the migration
        $pdo->exec("PRAGMA foreign_keys = OFF");
        
        $pdo->beginTransaction();

        // Drop the digital_file_type_associations table if it exists
        $pdo->exec("DROP TABLE IF EXISTS digital_file_type_associations");

        // Restore foreign key setting
        if ($fk_enabled_before) {
            $pdo->exec("PRAGMA foreign_keys = ON");
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Migration error (drop_digital_file_type_associations): " . $e->getMessage());
        return false;
    }
}
