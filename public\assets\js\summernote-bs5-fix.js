

(function() {
    
    function initFix() {
        if (typeof jQuery === 'undefined') {
            console.warn('jQuery not loaded yet. Will retry in 100ms.');
            setTimeout(initFix, 100);
            return;
        }

        const $ = jQuery;

        
        const style = document.createElement('style');
        style.textContent = `
            
            .note-btn-group.open .dropdown-menu,
            .note-btn-group.show .dropdown-menu,
            .note-btn-group.open .note-dropdown-menu,
            .note-btn-group.show .note-dropdown-menu {
                display: block !important;
                opacity: 1 !important;
                visibility: visible !important;
                transform: none !important;
                position: absolute !important;
                top: 100% !important;
                left: 0 !important;
                z-index: 1000 !important;
                margin-top: 0 !important;
            }

            
            .note-dropdown-menu {
                position: absolute !important;
                z-index: 1000 !important;
                display: none;
                min-width: 100px;
                padding: 5px;
                background-color: #212529 !important;
                border: 1px solid rgba(255,255,255,.15) !important;
                border-radius: 4px;
                box-shadow: 0 6px 12px rgba(0,0,0,.5);
                color: #fff !important;
            }

            
            .note-dropdown-item {
                display: block;
                width: 100%;
                padding: 3px 10px;
                clear: both;
                font-weight: 400;
                color: #fff !important;
                text-align: inherit;
                white-space: nowrap;
                background-color: transparent;
                border: 0;
                cursor: pointer;
            }

            .note-dropdown-item:hover,
            .note-dropdown-item:focus {
                color: #fff !important;
                background-color: #0d6efd !important;
                text-decoration: none;
            }

            
            .note-color .dropdown-menu {
                min-width: 340px;
            }

            .note-color .note-color-palette {
                margin: 0;
            }

            .note-color .note-color-palette .note-color-row {
                height: 20px;
            }

            .note-color .note-color-palette .note-color-btn {
                margin: 0;
                padding: 0;
                border: 1px solid #fff;
                width: 20px;
                height: 20px;
            }
        `;
        document.head.appendChild(style);

        
        $(document).off('.summernote-bs5-fix');

        
        $(document).on('click.summernote-bs5-fix', '.note-btn-group .dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const $parent = $(this).parent();
            const $menu = $parent.find('.dropdown-menu, .note-dropdown-menu');

            
            $('.note-btn-group.show, .note-btn-group.open').not($parent).removeClass('show open')
                .find('.dropdown-menu, .note-dropdown-menu').hide();

            
            if ($parent.hasClass('show') || $parent.hasClass('open')) {
                $parent.removeClass('show open');
                $menu.hide();
            } else {
                $parent.addClass('show open');

                
                if ($menu.length) {
                    $menu.css({
                        'display': 'block',
                        'position': 'absolute',
                        'top': '100%',
                        'left': '0',
                        'z-index': '1000'
                    });
                }
            }
        });

        
        $(document).on('click.summernote-bs5-fix-outside', function(e) {
            if (!$(e.target).closest('.note-btn-group').length) {
                $('.note-btn-group.show, .note-btn-group.open').removeClass('show open');
                $('.dropdown-menu, .note-dropdown-menu').hide();
            }
        });

        
        $(document).on('click.summernote-bs5-fix-menu', '.dropdown-menu, .note-dropdown-menu', function(e) {
            if ($(this).closest('.note-editor').length) {
                e.stopPropagation();
            }
        });

        
        $(document).on('click.summernote-bs5-fix-color', '.note-color .note-color-btn', function(e) {
            e.stopPropagation();

            
            const $this = $(this);
            const value = $this.data('value');

            
            const $dropdown = $this.closest('.note-btn-group');

            
            setTimeout(function() {
                $dropdown.removeClass('show open');
                $dropdown.find('.dropdown-menu, .note-dropdown-menu').hide();
            }, 100);
        });

        
        $(document).on('click.summernote-bs5-fix-fontsize', '.note-fontsize .dropdown-item', function(e) {
            e.stopPropagation();

            
            const $this = $(this);
            const value = $this.data('value');

            
            const $dropdown = $this.closest('.note-btn-group');

            
            setTimeout(function() {
                $dropdown.removeClass('show open');
                $dropdown.find('.dropdown-menu, .note-dropdown-menu').hide();
            }, 100);
        });

        
        $(document).on('click.summernote-bs5-fix-fontname', '.note-fontname .dropdown-item', function(e) {
            e.stopPropagation();

            
            const $this = $(this);
            const value = $this.data('value');

            
            const $dropdown = $this.closest('.note-btn-group');

            
            setTimeout(function() {
                $dropdown.removeClass('show open');
                $dropdown.find('.dropdown-menu, .note-dropdown-menu').hide();
            }, 100);
        });

        
        $(document).on('summernote.initialized', function(e, editor) {
            const $editor = $(editor);
            fixEditor($editor.closest('.note-editor'));
        });

        
        function fixEditor($editor) {
            if (!$editor.length) return;

            
            $editor.find('.note-btn-group .dropdown-toggle').each(function() {
                const $toggle = $(this);
                
                $toggle.removeAttr('data-bs-toggle');
                $toggle.removeAttr('data-bs-auto-close');
                $toggle.removeAttr('aria-expanded');
            });

            
            $editor.find('.dropdown-menu, .note-dropdown-menu').addClass('summernote-dropdown');
        }

        
        $('.note-editor').each(function() {
            fixEditor($(this));
        });

        
        const originalSummernote = $.fn.summernote;
        $.fn.summernote = function() {
            const result = originalSummernote.apply(this, arguments);

            
            setTimeout(() => {
                this.each(function() {
                    const $this = $(this);
                    fixEditor($this.next('.note-editor'));
                });
            }, 100);

            return result;
        };
    }

    
    initFix();
    document.addEventListener('DOMContentLoaded', initFix);

    
    setTimeout(initFix, 500);
    setTimeout(initFix, 1000);
})();
