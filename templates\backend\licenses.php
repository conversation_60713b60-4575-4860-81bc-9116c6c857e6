<?php

if (!defined('ADMIN_PAGE')) {
    exit('Direct access not permitted');
}

require_once __DIR__ . '/../../includes/digital_product_functions.php';

$action = $_GET['action'] ?? 'list';
$license_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$errors = [];
$success_message = '';
$license = [];

if ($action === 'edit' && $license_id > 0) {
    
    $license = get_license_by_id($license_id);
    if (!$license) {
        $errors[] = "Licença não encontrada.";
        $action = 'list';
    }
} elseif ($action === 'delete' && $license_id > 0) {
    
    if (delete_license($license_id)) {
        $success_message = "Licença eliminada com sucesso.";
    } else {
        $errors[] = "Erro ao eliminar a licença.";
    }
    $action = 'list';
} elseif ($action === 'revoke' && $license_id > 0) {
    
    if (update_license_status($license_id, 'revoked')) {
        $success_message = "Licença revogada com sucesso.";
    } else {
        $errors[] = "Erro ao revogar a licença.";
    }
    $action = 'list';
} elseif ($action === 'activate' && $license_id > 0) {
    
    if (update_license_status($license_id, 'active')) {
        $success_message = "Licença ativada com sucesso.";
    } else {
        $errors[] = "Erro ao ativar a licença.";
    }
    $action = 'list';
} elseif ($action === 'resend' && $license_id > 0) {
    
    if (resend_license_email($license_id)) {
        $success_message = "Email de licença reenviado com sucesso.";
    } else {
        $errors[] = "Erro ao reenviar o email de licença.";
    }
    $action = 'list';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && $action === 'edit') {
    
    $license_code = $_POST['license_code'] ?? '';
    $customer_name = $_POST['customer_name'] ?? '';
    $customer_email = $_POST['customer_email'] ?? '';
    $status = $_POST['status'] ?? '';
    $expiry_date = $_POST['expiry_date'] ?? '';
    $download_limit = (int)($_POST['download_limit'] ?? 3);
    
    
    if (empty($license_code)) $errors[] = "O código de licença é obrigatório.";
    if (empty($customer_name)) $errors[] = "O nome do cliente é obrigatório.";
    if (empty($customer_email) || !filter_var($customer_email, FILTER_VALIDATE_EMAIL)) $errors[] = "O email do cliente é inválido.";
    if (empty($status)) $errors[] = "O estado da licença é obrigatório.";
    if (empty($expiry_date)) $errors[] = "A data de expiração é obrigatória.";
    if ($download_limit < 1) $errors[] = "O limite de downloads deve ser pelo menos 1.";
    
    if (empty($errors)) {
        
        $license_data = [
            'license_code' => $license_code,
            'customer_name' => $customer_name,
            'customer_email' => $customer_email,
            'status' => $status,
            'expiry_date' => $expiry_date,
            'download_limit' => $download_limit,
        ];
        
        if (update_license($license_id, $license_data)) {
            $success_message = "Licença atualizada com sucesso.";
            $license = get_license_by_id($license_id); 
        } else {
            $errors[] = "Erro ao atualizar a licença.";
        }
    }
}

$licenses = [];
$search_term = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

$conditions = [];
$params = [];

if (!empty($search_term)) {
    $conditions[] = "(license_code LIKE ? OR customer_name LIKE ? OR customer_email LIKE ?)";
    $search_param = "%$search_term%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if (!empty($status_filter)) {
    $conditions[] = "status = ?";
    $params[] = $status_filter;
}

$where_clause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";

$count_query = "SELECT COUNT(*) as total FROM licenses $where_clause";
$total_result = db_query($count_query, $params, true);
$total_licenses = $total_result ? (int)$total_result['total'] : 0;
$total_pages = ceil($total_licenses / $per_page);

$query = "SELECT * FROM licenses $where_clause ORDER BY id DESC LIMIT $per_page OFFSET $offset";
$licenses = db_query($query, $params);

$status_options = [
    'waiting_payment' => 'Aguardando Pagamento',
    'active' => 'Ativa',
    'expired' => 'Expirada',
    'revoked' => 'Revogada'
];
?>

<!-- License Management Interface -->
<div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-semibold">Gestão de Licenças</h1>
    </div>
    
    <?php if (!empty($errors)): ?>
        <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
            <ul class="list-disc pl-5">
                <?php foreach ($errors as $error): ?>
                    <li><?= $error ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($success_message)): ?>
        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6" role="alert">
            <p><?= $success_message ?></p>
        </div>
    <?php endif; ?>
    
    <?php if ($action === 'list'): ?>
        <!-- Search and Filter Form -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <form method="get" action="admin.php" class="flex flex-wrap gap-4">
                <input type="hidden" name="section" value="licenses">
                
                <div class="flex-1 min-w-[200px]">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Pesquisar</label>
                    <input type="text" id="search" name="search" value="<?= htmlspecialchars($search_term) ?>" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                </div>
                
                <div class="w-48">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Estado</label>
                    <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                        <option value="">Todos</option>
                        <?php foreach ($status_options as $value => $label): ?>
                            <option value="<?= $value ?>" <?= $status_filter === $value ? 'selected' : '' ?>><?= $label ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="flex items-end">
                    <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        Filtrar
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Licenses Table -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Código</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cliente</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Produto</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estado</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiração</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Downloads</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Ações</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (empty($licenses)): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">Nenhuma licença encontrada</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($licenses as $license): 
                            
                            $product_info = get_product_name_by_order_item($license['order_id'], $license['order_item_id']);
                            $product_name = $product_info ? $product_info : 'N/D';
                            
                            
                            $download_count = get_license_download_count($license['id']);
                            
                            
                            $status_label = $status_options[$license['status']] ?? $license['status'];
                            $status_class = '';
                            switch ($license['status']) {
                                case 'active':
                                    $status_class = 'bg-green-100 text-green-800';
                                    break;
                                case 'waiting_payment':
                                    $status_class = 'bg-yellow-100 text-yellow-800';
                                    break;
                                case 'expired':
                                    $status_class = 'bg-gray-100 text-gray-800';
                                    break;
                                case 'revoked':
                                    $status_class = 'bg-red-100 text-red-800';
                                    break;
                            }
                        ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                <?= htmlspecialchars($license['license_code']) ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?= htmlspecialchars($license['customer_name']) ?><br>
                                <span class="text-xs"><?= htmlspecialchars($license['customer_email']) ?></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?= htmlspecialchars($product_name) ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?= $status_class ?>">
                                    <?= $status_label ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?= format_date($license['expiry_date']) ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?= $download_count ?> / <?= $license['download_limit'] ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-2">
                                    <a href="admin.php?section=licenses&action=edit&id=<?= $license['id'] ?>" class="text-indigo-600 hover:text-indigo-900">Editar</a>
                                    
                                    <?php if ($license['status'] === 'active'): ?>
                                        <a href="admin.php?section=licenses&action=revoke&id=<?= $license['id'] ?>" class="text-red-600 hover:text-red-900" onclick="return confirm('Tem certeza que deseja revogar esta licença?')">Revogar</a>
                                    <?php elseif ($license['status'] === 'revoked' || $license['status'] === 'waiting_payment'): ?>
                                        <a href="admin.php?section=licenses&action=activate&id=<?= $license['id'] ?>" class="text-green-600 hover:text-green-900">Ativar</a>
                                    <?php endif; ?>
                                    
                                    <a href="admin.php?section=licenses&action=resend&id=<?= $license['id'] ?>" class="text-blue-600 hover:text-blue-900">Reenviar</a>
                                    <a href="admin.php?section=licenses&action=delete&id=<?= $license['id'] ?>" class="text-red-600 hover:text-red-900" onclick="return confirm('Tem certeza que deseja eliminar esta licença? Esta ação não pode ser desfeita.')">Eliminar</a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <?php if ($total_pages > 1): ?>
        <div class="mt-6 flex justify-center">
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <?php if ($page > 1): ?>
                    <a href="admin.php?section=licenses&page=<?= $page - 1 ?>&search=<?= urlencode($search_term) ?>&status=<?= urlencode($status_filter) ?>" 
                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Anterior</span>
                        &laquo;
                    </a>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                    <a href="admin.php?section=licenses&page=<?= $i ?>&search=<?= urlencode($search_term) ?>&status=<?= urlencode($status_filter) ?>" 
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium <?= $i === $page ? 'text-primary bg-primary-50 z-10' : 'text-gray-500 hover:bg-gray-50' ?>">
                        <?= $i ?>
                    </a>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                    <a href="admin.php?section=licenses&page=<?= $page + 1 ?>&search=<?= urlencode($search_term) ?>&status=<?= urlencode($status_filter) ?>" 
                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <span class="sr-only">Próxima</span>
                        &raquo;
                    </a>
                <?php endif; ?>
            </nav>
        </div>
        <?php endif; ?>
    <?php elseif ($action === 'edit' && !empty($license)): ?>
        <!-- Edit License Form -->
        <div class="bg-white shadow-md rounded-lg p-6">
            <form method="post" action="admin.php?section=licenses&action=edit&id=<?= $license_id ?>">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="license_code" class="block text-sm font-medium text-gray-700 mb-1">Código de Licença</label>
                        <input type="text" id="license_code" name="license_code" value="<?= htmlspecialchars($license['license_code']) ?>" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                    
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Estado</label>
                        <select id="status" name="status" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                            <?php foreach ($status_options as $value => $label): ?>
                                <option value="<?= $value ?>" <?= $license['status'] === $value ? 'selected' : '' ?>><?= $label ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div>
                        <label for="customer_name" class="block text-sm font-medium text-gray-700 mb-1">Nome do Cliente</label>
                        <input type="text" id="customer_name" name="customer_name" value="<?= htmlspecialchars($license['customer_name']) ?>" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                    
                    <div>
                        <label for="customer_email" class="block text-sm font-medium text-gray-700 mb-1">Email do Cliente</label>
                        <input type="email" id="customer_email" name="customer_email" value="<?= htmlspecialchars($license['customer_email']) ?>" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                    
                    <div>
                        <label for="expiry_date" class="block text-sm font-medium text-gray-700 mb-1">Data de Expiração</label>
                        <input type="date" id="expiry_date" name="expiry_date" value="<?= date('Y-m-d', strtotime($license['expiry_date'])) ?>" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                    
                    <div>
                        <label for="download_limit" class="block text-sm font-medium text-gray-700 mb-1">Limite de Downloads</label>
                        <input type="number" id="download_limit" name="download_limit" value="<?= (int)$license['download_limit'] ?>" min="1" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary">
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end space-x-3">
                    <a href="admin.php?section=licenses" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        Cancelar
                    </a>
                    <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        Guardar Alterações
                    </button>
                </div>
            </form>
        </div>
    <?php endif; ?>
</div>
