<?php

if (!$is_admin_logged_in) {
    header('Location: admin.php?section=login&' . get_session_id_param());
    exit;
}

$filter_type = isset($_GET['filter']) ? $_GET['filter'] : 'all';

if ($filter_type === 'orphaned') {
    $digital_files = get_orphaned_digital_files();
} else {
    $digital_files = get_all_digital_files();
}

$digital_products = get_all_digital_products();

$file_usage = [];
foreach ($digital_files as $file) {
    $file_usage[$file['id']] = get_digital_file_usage($file['id']);
}

display_flash_messages();
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Gestão de Arquivos Digitais</h1>
        <div class="d-flex">
            <a href="admin.php?section=digital_files&action=list&<?= get_session_id_param() ?>" class="btn btn-secondary me-2">
                <i class="bi bi-list"></i> Lista de Arquivos
            </a>
            <div class="btn-group">
                <a href="admin.php?section=digital_files&action=manage&filter=all&<?= get_session_id_param() ?>" class="btn btn-outline-primary <?= $filter_type === 'all' || $filter_type === '' ? 'active' : '' ?>">
                    <i class="bi bi-files"></i> Todos os Arquivos
                </a>
                <a href="admin.php?section=digital_files&action=manage&filter=orphaned&<?= get_session_id_param() ?>" class="btn btn-outline-primary <?= $filter_type === 'orphaned' ? 'active' : '' ?>">
                    <i class="bi bi-file-earmark-x"></i> Arquivos Órfãos
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Carregar Novo Arquivo</h5>
                </div>
                <div class="card-body">
                    <!-- Flash messages are displayed above -->

                    <form method="POST" action="admin.php?section=digital_files&<?= get_session_id_param() ?>" enctype="multipart/form-data">
                        <?= csrf_input_field() ?>
                        <input type="hidden" name="action" value="upload">

                        <div class="mb-3">
                            <label for="digital_file" class="form-label">Arquivo Digital *</label>
                            <input type="file" class="form-control" id="digital_file" name="digital_file" required>
                            <div class="form-text">Tamanho máximo: 100MB.</div>
                        </div>

                        <div class="mb-3">
                            <label for="new_file_display_name" class="form-label">Nome de Exibição</label>
                            <input type="text" class="form-control" id="new_file_display_name" name="new_file_display_name" placeholder="Opcional. Usa nome original se vazio.">
                            <div class="form-text">Nome amigável para este arquivo. Se vazio, usará o nome original do arquivo.</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Descrição</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            <div class="form-text">Uma descrição opcional para identificar este arquivo.</div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-upload"></i> Carregar Arquivo
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Arquivos Digitais Disponíveis</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($digital_files)): ?>
                        <div class="alert alert-info mb-0">
                            Nenhum arquivo digital encontrado. Carregue seu primeiro arquivo usando o formulário ao lado.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Nome de Exibição</th>
                                        <th>Nome Original</th>
                                        <th>Tamanho</th>
                                        <th>Tipo</th>
                                        <th>Descrição</th>
                                        <th>Data de Upload</th>
                                        <th>Em Uso</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($digital_files as $file): ?>
                                        <tr>
                                            <td>
                                                <form method="POST" action="admin.php?section=digital_files&<?= get_session_id_param() ?>" class="d-inline">
                                                    <?= csrf_input_field() ?>
                                                    <input type="hidden" name="action" value="update">
                                                    <input type="hidden" name="file_id" value="<?= $file['id'] ?>">
                                                    <input type="text" class="form-control form-control-sm mb-1" name="display_name"
                                                           value="<?= sanitize_input(!empty($file['display_name']) ? $file['display_name'] : $file['original_filename']) ?>"
                                                           placeholder="Nome de Exibição">
                                                    <input type="text" class="form-control form-control-sm" name="description"
                                                           value="<?= sanitize_input($file['description'] ?? '') ?>" placeholder="Descrição">
                                                    <button type="submit" class="btn btn-sm btn-outline-primary mt-1">
                                                        <i class="bi bi-save"></i> Guardar
                                                    </button>
                                                </form>
                                            </td>
                                            <td><?= sanitize_input($file['original_filename']) ?></td>
                                            <td><?= format_file_size($file['file_size']) ?></td>
                                            <td><?= sanitize_input($file['file_type']) ?></td>
                                            <td><?= sanitize_input($file['description'] ?? '') ?></td>
                                            <td><?= date('d/m/Y H:i', strtotime($file['created_at'])) ?></td>
                                            <td>
                                                <?php if (count($file_usage[$file['id']]) > 0): ?>
                                                    <span class="badge bg-success"><?= count($file_usage[$file['id']]) ?> produto(s)</span>
                                                    <button type="button" class="btn btn-sm btn-link p-0 ms-1"
                                                            data-bs-toggle="popover"
                                                            data-bs-trigger="focus"
                                                            title="Produtos usando este arquivo"
                                                            data-bs-content="<?= implode('<br>', array_map(function($p) { return sanitize_input($p['name_pt']); }, $file_usage[$file['id']])) ?>">
                                                        <i class="bi bi-info-circle"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary">Não</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (count($file_usage[$file['id']]) === 0): ?>
                                                    <form method="POST" action="admin.php?section=digital_files&<?= get_session_id_param() ?>" class="d-inline" onsubmit="return confirm('Tem certeza que deseja excluir este arquivo?');">
                                                        <?= csrf_input_field() ?>
                                                        <input type="hidden" name="action" value="delete">
                                                        <input type="hidden" name="file_id" value="<?= $file['id'] ?>">
                                                        <button type="submit" class="btn btn-sm btn-danger">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </form>
                                                <?php endif; ?>
                                                <a href="<?= $file['file_path'] ?>" class="btn btn-sm btn-info" download>
                                                    <i class="bi bi-download"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl, {
            html: true
        });
    });
});
</script>
