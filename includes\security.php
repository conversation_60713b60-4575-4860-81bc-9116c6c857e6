<?php

require_once __DIR__ . '/../config.php'; 

define('ENCRYPTION_METHOD', 'AES-256-CBC');
define('ENCRYPTION_KEY_LENGTH', 32); 

function hash_password(string $password): string|false
{
    return password_hash($password, PASSWORD_DEFAULT);
}

function verify_password(string $password, string $hash): bool
{
    return password_verify($password, $hash);
}

function needs_rehash(string $hash): bool
{
    return password_needs_rehash($hash, PASSWORD_DEFAULT);
}

function generate_csrf_token(): string
{
    if (session_status() !== PHP_SESSION_ACTIVE) {
        
        
        
        if (!headers_sent()) {
             start_cookieless_session(); 
        } else {
            return ''; 
        }
    }

    if (empty($_SESSION['csrf_token'])) {
        try {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        } catch (Exception $e) {
            
            $_SESSION['csrf_token'] = hash('sha256', uniqid(microtime() . CSRF_SECRET, true));
        }
    }
    return $_SESSION['csrf_token'];
}

function validate_csrf_token(?string $submitted_token): bool
{
    if (session_status() !== PHP_SESSION_ACTIVE) {
        return false; 
    }

    if (empty($submitted_token) || empty($_SESSION['csrf_token'])) {
        return false;
    }

    
    return hash_equals($_SESSION['csrf_token'], $submitted_token);
}

function csrf_input_field(): string
{
    return '<input type="hidden" name="csrf_token" value="' . generate_csrf_token() . '">';
}

function get_encryption_key(): string
{
    
    return hash('sha256', CSRF_SECRET, true);
}

function encrypt_sensitive_data(string $data): string|false
{
    if (empty($data)) {
        return $data;
    }

    try {
        
        $iv = random_bytes(16); 

        
        $key = get_encryption_key();

        
        $encrypted = openssl_encrypt(
            $data,
            ENCRYPTION_METHOD,
            $key,
            0, 
            $iv
        );

        if ($encrypted === false) {
            return false;
        }

        
        return base64_encode($iv . $encrypted);
    } catch (Exception $e) {
        
        return false;
    }
}

function decrypt_sensitive_data(string $encrypted_data): string|false
{
    if (empty($encrypted_data)) {
        return $encrypted_data;
    }

    try {
        
        $decoded = base64_decode($encrypted_data);
        if ($decoded === false) {
            return false;
        }

        
        $iv = substr($decoded, 0, 16);

        
        $encrypted = substr($decoded, 16);

        
        $key = get_encryption_key();

        
        $decrypted = openssl_decrypt(
            $encrypted,
            ENCRYPTION_METHOD,
            $key,
            0, 
            $iv
        );

        return $decrypted;
    } catch (Exception $e) {
        
        return false;
    }
}

function is_likely_encrypted(string $data): bool
{
    
    if (base64_encode(base64_decode($data, true)) !== $data) {
        return false;
    }

    
    $decoded = base64_decode($data);
    if (strlen($decoded) <= 16) {
        return false;
    }

    

    return true;
}

?>
