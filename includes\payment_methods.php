<?php

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/db.php';

function ensure_payment_methods_table_exists() {

    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    
    $table_exists = false;
    try {
        $result = $pdo->query("SELECT 1 FROM payment_methods LIMIT 1");
        $table_exists = true;

        
        $stmt = $pdo->query("SELECT COUNT(*) FROM payment_methods");
        $count = $stmt->fetchColumn();

        if ($count == 0) {
            
            $pdo->exec("INSERT INTO payment_methods (title, instructions, is_active, sort_order) VALUES
                ('Transferência Bancária', 'Por favor, transfira o valor total da sua encomenda para a seguinte conta bancária:\n\nBanco: [Nome do Banco]\nIBAN: [Número IBAN]\nBIC/SWIFT: [Código BIC/SWIFT]\nTitular: [Nome do Titular]\n\nPor favor, inclua o número da sua encomenda na descrição da transferência.', 1, 0);");
        } else {
        }
    } catch (PDOException $e) {
        $table_exists = false;
    }

    if (!$table_exists) {
        try {
            
            $pdo->exec("CREATE TABLE payment_methods (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                instructions TEXT NOT NULL,
                is_active INTEGER NOT NULL DEFAULT 1,
                sort_order INTEGER NOT NULL DEFAULT 0,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
            );");

            $pdo->exec("CREATE INDEX idx_payment_methods_active ON payment_methods (is_active);");

            
            $pdo->exec("INSERT INTO payment_methods (title, instructions, is_active, sort_order) VALUES
                ('Transferência Bancária', 'Por favor, transfira o valor total da sua encomenda para a seguinte conta bancária:\n\nBanco: [Nome do Banco]\nIBAN: [Número IBAN]\nBIC/SWIFT: [Código BIC/SWIFT]\nTitular: [Nome do Titular]\n\nPor favor, inclua o número da sua encomenda na descrição da transferência.', 1, 0);");
            return true;
        } catch (PDOException $e) {
            return false;
        }
    }

    return true;
}

function get_payment_methods($active_only = false) {

    
    ensure_payment_methods_table_exists();

    $sql = "SELECT * FROM payment_methods";
    if ($active_only) {
        $sql .= " WHERE is_active = 1";
    }
    $sql .= " ORDER BY sort_order ASC, title ASC";

    $result = db_query($sql, [], false, true);

    
    if (empty($result)) {

        
        $pdo = get_db_connection();
        if ($pdo) {
            try {
                
                $check_sql = "SELECT COUNT(*) FROM payment_methods";
                $stmt = $pdo->prepare($check_sql);
                $stmt->execute();
                $count = $stmt->fetchColumn();

                if ($count == 0) {
                    
                    $pdo->exec("INSERT INTO payment_methods (title, instructions, is_active, sort_order) VALUES
                        ('Transferência Bancária', 'Por favor, transfira o valor total da sua encomenda para a seguinte conta bancária:\n\nBanco: [Nome do Banco]\nIBAN: [Número IBAN]\nBIC/SWIFT: [Código BIC/SWIFT]\nTitular: [Nome do Titular]\n\nPor favor, inclua o número da sua encomenda na descrição da transferência.', 1, 0);");
                }
            } catch (PDOException $e) {
            }
        }

        
        $result = db_query($sql, [], false, true);
    }

    return $result ?: []; 
}

function get_payment_method($id) {
    ensure_payment_methods_table_exists();

    $sql = "SELECT * FROM payment_methods WHERE id = :id";
    return db_query($sql, [':id' => $id], true);
}

function add_payment_method($data) {
    ensure_payment_methods_table_exists();

    $pdo = get_db_connection();

    try {
        $sql = "INSERT INTO payment_methods (title, instructions, is_active, sort_order, created_at, updated_at)
                VALUES (:title, :instructions, :is_active, :sort_order, datetime('now', 'localtime'), datetime('now', 'localtime'))";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':title' => $data['title'],
            ':instructions' => $data['instructions'],
            ':is_active' => $data['is_active'] ?? 1,
            ':sort_order' => $data['sort_order'] ?? 0
        ]);

        return $pdo->lastInsertId();
    } catch (PDOException $e) {
        return false;
    }
}

function update_payment_method($id, $data) {
    ensure_payment_methods_table_exists();

    $pdo = get_db_connection();

    try {
        $sql = "UPDATE payment_methods
                SET title = :title,
                    instructions = :instructions,
                    is_active = :is_active,
                    sort_order = :sort_order,
                    updated_at = datetime('now', 'localtime')
                WHERE id = :id";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':id' => $id,
            ':title' => $data['title'],
            ':instructions' => $data['instructions'],
            ':is_active' => $data['is_active'] ?? 1,
            ':sort_order' => $data['sort_order'] ?? 0
        ]);

        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

function delete_payment_method($id) {
    ensure_payment_methods_table_exists();

    $pdo = get_db_connection();

    try {
        $sql = "DELETE FROM payment_methods WHERE id = :id";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([':id' => $id]);

        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

function get_payment_instructions($id_or_code) {
    ensure_payment_methods_table_exists();

    $payment_method = null;

    if (is_numeric($id_or_code)) {
        
        $payment_method = get_payment_method($id_or_code);
    } else {
        
        $sql = "SELECT * FROM payment_methods WHERE title = :title AND is_active = 1";
        $payment_method = db_query($sql, [':title' => $id_or_code], true);
    }

    return $payment_method ? $payment_method['instructions'] : null;
}
