<?php

require_once __DIR__ . '/../../includes/digital_product_functions.php';

$is_editing = $action === 'edit' && $item_id > 0;
$product_id = $item_id ?? null;

$digital_product = null;
$product_data = null;
$form_title = $is_editing ? "Editar Produto Digital" : "Adicionar Produto Digital";
$selected_file_types = [];

if ($product_id) {
    $product_data = db_query("SELECT * FROM products WHERE id = :id", [':id' => $product_id], true);

    if (!$product_data) {
        add_flash_message('Produto não encontrado.', 'danger');
        header('Location: admin.php?section=products&' . get_session_id_param());
        exit;
    }



    $stmt = db_query(
        "SELECT dp.*, df.original_filename, df.display_name AS file_display_name
         FROM digital_products dp
         LEFT JOIN digital_files df ON dp.digital_file_id = df.id
         WHERE dp.product_id = :product_id",
        [':product_id' => $product_id],
        true
    );
    if ($stmt) {
        $digital_product = $stmt;

        $selected_file_types = get_digital_product_file_type_ids($digital_product['id']);
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['form_submitted_from_digital_product_form'])) {
    $errors = [];
    $digital_file_id_for_db = null; // This will hold the digital_files.id

    $active_tab = $_POST['active_tab'] ?? 'upload';
    $selected_existing_file_id = filter_input(INPUT_POST, 'existing_digital_file_id', FILTER_VALIDATE_INT);

    // Handle file choice: new upload OR existing selection OR keep current
    if ($active_tab === 'upload' && isset($_FILES['digital_file']) && $_FILES['digital_file']['error'] === UPLOAD_ERR_OK) {
        // Process new file upload
        if ($_FILES['digital_file']['error'] !== UPLOAD_ERR_OK) {
            $errors[] = 'Erro ao carregar o arquivo: ' . get_upload_error_message($_FILES['digital_file']['error']);
        } else {
            $max_size = 100 * 1024 * 1024; // 100MB
            if ($_FILES['digital_file']['size'] > $max_size) {
                $errors[] = 'O arquivo é muito grande. O tamanho máximo permitido é 100MB.';
            } else {
                $upload_dir = get_setting('digital_products_directory', '../digital_products');
                if (!file_exists($upload_dir)) {
                    if (!mkdir($upload_dir, 0755, true)) {
                        $errors[] = 'Falha ao criar o diretório de uploads.';
                    }
                }

                if (empty($errors)) {
                    $original_filename = basename($_FILES['digital_file']['name']);
                    $file_extension = pathinfo($original_filename, PATHINFO_EXTENSION);
                    $new_filename = uniqid('digital_') . '_' . time() . '.' . strtolower($file_extension);
                    $file_path_on_server = $upload_dir . '/' . $new_filename;

                    if (move_uploaded_file($_FILES['digital_file']['tmp_name'], $file_path_on_server)) {
                        // File uploaded successfully, now create a record in digital_files
                        require_once __DIR__ . '/../../includes/digital_files_functions.php';
                        $uploaded_file_display_name = sanitize_input($_POST['digital_file_display_name'] ?? '');
                        if (empty($uploaded_file_display_name)) {
                            // Use product name (if available) or original filename as fallback
                            $uploaded_file_display_name = isset($product_data['name_pt']) && !empty($product_data['name_pt']) ? $product_data['name_pt'] : $original_filename;
                        }

                        $new_digital_file_data = [
                            'original_filename' => $original_filename,
                            'display_name'      => $uploaded_file_display_name,
                            'file_path'         => $file_path_on_server, // Storing the full path from root
                            'file_size'         => $_FILES['digital_file']['size'],
                            'file_type'         => $_FILES['digital_file']['type']
                        ];
                        $digital_file_id_for_db = create_digital_file($new_digital_file_data);
                        if (!$digital_file_id_for_db) {
                            $errors[] = 'Erro ao criar registo do arquivo digital para o novo ficheiro.';
                        }
                    } else {
                        $errors[] = 'Falha ao mover o arquivo carregado para o destino final.';
                    }
                }
            }
        }
    } elseif ($active_tab === 'existing' && $selected_existing_file_id && $selected_existing_file_id > 0) {
        // Use selected existing file
        $digital_file_id_for_db = $selected_existing_file_id;
    } elseif ($is_editing && $digital_product && !empty($digital_product['digital_file_id'])) {
        // If editing, and no new file uploaded or selected, keep the current one
        $digital_file_id_for_db = $digital_product['digital_file_id'];
    }

    // If after all checks, no file is identified and one is needed:
    if (empty($digital_file_id_for_db)) {
         // This error applies if we are creating a new digital_products record,
         // or if we are editing one that previously had no file and no new file was provided.
        $errors[] = 'Por favor, carregue um novo arquivo ou selecione um arquivo existente.';
    }

    // Validate expiry_days, download_limit, file_types
    $expiry_days_posted = filter_input(INPUT_POST, 'expiry_days', FILTER_VALIDATE_INT);
    if ($expiry_days_posted === false || $expiry_days_posted < 0) {
        $errors[] = 'Dias de expiração inválidos.';
    }

    $download_limit_posted = filter_input(INPUT_POST, 'download_limit', FILTER_VALIDATE_INT);
    if ($download_limit_posted === false || $download_limit_posted < 0) {
        $errors[] = 'Limite de downloads inválido.';
    }

    $file_types_posted = $_POST['file_types'] ?? [];
    if (empty($file_types_posted)) {
        $errors[] = 'Selecione pelo menos um tipo de arquivo.';
    }

    if (empty($errors)) {
        $data_for_digital_products_table = [
            'product_id'     => $product_id, // This is products.id
            'digital_file_id'=> $digital_file_id_for_db, // This is digital_files.id
            'expiry_days'    => $expiry_days_posted,
            'download_limit' => $download_limit_posted,
            'file_types'     => $file_types_posted
        ];

        $success_db_operation = false;
        if ($is_editing && $digital_product) { // $digital_product is the record from digital_products table
            // $digital_product['id'] is digital_products.id
            if (update_digital_product($digital_product['id'], $data_for_digital_products_table)) {
                add_flash_message('Produto digital atualizado com sucesso.', 'success');
                $success_db_operation = true;
            } else {
                add_flash_message('Erro ao atualizar o produto digital. Verifique os logs para mais detalhes.', 'danger');
            }
        } else {
            // Creating a new entry in digital_products table
            $new_dp_id = create_digital_product($data_for_digital_products_table);
            if ($new_dp_id) {
                add_flash_message('Produto digital criado com sucesso.', 'success');
                $success_db_operation = true;
            } else {
                add_flash_message('Erro ao criar o produto digital. Verifique os logs para mais detalhes.', 'danger');
            }
        }

        if ($success_db_operation) {
            header('Location: admin.php?section=products&action=edit&id=' . $product_id . '&' . get_session_id_param());
            exit;
        }
    }

    // If errors exist (either from file handling or other validations)
    if (!empty($errors)) {
        foreach ($errors as $error) {
            add_flash_message($error, 'danger');
        }
        // No redirect, stay on page to show errors
    }
}

$all_file_types = get_all_file_types();

require_once __DIR__ . '/../../includes/digital_files_functions.php';
$all_digital_files = get_all_digital_files();

// Enhance digital files with their file type IDs from digital_file_type_associations table
foreach ($all_digital_files as &$file) {
    if (!isset($file['file_type_ids'])) {
        // Get file type IDs and ensure it's an array
        $file_type_ids = get_digital_file_file_type_ids((int)$file['id']);
        $file['file_type_ids'] = is_array($file_type_ids) ? $file_type_ids : [];

        // Log for debugging
        error_log("File ID {$file['id']} ({$file['original_filename']}) has " . count($file['file_type_ids']) . " file type associations");
    }
}
unset($file); // Break the reference

// Prepare JSON string for $all_digital_files safely
$all_digital_files_json_string = json_encode(
    $all_digital_files,
    JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_SUBSTITUTE
);

if ($all_digital_files_json_string === false) {
    // Log a server-side error for diagnostics if json_encode fails
    error_log("CRITICAL: json_encode failed for \$all_digital_files in templates/backend/digital_product_form.php. JSON Error: " . json_last_error_msg());
    $all_digital_files_json_string = '[]'; // Fallback to an empty JavaScript array string
}

// Prepare submit button text for JavaScript
$submit_button_text_base = ($is_editing && $digital_product) ? 'Atualizar' : 'Criar';
$submit_button_text_js_string = json_encode($submit_button_text_base . ' Produto Digital', JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE | JSON_INVALID_UTF8_SUBSTITUTE);

if ($submit_button_text_js_string === false) {
    error_log("CRITICAL: json_encode failed for submit_button_text_js_string in templates/backend/digital_product_form.php. JSON Error: " . json_last_error_msg());
    $submit_button_text_js_string = '"Guardar"'; // Fallback JS string
}

display_flash_messages();
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0"><?= $form_title ?></h1>
        <a href="admin.php?section=products&action=edit&id=<?= $product_id ?>&<?= get_session_id_param() ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Voltar ao Produto
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <form method="POST" action="admin.php?section=digital_products&action=<?= $is_editing ? 'edit&id=' . $product_id : 'new&id=' . $product_id ?>&<?= get_session_id_param() ?>" enctype="multipart/form-data">
                <?= csrf_input_field() ?>
                <input type="hidden" name="form_submitted_from_digital_product_form" value="1">
                <input type="hidden" name="active_tab" id="active_tab_input" value="upload"> <!-- Default to upload -->

                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="card mb-3">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Selecionar Arquivo Digital</h5>
                            </div>
                            <div class="card-body">
                                <ul class="nav nav-tabs" id="fileSelectionTabs" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link <?= empty($digital_product['digital_file_id']) ? 'active' : '' ?>" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-tab-pane" type="button" role="tab" aria-controls="upload-tab-pane" aria-selected="<?= empty($digital_product['digital_file_id']) ? 'true' : 'false' ?>">
                                            <i class="bi bi-upload"></i> Carregar Novo
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link <?= !empty($digital_product['digital_file_id']) ? 'active' : '' ?>" id="existing-tab" data-bs-toggle="tab" data-bs-target="#existing-tab-pane" type="button" role="tab" aria-controls="existing-tab-pane" aria-selected="<?= !empty($digital_product['digital_file_id']) ? 'true' : 'false' ?>">
                                            <i class="bi bi-list-ul"></i> Selecionar Existente
                                        </button>
                                    </li>
                                </ul>
                                <div class="tab-content p-3 border border-top-0 rounded-bottom" id="fileSelectionTabsContent">
                                    <div class="tab-pane fade <?= empty($digital_product['digital_file_id']) ? 'show active' : '' ?>" id="upload-tab-pane" role="tabpanel" aria-labelledby="upload-tab" tabindex="0">
                                        <div class="mb-3">
                                            <label for="digital_file" class="form-label">Arquivo Digital <?= (!$is_editing || !$digital_product || empty($digital_product['digital_file_id'])) ? '*' : '' ?></label>
                                            <input type="file" class="form-control" id="digital_file" name="digital_file">
                                            <div class="form-text">Tamanho máximo: 100MB.</div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="digital_file_display_name" class="form-label">Nome de Exibição do Arquivo</label>
                                            <input type="text" class="form-control" id="digital_file_display_name" name="digital_file_display_name"
                                                value="<?= sanitize_input($digital_product['file_display_name'] ?? '') ?>"
                                                placeholder="Opcional. Usará nome do produto ou original.">
                                            <div class="form-text">Nome amigável para o arquivo. Se um novo arquivo for carregado e este campo estiver vazio, será usado o nome do produto ou o nome original do arquivo.</div>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade <?= !empty($digital_product['digital_file_id']) ? 'show active' : '' ?>" id="existing-tab-pane" role="tabpanel" aria-labelledby="existing-tab" tabindex="0">
                                        <div class="mb-3">
                                            <label for="existing_digital_file_id" class="form-label">Selecionar Arquivo Existente <?= (!$is_editing || !$digital_product || empty($digital_product['digital_file_id'])) ? '*' : '' ?></label>
                                            <select class="form-select" id="existing_digital_file_id" name="existing_digital_file_id">
                                                <option value="">-- Selecione um Arquivo --</option>
                                                <?php foreach ($all_digital_files as $file): ?>
                                                    <option value="<?= $file['id'] ?>" <?= (!empty($digital_product['digital_file_id']) && $digital_product['digital_file_id'] == $file['id']) ? 'selected' : '' ?>>
                                                        <?= sanitize_input(!empty($file['display_name']) ? $file['display_name'] : $file['original_filename']) ?>
                                                        <?php if (!empty($file['display_name']) && $file['display_name'] !== $file['original_filename']): ?>
                                                            (<?= sanitize_input($file['original_filename']) ?>)
                                                        <?php endif; ?>
                                                        - <?= format_file_size($file['file_size']) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-text">
                                                Selecione um arquivo digital já carregado no sistema.
                                                <?php if (empty($all_digital_files)): ?>
                                                    <span class="text-warning">Nenhum arquivo disponível. Carregue um novo arquivo.</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                        <?php if (!empty($all_digital_files)): ?>
                                            <div class="mb-3">
                                                <a href="admin.php?section=digital_files&action=list&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-secondary" target="_blank">
                                                    <i class="bi bi-folder"></i> Gerenciar Arquivos Digitais
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php if ($is_editing && $digital_product && !empty($digital_product['digital_file_id'])): ?>
                            <div class="alert alert-info">
                                <strong>Arquivo atual:</strong>
                                <?php
                                    $current_file_label_dpf = !empty($digital_product['file_display_name']) ? sanitize_input($digital_product['file_display_name']) : sanitize_input($digital_product['original_filename'] ?? 'N/A');
                                    echo $current_file_label_dpf;
                                ?>
                                <a href="admin.php?section=digital_files&action=change_file&id=<?= $digital_product['id'] ?>&item_id=<?= $digital_product['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-primary ms-2">
                                    <i class="bi bi-arrow-repeat"></i> Alterar Arquivo
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="expiry_days" class="form-label">Dias de Expiração *</label>
                                <input type="number" class="form-control" id="expiry_days" name="expiry_days"
                                       value="<?= $digital_product['expiry_days'] ?? get_setting('digital_download_expiry_days', 5) ?>"
                                       min="0" required>
                                <div class="form-text">
                                    Número de dias após a compra em que o download estará disponível. Use 0 para sem expiração.
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="download_limit" class="form-label">Limite de Downloads *</label>
                                <input type="number" class="form-control" id="download_limit" name="download_limit"
                                       value="<?= $digital_product['download_limit'] ?? get_setting('digital_download_limit', 3) ?>"
                                       min="0" required>
                                <div class="form-text">
                                    Número máximo de downloads permitidos. Use 0 para downloads ilimitados.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Tipos de Arquivo Incluídos *</label>
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($all_file_types as $type): ?>
                                    <div class="col-md-3 mb-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" name="file_types[]"
                                                   value="<?= $type['id'] ?>"
                                                   id="file_type_<?= $type['id'] ?>"
                                                   <?= in_array($type['id'], $selected_file_types) ? 'checked' : '' ?>>
                                            <label class="form-check-label" for="file_type_<?= $type['id'] ?>">
                                                <?= sanitize_input($type['name']) ?> (<?= sanitize_input($type['extension']) ?>)
                                            </label>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            <?php if (empty($all_file_types)): ?>
                                <div class="alert alert-warning mb-0">
                                    Nenhum tipo de arquivo definido. <a href="admin.php?section=file_types&<?= get_session_id_param() ?>" target="_blank">Adicionar tipos de arquivo</a>.
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($digital_product) && !empty($digital_product['digital_file_id'])): ?>
                                <div class="mt-3 p-2 bg-light border rounded">
                                    <?php if (empty($selected_file_types)): ?>
                                        <div class="alert alert-info mb-0">
                                            <i class="bi bi-info-circle"></i> Este arquivo não tem tipos de arquivo associados. Selecione os tipos de arquivo que deseja incluir.
                                        </div>
                                    <?php else: ?>
                                        <small class="text-muted">Tipos selecionados: <?= implode(', ', $selected_file_types) ?></small>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="form-text">Selecione as extensões de arquivo incluídas neste produto digital.</div>
                </div>

                <div class="d-flex justify-content-end">
                    <a href="#" onclick="event.preventDefault(); window.history.back(); return false;" class="btn btn-secondary me-2">
                        Cancelar
                    </a>
                    <button type="submit" class="btn btn-primary" id="submit-button">
                        <?= $is_editing && $digital_product ? 'Atualizar' : 'Criar' ?> Produto Digital
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Explicitly initialize Bootstrap Tabs
    const triggerTabList = document.querySelectorAll('#fileSelectionTabs button[data-bs-toggle="tab"]');
    triggerTabList.forEach(function (triggerEl) {
        // Ensure a Bootstrap Tab instance is created for each tab button.
        // This can help if default data-attribute initialization is failing.
        if (window.bootstrap && window.bootstrap.Tab) { // Check if bootstrap.Tab is available
            const tabInstance = window.bootstrap.Tab.getInstance(triggerEl);
            if (!tabInstance) {
                new window.bootstrap.Tab(triggerEl);
            }
        }
    });

    // Get references to important elements
    const existingFileSelect = document.getElementById('existing_digital_file_id');
    const submitButton = document.getElementById('submit-button');
    const uploadTab = document.getElementById('upload-tab');
    const existingTab = document.getElementById('existing-tab');
    const uploadTabPane = document.getElementById('upload-tab-pane');
    const existingTabPane = document.getElementById('existing-tab-pane');

    // Store digital files data for easy access
    const digitalFiles = <?php echo $all_digital_files_json_string; ?>;
    console.log('Digital files data:', digitalFiles);

    // Function to update the button text based on the selected tab
    function updateButtonText() {
        // Check if existing tab is active by checking the tab pane's classes
        const isExistingTabActive = existingTabPane.classList.contains('active');
        const hasSelectedFile = existingFileSelect && existingFileSelect.value !== '';

        if (isExistingTabActive && hasSelectedFile) {
            submitButton.textContent = 'Guardar definições';
        } else {
            submitButton.textContent = <?php echo $submit_button_text_js_string; ?>;
        }
    }

    // Function to populate form fields when an existing file is selected
    function populateFileDetails(fileId) {
        console.log('populateFileDetails called with fileId:', fileId);

        if (!fileId) {
            console.log('No file ID provided, unchecking all checkboxes');
            // If no file is selected, uncheck all checkboxes
            document.querySelectorAll('input[name="file_types[]"]').forEach(checkbox => {
                checkbox.checked = false;
            });
            return;
        }

        // Find the selected file in our data
        const selectedFile = digitalFiles.find(file => file.id == fileId);
        if (!selectedFile) {
            console.warn('File not found in data:', fileId);
            // Uncheck all checkboxes if file not found
            document.querySelectorAll('input[name="file_types[]"]').forEach(checkbox => {
                checkbox.checked = false;
            });
            return;
        }

        console.log('Selected file:', selectedFile);

        // Populate display name field if it exists
        const displayNameField = document.getElementById('digital_file_display_name');
        if (displayNameField) {
            // Use display_name, short_name, or original_filename in that order of preference
            displayNameField.value = selectedFile.display_name || selectedFile.short_name || selectedFile.original_filename;
        }

        // First, uncheck all checkboxes before fetching associations
        document.querySelectorAll('input[name="file_types[]"]').forEach(checkbox => {
            checkbox.checked = false;
        });

        // Fetch file type associations for this file from digital products
        fetchFileTypeAssociations(fileId);

        // Make sure the existing tab is active
        if (existingTab && !existingTabPane.classList.contains('active')) {
            // Create a new bootstrap tab instance and show it
            const bsTab = new bootstrap.Tab(existingTab);
            bsTab.show();
        }

        // Update button text
        updateButtonText();
    }

    // Function to fetch file type associations for a digital file
    function fetchFileTypeAssociations(fileId) {
        if (!fileId) {
            console.log('No file ID provided to fetchFileTypeAssociations');
            return;
        }

        console.log('Fetching file type associations for file ID:', fileId);

        // First, try to get file types from the digitalFiles data
        const selectedFile = digitalFiles.find(file => file.id == fileId);
        if (selectedFile && selectedFile.hasOwnProperty('file_type_ids')) {
            console.log('Using file type IDs from digitalFiles data:', selectedFile.file_type_ids);

            // Check if file_type_ids is actually an array
            if (Array.isArray(selectedFile.file_type_ids)) {
                console.log('file_type_ids is an array with length:', selectedFile.file_type_ids.length);
                // Even if the array is empty, we should use it (to show no checkboxes checked)
                updateFileTypeCheckboxes(selectedFile.file_type_ids);
                return; // Use the data we already have
            } else {
                console.warn('file_type_ids is not an array:', typeof selectedFile.file_type_ids);
                // Continue to fetch from server
            }
        } else {
            console.log('No file_type_ids property found in selectedFile, fetching from server');
        }

        // Create a FormData object
        const formData = new FormData();
        formData.append('action', 'get_file_type_associations');
        formData.append('file_id', fileId);
        formData.append('csrf_token', '<?php echo $_SESSION['csrf_token'] ?? ''; ?>');

        // Send AJAX request to get file type associations directly from digital_file_type_associations table
        // Use our dedicated endpoint instead of admin.php
        fetch('get_file_type_associations.php', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }

            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                return response.text().then(text => {
                    console.error('Response is not JSON:', text);
                    throw new Error('Response is not JSON');
                });
            }

            return response.json();
        })
        .then(data => {
            console.log('File type associations response:', data);

            if (data.success && data.hasOwnProperty('file_type_ids')) {
                console.log('File type IDs to check:', data.file_type_ids);

                // Ensure file_type_ids is an array
                const fileTypeIds = Array.isArray(data.file_type_ids) ? data.file_type_ids : [];
                console.log('Processed file type IDs:', fileTypeIds);

                // Update checkboxes based on returned file type IDs
                updateFileTypeCheckboxes(fileTypeIds);
            } else {
                console.warn('Failed to fetch file type associations:', data.message || 'Unknown error');
                // Uncheck all checkboxes as a fallback
                document.querySelectorAll('input[name="file_types[]"]').forEach(checkbox => {
                    checkbox.checked = false;
                });
                // Show message about no associations
                const fileTypesContainer = document.querySelector('.card-body .row').closest('.card-body');
                const existingMessage = fileTypesContainer.querySelector('.file-types-message');
                if (existingMessage) {
                    existingMessage.remove();
                }
                const message = document.createElement('div');
                message.className = 'alert alert-info mt-3 mb-0 file-types-message';
                message.innerHTML = '<i class="bi bi-info-circle"></i> Este arquivo não tem tipos de arquivo associados. Selecione os tipos de arquivo que deseja incluir.';
                fileTypesContainer.appendChild(message);
            }
        })
        .catch(error => {
            console.error('Error fetching file type associations:', error);
            // Uncheck all checkboxes on error
            document.querySelectorAll('input[name="file_types[]"]').forEach(checkbox => {
                checkbox.checked = false;
            });
            // Show error message
            const fileTypesContainer = document.querySelector('.card-body .row').closest('.card-body');
            const existingMessage = fileTypesContainer.querySelector('.file-types-message');
            if (existingMessage) {
                existingMessage.remove();
            }
            const message = document.createElement('div');
            message.className = 'alert alert-warning mt-3 mb-0 file-types-message';
            message.innerHTML = '<i class="bi bi-exclamation-triangle"></i> Erro ao obter tipos de arquivo. Selecione manualmente os tipos de arquivo que deseja incluir.';
            fileTypesContainer.appendChild(message);
        });
    }

    // This function is kept for backward compatibility but is no longer used
    // We now handle everything in the fetch error handler
    function getFileTypesFromProducts(fileId) {
        console.log('This function is deprecated and should not be called');

        // Uncheck all checkboxes to be safe
        document.querySelectorAll('input[name="file_types[]"]').forEach(checkbox => {
            checkbox.checked = false;
        });

        // Show message about no associations
        const fileTypesContainer = document.querySelector('.card-body .row').closest('.card-body');
        const existingMessage = fileTypesContainer.querySelector('.file-types-message');
        if (existingMessage) {
            existingMessage.remove();
        }
        const message = document.createElement('div');
        message.className = 'alert alert-info mt-3 mb-0 file-types-message';
        message.innerHTML = '<i class="bi bi-info-circle"></i> Este arquivo não tem tipos de arquivo associados. Selecione os tipos de arquivo que deseja incluir.';
        fileTypesContainer.appendChild(message);

        console.log('No file type associations found, all checkboxes unchecked');
    }

    // Function to update file type checkboxes
    function updateFileTypeCheckboxes(fileTypeIds) {
        console.log('Updating file type checkboxes with IDs:', fileTypeIds);

        // Ensure fileTypeIds is an array
        if (!Array.isArray(fileTypeIds)) {
            console.warn('fileTypeIds is not an array, converting to empty array');
            fileTypeIds = [];
        }

        // Get all checkboxes
        const allCheckboxes = document.querySelectorAll('input[name="file_types[]"]');
        console.log('Found checkboxes:', allCheckboxes.length);

        // First, uncheck all checkboxes
        allCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
            console.log('Unchecked checkbox:', checkbox.id, checkbox.value);
        });

        // Show a message if no file types are selected
        const fileTypesContainer = document.querySelector('.card-body .row').closest('.card-body');
        const existingMessage = fileTypesContainer.querySelector('.file-types-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        if (fileTypeIds.length === 0) {
            console.log('No file type IDs provided, showing message');
            const message = document.createElement('div');
            message.className = 'alert alert-info mt-3 mb-0 file-types-message';
            message.innerHTML = '<i class="bi bi-info-circle"></i> Este arquivo não tem tipos de arquivo associados. Selecione os tipos de arquivo que deseja incluir.';
            fileTypesContainer.appendChild(message);
            // Ensure all checkboxes remain unchecked when there are no associations
            return;
        }

        // Then check the ones that match the file type IDs
        let foundAny = false;

        fileTypeIds.forEach(typeId => {
            // Convert to integer if it's a string
            const typeIdInt = parseInt(typeId, 10);
            if (isNaN(typeIdInt)) {
                console.warn('Invalid file type ID (not a number):', typeId);
                return; // Skip this iteration
            }

            const checkbox = document.getElementById(`file_type_${typeIdInt}`);
            console.log('Looking for checkbox with ID:', `file_type_${typeIdInt}`, 'Found:', !!checkbox);

            if (checkbox) {
                checkbox.checked = true;
                console.log('Checked checkbox:', checkbox.id, checkbox.value);
                foundAny = true;
            } else {
                // Try by value if ID doesn't work
                const checkboxByValue = document.querySelector(`input[name="file_types[]"][value="${typeIdInt}"]`);
                if (checkboxByValue) {
                    checkboxByValue.checked = true;
                    console.log('Checked checkbox by value:', checkboxByValue.id, checkboxByValue.value);
                    foundAny = true;
                } else {
                    console.warn('Could not find checkbox for file type ID:', typeIdInt);
                }
            }
        });

        // If we didn't find any matching checkboxes, leave all unchecked and show message
        if (!foundAny && fileTypeIds.length > 0) {
            console.warn('No matching checkboxes found for the provided file type IDs:', fileTypeIds);
            const message = document.createElement('div');
            message.className = 'alert alert-warning mt-3 mb-0 file-types-message';
            message.innerHTML = '<i class="bi bi-exclamation-triangle"></i> Não foi possível encontrar os tipos de arquivo associados. Selecione manualmente os tipos de arquivo que deseja incluir.';
            fileTypesContainer.appendChild(message);
        }
    }

    // Add event listener to the file select dropdown
    if (existingFileSelect) {
        existingFileSelect.addEventListener('change', function() {
            populateFileDetails(this.value);
        });
    }

    // Add event listeners to tabs to update button text and hidden input when tabs change
    const activeTabInput = document.getElementById('active_tab_input');
    const tabEls = document.querySelectorAll('button[data-bs-toggle="tab"]');
    tabEls.forEach(tabEl => {
        tabEl.addEventListener('shown.bs.tab', function (event) {
            updateButtonText();
            if (event.target.id === 'upload-tab') {
                activeTabInput.value = 'upload';
            } else if (event.target.id === 'existing-tab') {
                activeTabInput.value = 'existing';
            }
        });
    });

    // Set initial active_tab_input value based on which tab is active on load
    if (uploadTabPane && uploadTabPane.classList.contains('active')) {
        activeTabInput.value = 'upload';
    } else if (existingTabPane && existingTabPane.classList.contains('active')) {
        activeTabInput.value = 'existing';
    }

    // Initialize with current selection
    if (existingFileSelect && existingFileSelect.value) {
        populateFileDetails(existingFileSelect.value);
    } else {
        // If no file is selected, make sure all checkboxes are unchecked
        // This handles the case of a new product with no file selected yet
        document.querySelectorAll('input[name="file_types[]"]').forEach(checkbox => {
            // Only uncheck if we're not editing an existing product
            // For existing products, PHP will have set the correct checkboxes
            if (!<?= $is_editing ? 'true' : 'false' ?>) {
                checkbox.checked = false;
            }
        });
    }

    // Initial button text update
    updateButtonText();
});
</script>
