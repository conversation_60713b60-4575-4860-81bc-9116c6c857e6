<?php

if (!function_exists('get_min_max_product_prices')) {
    
    
    
    
    
    
    
}
$price_bounds = get_min_max_product_prices();
$min_price_overall = $price_bounds['min_price'];
$max_price_overall = $price_bounds['max_price'];

$current_min_price = isset($_GET['min_price']) ? (float)$_GET['min_price'] : $min_price_overall;
$current_max_price = isset($_GET['max_price']) ? (float)$_GET['max_price'] : $max_price_overall;

$current_sort = $_GET['sort'] ?? 'recent';
?>

<div id="collapsible-filter-bar" class="bg-gray-800 text-white w-full hidden">
    <div class="container mx-auto px-4 py-3">
        <div class="flex flex-col md:flex-row items-center justify-between gap-4">
            
            <!-- Sorting (to be properly integrated or moved from home.php) -->
            <div class="flex items-center w-full md:w-auto">
                <span class="text-xs sm:text-sm text-gray-400 mr-2">Ordem:</span>
                <div class="relative flex-grow md:flex-grow-0">
                    <select id="collapsible-product-sort-select" name="sort" class="w-full md:w-auto bg-gray-700 px-3 py-1.5 sm:px-4 sm:py-2 text-xs sm:text-sm rounded-button appearance-none pr-8 focus:outline-none focus:ring-1 focus:ring-primary">
                        <option value="recent" <?= ($current_sort === 'recent') ? 'selected' : '' ?>>Mais Recentes</option>
                        <option value="price_asc" <?= ($current_sort === 'price_asc') ? 'selected' : '' ?>>Preço: Baixo para Alto</option>
                        <option value="price_desc" <?= ($current_sort === 'price_desc') ? 'selected' : '' ?>>Preço: Alto para Baixo</option>
                    </select>
                    <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                        <i class="ri-arrow-down-s-line text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- Price Range Filter -->
            <div class="flex flex-col md:flex-row items-center w-full md:w-auto md:space-x-4">
                <span class="text-xs sm:text-sm text-gray-400 mb-1 md:mb-0 md:mr-2">Preço:</span>
                <div class="flex items-center space-x-2 w-full md:w-auto">
                    <div class="flex-1">
                        <input type="number" id="min-price-input" value="<?= sanitize_input($current_min_price) ?>" min="<?= sanitize_input($min_price_overall) ?>" max="<?= sanitize_input($max_price_overall) ?>" class="w-full bg-gray-700 text-xs p-1.5 rounded-button text-center appearance-none focus:outline-none focus:ring-1 focus:ring-primary" placeholder="Min">
                    </div>
                    <span class="text-gray-400">-</span>
                    <div class="flex-1">
                        <input type="number" id="max-price-input" value="<?= sanitize_input($current_max_price) ?>" min="<?= sanitize_input($min_price_overall) ?>" max="<?= sanitize_input($max_price_overall) ?>" class="w-full bg-gray-700 text-xs p-1.5 rounded-button text-center appearance-none focus:outline-none focus:ring-1 focus:ring-primary" placeholder="Max">
                    </div>
                     <button id="apply-price-filter-btn" class="bg-primary hover:bg-secondary text-white px-3 py-1.5 text-xs rounded-button">Aplicar</button>
                </div>
            </div>
            <!-- End Price Range Filter -->

        </div>
    </div>
</div>

<button id="toggle-filter-bar-btn" class="fixed bottom-4 right-4 md:bottom-8 md:right-8 bg-primary text-white p-3 rounded-full shadow-lg z-50 hover:bg-secondary transition">
    <i class="ri-filter-3-line text-xl"></i>
    <span class="sr-only">Mostrar/Ocultar Filtros</span>
</button>

<script>
// Basic toggle for the filter bar - will be enhanced
document.addEventListener('DOMContentLoaded', function() {
    const toggleBtn = document.getElementById('toggle-filter-bar-btn');
    const filterBar = document.getElementById('collapsible-filter-bar');
    const minPriceInput = document.getElementById('min-price-input');
    const maxPriceInput = document.getElementById('max-price-input');
    const applyPriceFilterBtn = document.getElementById('apply-price-filter-btn');

    const overallMinPrice = minPriceInput ? parseFloat(minPriceInput.min) : 0;
    const overallMaxPrice = maxPriceInput ? parseFloat(maxPriceInput.max) : 1000;

    const localStorageKey = 'filterBarState';

    if (filterBar) {
        // Apply saved state on load
        const savedState = localStorage.getItem(localStorageKey);
        if (savedState === 'expanded') {
            filterBar.classList.remove('hidden'); // Make it part of the layout
            // Set styles directly for initial state without transition
            // Use a slight delay to ensure scrollHeight is calculated correctly after DOM is fully ready
            setTimeout(() => {
                filterBar.style.maxHeight = filterBar.scrollHeight + "px";
                filterBar.style.opacity = "1";
            }, 0);
        } else {
            // Default to collapsed. The 'hidden' class is already on the element by default.
            // Ensure styles for collapsed state are set.
            filterBar.style.maxHeight = "0px";
            filterBar.style.opacity = "0";
            if (!filterBar.classList.contains('hidden')) { // Should be redundant due to default HTML
                filterBar.classList.add('hidden');
            }
        }
    }

    if (toggleBtn && filterBar) {
        toggleBtn.addEventListener('click', function() {
            if (filterBar.classList.contains('hidden')) { // Currently hidden, will be expanded
                filterBar.classList.remove('hidden');
                requestAnimationFrame(() => {
                    filterBar.style.maxHeight = filterBar.scrollHeight + "px";
                    filterBar.style.opacity = "1";
                });
                localStorage.setItem(localStorageKey, 'expanded'); // Save new state
            } else { // Currently visible, will be collapsed
                filterBar.style.maxHeight = "0px";
                filterBar.style.opacity = "0";
                localStorage.setItem(localStorageKey, 'collapsed'); // Save new state
                // The 'hidden' class will be added on transitionend
            }
        });
        
        filterBar.addEventListener('transitionend', function() {
            // Only add 'hidden' if it's meant to be collapsed (maxHeight is 0px)
            // This prevents adding 'hidden' if the transition was for expansion.
            if (filterBar.style.maxHeight === "0px") {
                filterBar.classList.add('hidden');
            }
        });
    }
    
    function applyFilters() {
        const newSort = collapsibleSortSelect ? collapsibleSortSelect.value : 'recent';
        const newMinPrice = minPriceInput ? parseFloat(minPriceInput.value) : overallMinPrice;
        const newMaxPrice = maxPriceInput ? parseFloat(maxPriceInput.value) : overallMaxPrice;

        const url = new URL(window.location.href);
        const currentCatId = url.searchParams.get('category_id');

        if (currentCatId) {
            url.searchParams.set('category_id', currentCatId);
        } else {
            url.searchParams.delete('category_id');
        }
        
        if (newSort && newSort !== 'recent') {
            url.searchParams.set('sort', newSort);
        } else {
            url.searchParams.delete('sort');
        }

        if (newMinPrice > overallMinPrice || newMinPrice === 0) { // Also consider 0 if it's a valid user input intent
             url.searchParams.set('min_price', newMinPrice);
        } else {
             url.searchParams.delete('min_price');
        }

        if (newMaxPrice < overallMaxPrice) {
            url.searchParams.set('max_price', newMaxPrice);
        } else {
            url.searchParams.delete('max_price');
        }

        url.searchParams.delete('pg'); // Reset to page 1
        window.location.href = url.toString();
    }

    if (applyPriceFilterBtn) {
        applyPriceFilterBtn.addEventListener('click', applyFilters);
    }
    
    // Debounce function
    function debounce(func, delay) {
        let timeout;
        return function(...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => func.apply(this, args), delay);
        };
    }

    const debouncedApplyFilters = debounce(applyFilters, 800);

    if (minPriceInput) {
        minPriceInput.addEventListener('change', debouncedApplyFilters); // Or 'input' for more responsiveness
    }
    if (maxPriceInput) {
        maxPriceInput.addEventListener('change', debouncedApplyFilters); // Or 'input' for more responsiveness
    }

    const collapsibleSortSelect = document.getElementById('collapsible-product-sort-select');
    if (collapsibleSortSelect) {
        // Get current sort from URL or default
        const urlParamsSort = new URLSearchParams(window.location.search).get('sort');
        collapsibleSortSelect.value = urlParamsSort || 'recent';

        collapsibleSortSelect.addEventListener('change', applyFilters);
    }
});
</script>
<style>
#collapsible-filter-bar {
    transition: max-height 0.3s ease-out, opacity 0.3s ease-out; /* Smoother transition */
    overflow: hidden;
    max-height: 0; /* Start collapsed */
    opacity: 0;
}
#collapsible-filter-bar:not(.hidden) {
    /* opacity: 1; is set by JS to ensure it's visible after max-height transition starts */
}
/* Hide number input spinners */
input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type=number] {
  -moz-appearance: textfield; /* Firefox */
}
</style>