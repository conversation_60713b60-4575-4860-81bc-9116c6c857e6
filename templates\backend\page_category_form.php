<?php

$is_editing = isset($category_data) && !empty($category_data);
$form_action = $is_editing
    ? 'admin.php?section=page_categories&action=edit&id=' . $category_data['id'] . '&' . get_session_id_param()
    : 'admin.php?section=page_categories&action=new&' . get_session_id_param();
$form_title = $is_editing ? 'Editar Categoria de Página' : 'Criar Nova Categoria de Página';
$button_label = $is_editing ? 'Guardar Alterações' : 'Criar Categoria';

$category_name = $_POST['name'] ?? ($category_data['name'] ?? '');
$category_slug = $_POST['slug'] ?? ($category_data['slug'] ?? '');
$show_in_header = $_POST['show_in_header'] ?? ($category_data['show_in_header'] ?? 0); 

?>
<h1><?= $form_title ?></h1>

<form action="<?= $form_action ?>" method="post">
    <?= csrf_input_field() ?> <!-- Use the helper function to generate the input -->

    <div class="card">
        <div class="card-body">
            <div class="mb-3">
                <label for="name" class="form-label">Nome da Categoria <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="name" name="name" value="<?= htmlspecialchars($category_name) ?>" required>
            </div>

            <div class="mb-3">
                <label for="slug" class="form-label">Slug (URL)</label>
                <input type="text" class="form-control" id="slug" name="slug" value="<?= htmlspecialchars($category_slug) ?>" aria-describedby="slugHelp">
                <div id="slugHelp" class="form-text">Deixe em branco para gerar automaticamente a partir do nome. Use apenas letras minúsculas, números e hífens.</div>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="show_in_header" name="show_in_header" value="1" <?= ($show_in_header == 1) ? 'checked' : '' ?>>
                <label class="form-check-label" for="show_in_header">Mostrar no Cabeçalho</label>
                 <div class="form-text">Se ativo, esta categoria aparecerá como um menu no cabeçalho do site.</div>
            </div>

            <hr>

            <button type="submit" class="btn btn-primary"><?= $button_label ?></button>
            <a href="admin.php?section=page_categories&<?= get_session_id_param() ?>" class="btn btn-secondary">Cancelar</a>
        </div>
    </div>
</form>
