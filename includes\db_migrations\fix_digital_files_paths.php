<?php

/**
 * Migration to fix file paths in the digital_files table
 * Convert absolute paths to relative paths
 */
function migrate_fix_digital_files_paths()
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        $pdo->beginTransaction();

        // Update all file paths with absolute paths to use relative paths
        // First, convert C:\xampp/digital_products/digital_XXXXX.zip to ../digital_products/digital_XXXXX.zip
        $pdo->exec("UPDATE digital_files SET file_path = '../digital_products/' || substr(file_path, instr(file_path, 'digital_')) WHERE file_path LIKE 'C:%'");

        // Fix any paths that might have duplicate digital_products folders
        $pdo->exec("UPDATE digital_files SET file_path = REPLACE(file_path, '../digital_products/digital_products/', '../digital_products/') WHERE file_path LIKE '../digital_products/digital_products/%'");

        error_log("Updated file paths in digital_files table to use relative paths");

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Migration error (fix_digital_files_paths): " . $e->getMessage());
        return false;
    }
}
