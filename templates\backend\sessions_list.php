<?php
display_flash_messages();

// Variables $sessions_result, $sessions, $csrf_token are expected to be
// locally available in this scope due to extract() in include_template().
// We use null coalescing for safety, in case they are not set from the caller.

$sessions_result = $sessions_result ?? ['success' => false, 'message' => 'Variável $sessions_result não foi definida no template.', 'sessions' => []];
$sessions = $sessions ?? [];
$csrf_token = $csrf_token ?? '';

?>

<div class="container-fluid">
    <h1>G<PERSON>r Sessões Ativas</h1>
    <p class="lead">Lista de todas as sessões de utilizador ativas no sistema.</p>

    <?php if (!($sessions_result['success'] ?? false)): ?>
        <div class="alert alert-danger">
            Erro ao carregar sessões: <?= htmlspecialchars($sessions_result['message'] ?? 'Erro desconhecido ao processar $sessions_result no template.') ?>
        </div>
    <?php elseif (empty($sessions)): ?>
        <div class="alert alert-info">
            Não existem sessões ativas de momento.
        </div>
    <?php else: ?>
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID da Sessão</th>
                        <th>Criada Em</th>
                        <th>Último Acesso</th>
                        <th>Expira Em</th>
                        <th>Conteúdo do Carrinho (Sumário)</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($sessions as $session): ?>
                        <tr>
                            <td><small><?= htmlspecialchars($session['session_id']) ?></small></td>
                            <td><?= htmlspecialchars(format_date($session['created_at'], 'd/m/Y H:i:s')) ?></td>
                            <td><?= htmlspecialchars(format_date($session['last_access'], 'd/m/Y H:i:s')) ?></td>
                            <td><?= htmlspecialchars(format_date($session['expires_at'], 'd/m/Y H:i:s')) ?></td>
                            <td>
                                <?php $cart_list = $session['cart_items_list'] ?? []; ?>
                                <?php if (!empty($cart_list)): ?>
                                    <small><?= implode('<br>', array_map('htmlspecialchars', $cart_list)) ?></small>
                                <?php else: ?>
                                    <small>Vazio</small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <form method="post" action="admin.php?section=sessions&action=delete_session&<?= get_session_id_param() ?>"
                                      onsubmit="return confirm('Tem a certeza que deseja eliminar esta sessão (<?= htmlspecialchars($session['session_id']) ?>) e os seus tokens associados? Esta ação não pode ser desfeita.');"
                                      style="display: inline-block;">
                                    <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                                    <input type="hidden" name="session_id_to_delete" value="<?= htmlspecialchars($session['session_id']) ?>">
                                    <button type="submit" class="btn btn-danger btn-sm" title="Eliminar Sessão">
                                        <i class="bi bi-trash"></i> Eliminar
                                    </button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endif; ?>
</div>