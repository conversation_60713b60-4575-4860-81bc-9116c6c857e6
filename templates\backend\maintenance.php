<?php

require_once __DIR__ . '/../../includes/maintenance_functions.php';
require_once __DIR__ . '/../../includes/order_functions.php';

$download_tokens_stats = get_download_tokens_stats();
$order_access_tokens_stats = get_order_access_tokens_stats();

display_flash_messages();
?>

<h1>Manutenção do Sistema</h1>
<p class="lead">Ferramentas para manutenção e otimização do sistema.</p>
<hr>

<div class="row">
    <!-- Tokens de Download -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Tokens de Download</h5>
                <?php if ($download_tokens_stats['success']): ?>
                <span class="badge bg-info"><?= $download_tokens_stats['total'] ?> tokens</span>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if ($download_tokens_stats['success']): ?>
                <div class="mb-3">
                    <h6>Estatísticas</h6>
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Total de tokens
                            <span class="badge bg-primary rounded-pill"><?= $download_tokens_stats['total'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Tokens expirados
                            <span class="badge bg-warning rounded-pill"><?= $download_tokens_stats['expired'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Tokens utilizados
                            <span class="badge bg-success rounded-pill"><?= $download_tokens_stats['used'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Tokens expirados e utilizados
                            <span class="badge bg-secondary rounded-pill"><?= $download_tokens_stats['expired_used'] ?></span>
                        </li>
                    </ul>
                </div>
                <?php else: ?>
                <div class="alert alert-danger">
                    <?= $download_tokens_stats['message'] ?>
                </div>
                <?php endif; ?>

                <h6>Ações</h6>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-warning" id="cleanup-expired-download-tokens-btn" data-action="cleanup_expired_download_tokens" data-only-used="0">
                        <i class="bi bi-trash"></i> Limpar Tokens Expirados
                    </button>
                    <button type="button" class="btn btn-outline-warning" id="cleanup-expired-used-download-tokens-btn" data-action="cleanup_expired_download_tokens" data-only-used="1">
                        <i class="bi bi-trash"></i> Limpar Apenas Tokens Expirados e Utilizados
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Tokens de Acesso a Encomendas -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Tokens de Acesso a Encomendas</h5>
                <?php if ($order_access_tokens_stats['success']): ?>
                <span class="badge bg-info"><?= $order_access_tokens_stats['total'] ?> tokens</span>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if ($order_access_tokens_stats['success']): ?>
                <div class="mb-3">
                    <h6>Estatísticas</h6>
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Total de tokens
                            <span class="badge bg-primary rounded-pill"><?= $order_access_tokens_stats['total'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Tokens expirados
                            <span class="badge bg-warning rounded-pill"><?= $order_access_tokens_stats['expired'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Tokens em formato antigo
                            <span class="badge bg-danger rounded-pill"><?= $order_access_tokens_stats['old_format'] ?></span>
                        </li>
                    </ul>
                </div>
                <?php else: ?>
                <div class="alert alert-danger">
                    <?= $order_access_tokens_stats['message'] ?>
                </div>
                <?php endif; ?>

                <h6>Ações</h6>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-warning" id="cleanup-expired-order-access-tokens-btn" data-action="cleanup_expired_order_access_tokens">
                        <i class="bi bi-trash"></i> Limpar Tokens Expirados
                    </button>
                    <button type="button" class="btn btn-primary" id="migrate-order-tokens-btn" data-action="migrate_order_tokens">
                        <i class="bi bi-arrow-repeat"></i> Migrar Tokens de Acesso
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Otimização de Base de Dados -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">Otimização de Base de Dados</h5>
            </div>
            <div class="card-body">
                <p>Otimize a base de dados para melhorar o desempenho e reduzir o tamanho do ficheiro.</p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" id="optimize-database-btn" data-action="optimize_database">
                        <i class="bi bi-speedometer"></i> Otimizar Base de Dados
                    </button>
                    <button type="button" class="btn btn-info" id="check-database-integrity-btn" data-action="check_database_integrity">
                        <i class="bi bi-check-circle"></i> Verificar Integridade
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup de Base de Dados -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">Backup de Base de Dados</h5>
            </div>
            <div class="card-body">
                <p>Crie um backup da base de dados para garantir a segurança dos seus dados.</p>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-primary" id="backup-database-btn" data-action="backup_database">
                        <i class="bi bi-download"></i> Criar Backup
                    </button>
                </div>
                <div class="mt-3" id="backup-result"></div>
            </div>
        </div>
    </div>
</div>

<!-- Modal for displaying results -->
<div class="modal fade" id="maintenanceResultModal" tabindex="-1" aria-labelledby="maintenanceResultModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="maintenanceResultModalLabel">Resultado da Operação</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="maintenanceResultContent">
                <!-- Content will be dynamically inserted here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
            </div>
        </div>
    </div>
</div>

<script>
// Define a function to initialize maintenance features
function initMaintenanceFeatures() {

    // Get CSRF token
    const csrfToken = document.querySelector('input[name="csrf_token"]')?.value;

    // Initialize modal
    const resultModalElement = document.getElementById('maintenanceResultModal');
    if (!resultModalElement) {
        console.error('Modal element not found');
        return;
    }

    // Use getOrCreateInstance to avoid issues with multiple 'new' calls
    let resultModal;
    try {
        resultModal = bootstrap.Modal.getOrCreateInstance(resultModalElement);
    } catch (error) {
        console.error('Error initializing modal:', error);
        // Fallback to creating a new instance
        try {
            resultModal = new bootstrap.Modal(resultModalElement);
        } catch (error) {
            console.error('Fatal error initializing modal:', error);
            alert('Erro ao inicializar o modal. Por favor, recarregue a página e tente novamente.');
            return;
        }
    }

    const resultContent = document.getElementById('maintenanceResultContent');

    // Function to handle maintenance actions
    function handleMaintenanceAction(action, params = {}) {

        // Show the modal with loading state
        resultContent.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">A processar...</span>
                </div>
                <p>A executar operação...</p>
            </div>
        `;
        resultModal.show();

        // Set a timeout to prevent the modal from being stuck indefinitely
        const requestTimeout = setTimeout(() => {
            resultContent.innerHTML = `
                <div class="alert alert-warning">
                    <h5>Operação demorada</h5>
                    <p>A operação está demorando mais do que o esperado. Você pode fechar este modal e verificar os resultados mais tarde.</p>
                </div>
            `;
            console.warn('Request timeout reached for action:', action);
        }, 30000); // 30 seconds timeout

        // Prepare request data
        const requestData = new URLSearchParams();
        requestData.append('csrf_token', csrfToken);
        requestData.append('action', action);

        // Add any additional parameters
        for (const key in params) {
            requestData.append(key, params[key]);
        }

        // Make the AJAX request
        fetch('admin.php?section=maintenance&action=' + action + '&' + getSessionIdParam(), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: requestData
        })
        .then(response => {
            // Clear the timeout since we got a response
            clearTimeout(requestTimeout);

            // Check if the response is ok (status in the range 200-299)
            if (!response.ok) {
                throw new Error(`Server responded with status: ${response.status}`);
            }

            // Try to parse the response as JSON
            return response.json();
        })
        .then(data => {
            if (data.success) {
                resultContent.innerHTML = `
                    <div class="alert alert-success">
                        <h5>Operação concluída com sucesso!</h5>
                        <p>${data.message}</p>
                        ${data.details ? `<pre>${JSON.stringify(data.details, null, 2)}</pre>` : ''}
                    </div>
                `;

                // If this is a backup operation with a file path, show download link
                if (action === 'backup_database' && data.backup_file) {
                    const backupFilename = data.backup_file.split('/').pop();
                    resultContent.innerHTML += `
                        <div class="mt-3">
                            <a href="download_backup.php?file=${encodeURIComponent(backupFilename)}&${getSessionIdParam()}"
                               class="btn btn-primary">
                                <i class="bi bi-download"></i> Download Backup
                            </a>
                        </div>
                    `;
                }

                // Add a close button
                resultContent.innerHTML += `
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                    </div>
                `;

                // Show a message that the page will reload
                const reloadMessage = document.createElement('div');
                reloadMessage.className = 'text-center mt-2 text-muted';
                reloadMessage.innerHTML = '<small>A página será atualizada em 2 segundos...</small>';
                resultContent.appendChild(reloadMessage);

                // Refresh the page after 2 seconds to update statistics
                const reloadTimeout = setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                console.warn('Server returned success: false for action:', action, data);
                resultContent.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>Erro na operação</h5>
                        <p>${data.message || 'Ocorreu um erro durante a operação.'}</p>
                    </div>
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                    </div>
                `;
            }
        })
        .catch(error => {
            // Clear the timeout if it's still active
            clearTimeout(requestTimeout);

            console.error('Error in maintenance action:', action, error);

            resultContent.innerHTML = `
                <div class="alert alert-danger">
                    <h5>Erro na operação</h5>
                    <p>Ocorreu um erro durante a operação: ${error.message}</p>
                    <p>Por favor, tente novamente ou contacte o administrador do sistema.</p>
                </div>
                <div class="text-center mt-3">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                </div>
            `;
        });
    }

    // Attach event listeners to all action buttons
    document.querySelectorAll('[data-action]').forEach(button => {

        // Remove any existing event listeners
        button.removeEventListener('click', handleButtonClick);

        // Add new event listener
        button.addEventListener('click', handleButtonClick);
    });

    // Button click handler function
    function handleButtonClick() {
        const action = this.dataset.action;
        const params = {};

        // Add any additional parameters from data attributes
        for (const key in this.dataset) {
            if (key !== 'action') {
                params[key] = this.dataset[key];
            }
        }
        handleMaintenanceAction(action, params);
    }

    // Special handling for migrate tokens button
    const migrateTokensBtn = document.getElementById('migrate-order-tokens-btn');
    if (migrateTokensBtn) {

        // Remove any existing event listeners
        migrateTokensBtn.removeEventListener('click', handleMigrateTokensClick);

        // Add new event listener
        migrateTokensBtn.addEventListener('click', handleMigrateTokensClick);
    }

    // Migrate tokens click handler function
    function handleMigrateTokensClick() {

        // Show the modal with loading state
        resultContent.innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">A processar...</span>
                </div>
                <p>A migrar tokens de acesso...</p>
            </div>
        `;
        resultModal.show();

        // Set a timeout to prevent the modal from being stuck indefinitely
        const requestTimeout = setTimeout(() => {
            resultContent.innerHTML = `
                <div class="alert alert-warning">
                    <h5>Operação demorada</h5>
                    <p>A migração está demorando mais do que o esperado. Você pode fechar este modal e verificar os resultados mais tarde.</p>
                </div>
                <div class="text-center mt-3">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                </div>
            `;
            console.warn('Request timeout reached for migrate tokens action');
        }, 30000); // 30 seconds timeout

        // Make the AJAX request
        fetch('admin.php?section=orders&action=migrate_tokens&' + getSessionIdParam(), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'csrf_token=' + csrfToken
        })
        .then(response => {
            // Clear the timeout since we got a response
            clearTimeout(requestTimeout);

            // Check if the response is ok (status in the range 200-299)
            if (!response.ok) {
                throw new Error(`Server responded with status: ${response.status}`);
            }

            // Try to parse the response as JSON
            return response.json();
        })
        .then(data => {
            if (data.success) {
                resultContent.innerHTML = `
                    <div class="alert alert-success">
                        <h5>Migração concluída com sucesso!</h5>
                        <p>Total de tokens: ${data.total}</p>
                        <p>Tokens migrados: ${data.migrated}</p>
                        <p>Tokens já migrados: ${data.already_migrated}</p>
                        <p>Erros: ${data.errors}</p>
                    </div>
                `;

                // Add a close button
                resultContent.innerHTML += `
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                    </div>
                `;

                // Show a message that the page will reload
                const reloadMessage = document.createElement('div');
                reloadMessage.className = 'text-center mt-2 text-muted';
                reloadMessage.innerHTML = '<small>A página será atualizada em 2 segundos...</small>';
                resultContent.appendChild(reloadMessage);

                // Refresh the page after 2 seconds to update statistics
                const reloadTimeout = setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                console.warn('Server returned success: false for migrate tokens action', data);
                resultContent.innerHTML = `
                    <div class="alert alert-danger">
                        <h5>Erro na migração</h5>
                        <p>${data.message || 'Ocorreu um erro durante a migração dos tokens.'}</p>
                    </div>
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                    </div>
                `;
            }
        })
        .catch(error => {
            // Clear the timeout if it's still active
            clearTimeout(requestTimeout);

            console.error('Error in migrate tokens action:', error);

            resultContent.innerHTML = `
                <div class="alert alert-danger">
                    <h5>Erro na migração</h5>
                    <p>Ocorreu um erro durante a migração dos tokens: ${error.message}</p>
                    <p>Por favor, tente novamente ou contacte o administrador do sistema.</p>
                </div>
                <div class="text-center mt-3">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                </div>
            `;
        });
    }
}

// Initialize on DOMContentLoaded for direct page loads
document.addEventListener('DOMContentLoaded', initMaintenanceFeatures);

// Make the initialization function available globally for AJAX navigation
window.initMaintenanceFeatures = initMaintenanceFeatures;
</script>
