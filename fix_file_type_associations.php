<?php
// This script fixes file type associations by ensuring all digital files have associations

// Include necessary files
require_once __DIR__ . '/includes/init.php';
require_once __DIR__ . '/includes/digital_files_functions.php';

// Check if user is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die("Not authorized");
}

// Set content type to HTML
header('Content-Type: text/html; charset=utf-8');

// Function to fix file type associations
function fix_file_type_associations() {
    $results = [
        'success' => 0,
        'errors' => 0,
        'details' => []
    ];

    // Get all digital files
    $files = db_query("SELECT * FROM digital_files", [], false, true);
    if (!is_array($files) || empty($files)) {
        $results['details'][] = "No digital files found";
        return $results;
    }

    // Get all file types
    $file_types = db_query("SELECT * FROM digital_product_file_types", [], false, true);
    if (!is_array($file_types) || empty($file_types)) {
        $results['details'][] = "No file types found";
        return $results;
    }

    // Extract file type IDs
    $file_type_ids = array_map(function($type) {
        return (int)$type['id'];
    }, $file_types);

    $pdo = get_db_connection();
    if (!$pdo) {
        $results['details'][] = "Failed to get database connection";
        return $results;
    }

    foreach ($files as $file) {
        $file_id = (int)$file['id'];

        try {
            // Check if file has any associations
            $existing_assocs = db_query(
                "SELECT COUNT(*) as count FROM digital_file_type_associations WHERE digital_file_id = :file_id",
                [':file_id' => $file_id],
                true
            );

            if (!$existing_assocs || (int)$existing_assocs['count'] === 0) {
                // No associations found, add all file types
                $pdo->beginTransaction();

                foreach ($file_type_ids as $type_id) {
                    $sql = "INSERT OR IGNORE INTO digital_file_type_associations (
                                digital_file_id, file_type_id, created_at
                            ) VALUES (
                                :digital_file_id, :file_type_id, datetime('now', 'localtime')
                            )";

                    $stmt = $pdo->prepare($sql);
                    $stmt->execute([
                        ':digital_file_id' => $file_id,
                        ':file_type_id' => $type_id
                    ]);
                }

                $pdo->commit();
                $results['success']++;
                $results['details'][] = "Added associations for file ID $file_id";
            } else {
                $results['details'][] = "File ID $file_id already has associations";
            }

            // Now check if any digital products using this file need associations
            $products = db_query(
                "SELECT dp.id FROM digital_products dp WHERE dp.digital_file_id = :file_id",
                [':file_id' => $file_id],
                false,
                true
            );

            if (is_array($products) && !empty($products)) {
                foreach ($products as $product) {
                    $digital_product_id = (int)$product['id'];

                    // Check if product has any associations
                    $existing_product_assocs = db_query(
                        "SELECT COUNT(*) as count FROM digital_product_file_type_associations WHERE digital_product_id = :digital_product_id",
                        [':digital_product_id' => $digital_product_id],
                        true
                    );

                    if (!$existing_product_assocs || (int)$existing_product_assocs['count'] === 0) {
                        // No associations found, add all file types
                        $pdo->beginTransaction();

                        foreach ($file_type_ids as $type_id) {
                            $sql = "INSERT OR IGNORE INTO digital_product_file_type_associations (
                                        digital_product_id, file_type_id, created_at
                                    ) VALUES (
                                        :digital_product_id, :file_type_id, datetime('now', 'localtime')
                                    )";

                            $stmt = $pdo->prepare($sql);
                            $stmt->execute([
                                ':digital_product_id' => $digital_product_id,
                                ':file_type_id' => $type_id
                            ]);
                        }

                        $pdo->commit();
                        $results['success']++;
                        $results['details'][] = "Added associations for digital product ID $digital_product_id";
                    } else {
                        $results['details'][] = "Digital product ID $digital_product_id already has associations";
                    }
                }
            }
        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $results['errors']++;
            $results['details'][] = "Error processing file ID $file_id: " . $e->getMessage();
        }
    }

    return $results;
}

// Run the fix if requested
$results = null;
if (isset($_POST['fix']) && $_POST['fix'] === '1') {
    $results = fix_file_type_associations();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix File Type Associations</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .results { margin-top: 20px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Fix File Type Associations</h1>

        <div class="card mb-4">
            <div class="card-body">
                <h5 class="card-title">What This Does</h5>
                <p class="card-text">
                    <strong>Warning:</strong> This script will check all digital files and add file type associations to those that have none.
                    If a file has no associations, it will add <strong>ALL</strong> available file types to it.
                    This is useful if you want all files to have all file types, but if you want some files to have no file types,
                    do not run this script.
                </p>
                <p class="card-text">
                    It will also check all digital products and ensure they have file type associations.
                </p>

                <form method="post" action="fix_file_type_associations.php">
                    <input type="hidden" name="fix" value="1">
                    <button type="submit" class="btn btn-primary">Fix Associations</button>
                </form>
            </div>
        </div>

        <?php if ($results): ?>
        <div class="results">
            <h2>Results</h2>

            <div class="alert <?= $results['errors'] > 0 ? 'alert-warning' : 'alert-success' ?>">
                <strong>Success:</strong> <?= $results['success'] ?> files/products updated<br>
                <strong>Errors:</strong> <?= $results['errors'] ?>
            </div>

            <h3>Details</h3>
            <pre><?= implode("\n", $results['details']) ?></pre>
        </div>
        <?php endif; ?>

        <div class="mt-4">
            <a href="debug_file_types.php" class="btn btn-secondary">View Debug Info</a>
            <a href="admin.php?section=digital_products" class="btn btn-primary">Back to Digital Products</a>
        </div>
    </div>
</body>
</html>
