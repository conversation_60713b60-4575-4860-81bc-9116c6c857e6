<?php

require_once __DIR__ . '/../../config.php';
require_once __DIR__ . '/../db.php';

function create_digital_products_tables(): bool
{
    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        $pdo->beginTransaction();

        
        $stmt = $pdo->query("PRAGMA table_info(products)");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);

        if (!in_array('product_type', $columns)) {
            $pdo->exec("ALTER TABLE products ADD COLUMN product_type TEXT DEFAULT 'regular'");
        }

        
        $stmt = $pdo->query("PRAGMA table_info(orders)");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);

        if (!in_array('has_digital_products', $columns)) {
            $pdo->exec("ALTER TABLE orders ADD COLUMN has_digital_products INTEGER DEFAULT 0");
        }

        
        $pdo->exec("CREATE TABLE IF NOT EXISTS digital_products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER NOT NULL,
            file_path TEXT,
            expiry_days INTEGER DEFAULT 5,
            download_limit INTEGER DEFAULT 3,
            file_types_json TEXT,
            created_at TEXT,
            updated_at TEXT,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
        )");

        
        $pdo->exec("CREATE TABLE IF NOT EXISTS digital_product_file_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            extension TEXT NOT NULL,
            created_at TEXT DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT DEFAULT (datetime('now', 'localtime'))
        )");

        
        $pdo->exec("CREATE TABLE IF NOT EXISTS digital_product_file_type_associations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            digital_product_id INTEGER NOT NULL,
            file_type_id INTEGER NOT NULL,
            created_at TEXT,
            FOREIGN KEY (digital_product_id) REFERENCES digital_products(id) ON DELETE CASCADE,
            FOREIGN KEY (file_type_id) REFERENCES digital_product_file_types(id) ON DELETE CASCADE
        )");

        
        $pdo->exec("CREATE TABLE IF NOT EXISTS licenses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            license_code TEXT NOT NULL UNIQUE,
            order_id INTEGER,
            order_item_id INTEGER,
            customer_name TEXT NOT NULL,
            customer_email TEXT NOT NULL,
            status TEXT DEFAULT 'waiting_payment',
            expiry_date TEXT,
            download_limit INTEGER DEFAULT 3,
            downloads_used INTEGER DEFAULT 0,
            created_at TEXT,
            updated_at TEXT,
            FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE SET NULL,
            FOREIGN KEY (order_item_id) REFERENCES order_items(id) ON DELETE SET NULL
        )");

        
        $pdo->exec("CREATE TABLE IF NOT EXISTS license_files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            license_id INTEGER NOT NULL,
            digital_product_id INTEGER NOT NULL,
            created_at TEXT,
            FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE,
            FOREIGN KEY (digital_product_id) REFERENCES digital_products(id) ON DELETE CASCADE
        )");

        
        $pdo->exec("CREATE TABLE IF NOT EXISTS downloads (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            license_id INTEGER NOT NULL,
            ip_address TEXT,
            user_agent TEXT,
            download_date TEXT,
            FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE
        )");

        
        $default_file_types = [
            ['name' => 'PDF', 'extension' => 'pdf'],
            ['name' => 'ZIP', 'extension' => 'zip'],
            ['name' => 'MP3', 'extension' => 'mp3'],
            ['name' => 'MP4', 'extension' => 'mp4'],
            ['name' => 'EPUB', 'extension' => 'epub'],
            ['name' => 'DOC/DOCX', 'extension' => 'docx'],
            ['name' => 'XLS/XLSX', 'extension' => 'xlsx'],
            ['name' => 'PPT/PPTX', 'extension' => 'pptx'],
            ['name' => 'JPG/JPEG', 'extension' => 'jpg'],
            ['name' => 'PNG', 'extension' => 'png']
        ];

        $stmt = $pdo->prepare("INSERT INTO digital_product_file_types (name, extension, created_at, updated_at)
                              VALUES (:name, :extension, datetime('now', 'localtime'), datetime('now', 'localtime'))");

        foreach ($default_file_types as $type) {
            
            $check = $pdo->prepare("SELECT id FROM digital_product_file_types WHERE name = :name");
            $check->execute([':name' => $type['name']]);

            if (!$check->fetch()) {
                $stmt->execute([
                    ':name' => $type['name'],
                    ':extension' => $type['extension']
                ]);
            }
        }

        
        $default_settings = [
            'digital_download_expiry_days' => '5',
            'digital_download_limit' => '3',
            'digital_products_directory' => '../digital_products',
            'digital_license_text' => 'Este ficheiro está licenciado apenas para uso pessoal e uso comercial limitado. A revenda ou distribuição não é permitida sem autorização expressa do autor, mesmo que este produto tenha sido obtido de forma gratuita.'
        ];

        $stmt = $pdo->prepare("INSERT OR IGNORE INTO settings (setting_key, setting_value)
                              VALUES (:key, :value)");

        foreach ($default_settings as $key => $value) {
            $stmt->execute([
                ':key' => $key,
                ':value' => $value
            ]);
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

if (create_digital_products_tables()) {
    echo "Digital products tables created successfully.\n";
} else {
    echo "Error creating digital products tables.\n";
}
