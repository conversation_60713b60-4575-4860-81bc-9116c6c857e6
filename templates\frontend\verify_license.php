<?php

require_once __DIR__ . '/../../includes/digital_product_functions.php';

$license_code = '';
$verification_result = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['verify_license'])) {
    $license_code = sanitize_input($_POST['license_code'] ?? '');

    if (empty($license_code)) {
        $verification_result = [
            'valid' => false,
            'message' => 'Por favor, insira um código de licença.'
        ];
    } else {
        $verification_result = check_license_validity($license_code);
    }
}
?>

<div class="max-w-4xl mx-auto px-4 py-8">
    <h1 class="text-2xl font-semibold mb-6">Verificar Licença</h1>

    <div class="bg-gray-900 rounded-lg p-6 mb-6">
        <form method="POST" action="<?= add_session_param_to_url(BASE_URL . '/index.php?view=verify_license') ?>">
            <div class="mb-4">
                <label for="license_code" class="block text-sm font-medium text-gray-300 mb-2">Código de Licença</label>
                <input type="text" id="license_code" name="license_code" value="<?= sanitize_input($license_code) ?>"
                       class="w-full bg-gray-800 border border-gray-700 rounded-md py-2 px-4 text-white focus:outline-none focus:ring-2 focus:ring-primary"
                       placeholder="Ex: XXXX-XXXX-JCS-XXXX-XXXX" required>
            </div>

            <div>
                <button type="submit" name="verify_license" value="1"
                        class="inline-block bg-primary hover:bg-primary/90 text-white py-3 px-6 rounded-button font-medium">
                    <i class="ri-check-line mr-2"></i> Verificar Licença
                </button>
            </div>
        </form>
    </div>

    <?php if ($verification_result !== null): ?>
        <div class="bg-gray-900 rounded-lg p-6">
            <h2 class="text-xl font-medium mb-4">Resultado da Verificação</h2>

            <?php if ($verification_result['valid']): ?>
                <div class="bg-green-900/50 border border-green-800 text-green-100 px-4 py-3 rounded mb-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 flex-shrink-0 flex items-center justify-center bg-green-800 rounded-full mr-3">
                            <i class="ri-check-line text-lg"></i>
                        </div>
                        <div>
                            <p class="font-bold">Licença Válida</p>
                            <p class="text-sm">Esta licença é válida e está ativa.</p>
                        </div>
                    </div>
                </div>

                <?php
                $license = $verification_result['license'];
                
                $censored_name = get_censored_string($license['customer_name']);
                $censored_email = get_censored_email($license['customer_email']);
                ?>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <p class="text-gray-400 text-sm mb-1">Licenciado para:</p>
                        <p class="font-medium"><?= sanitize_input($censored_name) ?></p>
                    </div>
                    <div>
                        <p class="text-gray-400 text-sm mb-1">Email:</p>
                        <p class="font-medium"><?= sanitize_input($censored_email) ?></p>
                    </div>
                    <div>
                        <p class="text-gray-400 text-sm mb-1">Status:</p>
                        <p class="font-medium">
                            <span class="inline-block px-2 py-1 text-xs rounded bg-green-900 text-green-100">Ativo</span>
                        </p>
                    </div>
                    <div>
                        <p class="text-gray-400 text-sm mb-1">Data de Expiração:</p>
                        <p class="font-medium"><?= date('d/m/Y', strtotime($license['expiry_date'])) ?></p>
                    </div>
                </div>

                <p class="text-sm text-gray-400">
                    <i class="ri-information-line mr-1"></i>
                    Por razões de privacidade, algumas informações estão parcialmente ocultas.
                </p>
            <?php else: ?>
                <div class="bg-red-900/50 border border-red-800 text-red-100 px-4 py-3 rounded mb-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 flex-shrink-0 flex items-center justify-center bg-red-800 rounded-full mr-3">
                            <i class="ri-close-line text-lg"></i>
                        </div>
                        <div>
                            <p class="font-bold">Licença Inválida</p>
                            <p class="text-sm"><?= sanitize_input($verification_result['message']) ?></p>
                        </div>
                    </div>
                </div>

                <p class="text-sm text-gray-400">
                    <i class="ri-information-line mr-1"></i>
                    Se você acredita que isso é um erro, entre em contato com o suporte.
                </p>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>
