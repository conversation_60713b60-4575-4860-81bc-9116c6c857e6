<?php

require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/stock_functions.php';

$order_item_id = 60;

$pdo = get_db_connection();
$stmt = $pdo->prepare("
    SELECT oi.id, oi.product_id, oi.variation_id, oi.quantity, oi.stock_reduced,
           p.product_type, p.stock as product_stock
    FROM order_items oi
    JOIN products p ON oi.product_id = p.id
    WHERE oi.id = ?
");
$stmt->execute([$order_item_id]);
$item = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$item) {
    echo "Order item not found: {$order_item_id}\n";
    exit;
}

echo "Before stock reduction:\n";
echo "Order Item ID: {$order_item_id}\n";
echo "Product ID: {$item['product_id']}\n";
echo "Variation ID: {$item['variation_id']}\n";
echo "Quantity: {$item['quantity']}\n";
echo "Stock Reduced Flag: {$item['stock_reduced']}\n";
echo "Product Type: {$item['product_type']}\n";

if (!empty($item['variation_id'])) {
    $stmt = $pdo->prepare("SELECT id, product_id, stock FROM product_variations WHERE id = ?");
    $stmt->execute([$item['variation_id']]);
    $variation = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($variation) {
        echo "Variation Stock Before: {$variation['stock']}\n";
    } else {
        echo "Variation not found with ID: {$item['variation_id']}\n";
    }
}

echo "\nAttempting to reduce stock...\n";
$result = reduce_order_item_stock($order_item_id);

echo "Stock reduction result: " . ($result ? "Success" : "Failed") . "\n\n";

$stmt = $pdo->prepare("
    SELECT oi.id, oi.product_id, oi.variation_id, oi.quantity, oi.stock_reduced
    FROM order_items oi
    WHERE oi.id = ?
");
$stmt->execute([$order_item_id]);
$item_after = $stmt->fetch(PDO::FETCH_ASSOC);

echo "After stock reduction:\n";
echo "Product ID: {$item_after['product_id']}\n";
echo "Variation ID: {$item_after['variation_id']}\n";
echo "Stock Reduced Flag: {$item_after['stock_reduced']}\n";

if (!empty($item_after['variation_id'])) {
    $stmt = $pdo->prepare("SELECT id, product_id, stock FROM product_variations WHERE id = ?");
    $stmt->execute([$item_after['variation_id']]);
    $variation_after = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($variation_after) {
        echo "Variation Stock After: {$variation_after['stock']}\n";
    } else {
        echo "Variation not found with ID: {$item_after['variation_id']}\n";
    }
}

echo "\nDone.\n";
