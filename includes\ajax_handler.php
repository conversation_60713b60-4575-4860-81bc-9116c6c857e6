<?php

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/db.php';
require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/product_functions.php';
require_once __DIR__ . '/coupon_functions.php';
require_once __DIR__ . '/session.php';
require_once __DIR__ . '/vat_functions.php';

$current_session_id = start_cookieless_session();

ob_start();

header('Content-Type: application/json');

$action = $_REQUEST['action'] ?? null;

function json_response($success, $data = []) {

    while (ob_get_level() > 0) {
        ob_end_clean();
    }


    ob_start();

    header('Content-Type: application/json');
    $response = ['success' => (bool)$success];

    if (is_string($data)) {

        if (!$success) {
            $response['error'] = $data;
        } else {
            $response['message'] = $data;
        }
    } elseif (is_array($data)) {
        $response = array_merge($response, $data);
    }


    array_walk_recursive($response, function(&$value) {
        if (is_string($value) && strpos($value, '<') !== false) {

        }
    });


    $json_result = json_encode($response, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP | JSON_UNESCAPED_UNICODE);

    if ($json_result === false) {

        $json_error = json_last_error();
        $json_error_msg = json_last_error_msg();


        foreach ($response as $key => $value) {
            if (is_string($value)) {
                $test_encode = json_encode($value);
                if ($test_encode === false) {

                    unset($response[$key]);
                }
            }
        }


        $response = [
            'success' => false,
            'error' => 'JSON encoding error: ' . $json_error_msg,
            'html' => '<p class="text-sm text-gray-400 text-center py-4">Erro ao processar resposta.</p>',
            'item_count' => 0,
            'subtotal_formatted' => '€0,00'
        ];

        $json_result = json_encode($response);
        if ($json_result === false) {

            echo '{"success":false,"error":"Critical JSON encoding error"}';
            exit;
        }
    }


    $output = ob_get_clean();
    if (!empty($output)) {

        ob_start();
    }

    echo $json_result;


    if (ob_get_level() > 0) {
        ob_end_flush();
    }

    exit;
}

switch ($action) {

    case 'request_license_extension':
        require_once __DIR__ . '/ajax/license_requests.php';
        $response = handle_license_extension_request($_POST);
        json_response($response['success'], $response);
        break;


    case 'request_additional_downloads':
        require_once __DIR__ . '/ajax/license_requests.php';
        $response = handle_additional_downloads_request($_POST);
        json_response($response['success'], $response);
        break;


    case 'get_cart_preview':
        try {
            require_once __DIR__ . '/digital_order_functions.php';


            $cart_items = $_SESSION['cart'] ?? [];
            $item_count = 0;
            $subtotal = 0.0;
            $currency_symbol = get_setting('currency_symbol', '€');
            $html = '';

            // Count digital products in cart
            $digital_products_count = count_digital_products_in_cart($cart_items);



if (empty($cart_items)) {
    $html = '<p class="text-sm text-gray-400 text-center py-4">Seu carrinho está vazio.</p>';
} else {
    foreach ($cart_items as $key => $item) {
        try {



            if (isset($item['attributes_display'])) {
            } else {
            }


            if (!isset($item['product_id']) || !isset($item['quantity']) || !isset($item['price'])) {
                continue;
            }



            $item_total = $item['price'] * $item['quantity'];
            $subtotal += $item_total;
            $item_count += $item['quantity'];


            $html .= '<div class="flex items-center py-2 border-b border-gray-800 last:border-b-0">';


            $image_html = '';
            try {
                $image_sql = "SELECT filename FROM product_images
                             WHERE product_id = ?
                             ORDER BY sort_order ASC, id ASC LIMIT 1";
                $image_result = db_query($image_sql, [$item['product_id']], true);

                if ($image_result && !empty($image_result['filename'])) {
                    $image_url = get_product_image_url($image_result['filename']);
                    $image_html = '<div class="w-12 h-12 bg-gray-800 rounded flex-shrink-0 overflow-hidden">';
                    $image_html .= '<img src="' . $image_url . '" alt="' . sanitize_input($item['name'] ?? 'Produto') . '" class="w-full h-full object-cover">';
                    $image_html .= '</div>';
                }
            } catch (Exception $img_e) {
            }


            if (empty($image_html)) {
                $html .= '<div class="w-12 h-12 bg-gray-800 rounded flex-shrink-0 flex items-center justify-center">';
                $html .= '<i class="ri-shopping-bag-line text-gray-400"></i>';
                $html .= '</div>';
            } else {
                $html .= $image_html;
            }


            $html .= '<div class="ml-3 flex-1 min-w-0">';
            $html .= '<p class="text-sm font-medium truncate">' . sanitize_input($item['name'] ?? 'Produto') . '</p>';


            if (!empty($item['sku'])) {
                $html .= '<p class="text-xs text-gray-500">SKU: ' . sanitize_input($item['sku']) . '</p>';
            }


            if (!empty($item['attributes_display'])) {
                $html .= '<p class="text-xs text-gray-400">' . sanitize_input($item['attributes_display']) . '</p>';
            } elseif (isset($item['variation_id']) && $item['variation_id'] !== null) {

                require_once __DIR__ . '/product_functions.php';
                $attributes_display = get_variation_attribute_string($item['variation_id']);
                if (!empty($attributes_display)) {
                    $html .= '<p class="text-xs text-gray-400">' . sanitize_input($attributes_display) . '</p>';
                } else {
                }
            } else {
            }

            $html .= '<p class="text-xs text-gray-400">' . $item['quantity'] . ' × ' . format_price($item['price'], $currency_symbol) . '</p>';
            $html .= '</div>';


            $html .= '<button class="remove-preview-item text-gray-500 hover:text-red-500 ml-2 flex-shrink-0" ';
            $html .= 'data-product-id="' . $item['product_id'] . '" ';


            if ($item['variation_id'] !== null) {
                $html .= 'data-variation-id="' . $item['variation_id'] . '" ';

                $html .= 'data-cart-key="' . $item['product_id'] . '-' . $item['variation_id'] . '" ';
            } else {
                $html .= 'data-variation-id="" ';
                $html .= 'data-cart-key="' . $item['product_id'] . '" ';
            }

            $html .= 'title="Remover">';
            $html .= '<div class="w-5 h-5 flex items-center justify-center"><i class="ri-close-line"></i></div>';
            $html .= '</button>';

            $html .= '</div>';
        } catch (Exception $item_e) {

        }
    }
}


            if (empty($html) && $item_count > 0) {
                $html = '<p class="text-sm text-gray-400 text-center py-4">Carrinho com ' . $item_count . ' item(s).</p>';
            }



            $html = preg_replace('/[\x00-\x1F\x7F]/', '', $html);




            $subtotal_formatted = format_price($subtotal, $currency_symbol);

            json_response(true, [
                'html' => $html,
                'item_count' => $item_count,
                'subtotal_formatted' => $subtotal_formatted,
                'digital_products_count' => $digital_products_count
            ]);
        } catch (Exception $e) {


            $item_count = 0;
            if (isset($_SESSION['cart']) && is_array($_SESSION['cart'])) {
                foreach ($_SESSION['cart'] as $item) {
                    $item_count += isset($item['quantity']) ? (int)$item['quantity'] : 0;
                }
            }


            $currency_symbol = get_setting('currency_symbol', '€');


            // Try to count digital products even in error case
            $digital_products_count = 0;
            try {
                require_once __DIR__ . '/digital_order_functions.php';
                $digital_products_count = count_digital_products_in_cart($_SESSION['cart'] ?? []);
            } catch (Exception $countEx) {
                // Ignore errors in counting digital products
            }

            json_response(false, [
                'error' => 'Erro ao carregar carrinho: ' . $e->getMessage(),
                'html' => '<p class="text-sm text-gray-400 text-center py-4">Erro ao carregar carrinho.</p>',
                'item_count' => $item_count,
                'subtotal_formatted' => format_price(0, $currency_symbol),
                'digital_products_count' => $digital_products_count
            ]);
        }
        break;


    case 'add_to_cart':

        require_once __DIR__ . '/custom_field_functions.php';
        require_once __DIR__ . '/digital_order_functions.php';


        $input_data = [];
        $custom_fields_data = [];
        $custom_field_files = [];


        if (isset($_SERVER['CONTENT_TYPE']) && strpos($_SERVER['CONTENT_TYPE'], 'multipart/form-data') !== false) {
            $input_data = $_POST;


            if (isset($_POST['custom_fields'])) {
                $custom_fields_data = json_decode($_POST['custom_fields'], true) ?? [];
            }


            foreach ($_FILES as $key => $file) {
                if (strpos($key, 'custom_field_file_') === 0) {
                    $field_id = (int)str_replace('custom_field_file_', '', $key);
                    $custom_field_files[$field_id] = $file;
                }
            }
        } elseif (isset($_SERVER['CONTENT_TYPE']) && stripos($_SERVER['CONTENT_TYPE'], 'application/json') !== false) {
            $json_body = file_get_contents('php://input');
            $input_data = json_decode($json_body, true);


            if (isset($input_data['custom_fields'])) {
                $custom_fields_data = $input_data['custom_fields'];
                unset($input_data['custom_fields']);
            }
        } else {

            $input_data = $_POST;
        }

        $product_id = filter_var($input_data['product_id'] ?? null, FILTER_VALIDATE_INT);
        $quantity = filter_var($input_data['quantity'] ?? null, FILTER_VALIDATE_INT);
        $variation_id = filter_var($input_data['variation_id'] ?? null, FILTER_VALIDATE_INT);

        if (!$product_id || !$quantity || $quantity < 1) {
            json_response(false, 'Dados inválidos.');
            break;
        }


        $product_sql = "SELECT p.*, v.rate as vat_rate, v.description as vat_description
                        FROM products p
                        LEFT JOIN vat_rates v ON p.vat_rate_id = v.id
                        WHERE p.id = ? AND p.is_active = 1";
        $product = db_query($product_sql, [$product_id], true);

        if (!$product) {
            json_response(false, 'Produto não encontrado ou indisponível.');
            break;
        }


        if ($product['product_type'] === 'regular' || $product['product_type'] === 'digital') {

            $cart_key = (string)$product_id;
            $current_cart_qty = $_SESSION['cart'][$cart_key]['quantity'] ?? 0;
            $requested_total_qty = $current_cart_qty + $quantity;
            $final_price = $product['base_price'];
            $product_sku = $product['sku'] ?? ($product['product_type'] === 'digital' ? 'DIGITAL-' . $product_id : 'PRODUCT-' . $product_id);

            // Check if this is a digital product and if we're already at the limit
            if ($product['product_type'] === 'digital') {
                // Count existing digital products in cart
                $digital_products_count = count_digital_products_in_cart($_SESSION['cart'] ?? []);

                // If this product is not already in the cart, it would add to the count
                if ($current_cart_qty == 0) {
                    $digital_products_count++;
                }

                // Check if adding this would exceed the limit
                if ($digital_products_count > 10) {
                    json_response(false, 'Não é possível adicionar mais de 10 produtos digitais ao carrinho. Por favor, finalize a compra atual antes de adicionar mais produtos digitais.');
                    break;
                }
            }

            if ($product['product_type'] === 'digital') {
                if ($current_cart_qty > 0 && $quantity > 0) {
                    json_response(false, 'Produtos digitais são limitados a 1 unidade por pedido.');
                    break;
                }
                $quantity = 1;
                $requested_total_qty = 1;
            } else {
                if ($product['stock'] < $requested_total_qty) {
                    $available = $product['stock'] - $current_cart_qty;
                    $message = $available > 0 ? 'Apenas ' . $available . ' unidade(s) adicional(is) disponível(is) em stock.' : 'Não há mais stock disponível para este item.';
                    json_response(false, $message);
                    break;
                }
            }


            $processed_custom_fields = [];
            $custom_fields_price_modifier = 0;
            $product_custom_fields_db = get_product_custom_fields($product_id);

            $submitted_text_fields_map_simple = [];
            if (!empty($custom_fields_data) && is_array($custom_fields_data)) {
                foreach ($custom_fields_data as $submitted_field) {
                    if (isset($submitted_field['field_id'])) {
                        $submitted_text_fields_map_simple[(int)$submitted_field['field_id']] = $submitted_field;
                    }
                }
            }

            foreach ($product_custom_fields_db as $field) {
                $field_id = (int)$field['id'];
                $is_required = ((int)$field['is_required'] === 1);
                $field_type = $field['type_slug'];
                $field_name = $field['name'];
                $price_modifier_value = $field['price_modifier_override'] !== null ? (float)$field['price_modifier_override'] : (float)$field['price_modifier'];

                if ($field_type === 'custom-text') {
                    $submitted_data = $submitted_text_fields_map_simple[$field_id] ?? null;
                    $text_value = isset($submitted_data['text_value']) ? trim($submitted_data['text_value']) : '';
                    if (!empty($text_value)) {
                        $custom_fields_price_modifier += $price_modifier_value;
                        $processed_custom_fields[] = ['field_id' => $field_id, 'field_name' => $field_name, 'field_type' => $field_type, 'text_value' => $text_value, 'font_id' => $submitted_data['font_id'] ?? null, 'price_modifier' => $price_modifier_value];
                    } elseif ($is_required) {
                        json_response(false, "Campo de texto personalizado '{$field_name}' obrigatório não preenchido.");
                        break 2;
                    }
                } elseif ($field_type === 'file-upload') {
                    $uploaded_file_data = $custom_field_files[$field_id] ?? null;
                    if ($uploaded_file_data && $uploaded_file_data['error'] === UPLOAD_ERR_OK) {
                        $file_info = handle_custom_field_file_upload($uploaded_file_data, $product_id, $field_id);
                        if ($file_info === false) {
                            json_response(false, "Erro ao processar o ficheiro '{$uploaded_file_data['name']}' carregado para '{$field_name}'.");
                            break 2;
                        }
                        $custom_fields_price_modifier += $price_modifier_value;
                        $processed_custom_fields[] = ['field_id' => $field_id, 'field_name' => $field_name, 'field_type' => $field_type, 'file_path' => $file_info['file_path'], 'file_name' => $file_info['original_name'], 'price_modifier' => $price_modifier_value];
                    } elseif ($is_required) {
                        json_response(false, "Upload de ficheiro obrigatório '{$field_name}' não realizado.");
                        break 2;
                    }
                }
            }
            $final_price += $custom_fields_price_modifier;


            if (!empty($processed_custom_fields)) {
                $cart_key = $product_id . '-' . md5(json_encode($processed_custom_fields));
            }

            $vat_rate = $product['vat_rate'] ?? null;
            $vat_description = $product['vat_description'] ?? null;
            if ($vat_rate === null) {
                $default_vat = get_default_vat_rate();
                $vat_rate = $default_vat ? $default_vat['rate'] : get_setting('tax_rate_percent', 23.0);
                $vat_description = $default_vat ? $default_vat['description'] : 'Taxa Normal';
            }

            if (isset($_SESSION['cart'][$cart_key])) {
                $_SESSION['cart'][$cart_key]['quantity'] = $requested_total_qty;
            } else {
                $_SESSION['cart'][$cart_key] = [
                    'product_id' => $product_id,
                    'variation_id' => null,
                    'quantity' => $quantity,
                    'name' => $product['name_pt'],
                    'sku' => $product_sku,
                    'price' => $final_price,
                    'attributes_display' => '',
                    'product_slug' => $product['slug'],
                    'vat_rate' => $vat_rate,
                    'vat_description' => $vat_description,
                    'product_type' => $product['product_type'],
                    'custom_fields' => $processed_custom_fields
                ];
            }
        } elseif ($product['product_type'] === 'variation') {

            if (!$variation_id) {
                $single_variation_sql = "SELECT id FROM product_variations WHERE product_id = ? AND is_active = 1";
                $variations = db_query($single_variation_sql, [$product_id], false, true);
                if ($variations && count($variations) === 1) {
                    $variation_id = $variations[0]['id'];
                } else {
                    json_response(false, 'Por favor, selecione as opções do produto.');
                    break;
                }
            }


            $variation_sql = "SELECT pv.*, p.name_pt as product_name, p.base_price, p.slug as product_slug,
                              p.vat_rate_id, v.rate as vat_rate, v.description as vat_description, p.product_type
                              FROM product_variations pv
                              JOIN products p ON pv.product_id = p.id
                              LEFT JOIN vat_rates v ON p.vat_rate_id = v.id
                              WHERE pv.product_id = ? AND pv.id = ? AND pv.is_active = 1 AND p.is_active = 1";
            $variation = db_query($variation_sql, [$product_id, $variation_id], true);

            if (!$variation) {
                 json_response(false, 'Produto ou variação não encontrada ou indisponível.');
                 break;
            }


            $cart_key = $product_id . '-' . $variation_id;


            $current_cart_qty = $_SESSION['cart'][$cart_key]['quantity'] ?? 0;
            $requested_total_qty = $current_cart_qty + $quantity;


            if ($variation['product_type'] === 'digital') {
                 json_response(false, 'Tipo de produto inválido para variação.');
                 break;
            } else if ($variation['stock'] < $requested_total_qty) {
                $available = $variation['stock'] - $current_cart_qty;
                $message = $available > 0 ? 'Apenas ' . $available . ' unidade(s) adicional(is) disponível(is) em stock.' : 'Não há mais stock disponível para este item.';
                json_response(false, $message);
                break;
            }


            $final_price = calculate_variation_price($product_id, $variation_id);
            if ($final_price === false) {
                 json_response(false, 'Erro ao calcular preço da variação.');
                 break;
            }


            $processed_custom_fields = [];
            $custom_fields_price_modifier = 0;
            $product_custom_fields_db = get_product_custom_fields($product_id);

            $submitted_text_fields_map_variant = [];
             if (!empty($custom_fields_data) && is_array($custom_fields_data)) {
                 foreach ($custom_fields_data as $submitted_field) {
                     if (isset($submitted_field['field_id'])) {
                         $submitted_text_fields_map_variant[(int)$submitted_field['field_id']] = $submitted_field;
                     }
                 }
             }

            foreach ($product_custom_fields_db as $field) {
                $field_id = (int)$field['id'];
                $is_required = ((int)$field['is_required'] === 1);
                $field_type = $field['type_slug'];
                $field_name = $field['name'];
                $price_modifier_value = $field['price_modifier_override'] !== null ? (float)$field['price_modifier_override'] : (float)$field['price_modifier'];

                if ($field_type === 'custom-text') {
                    $submitted_data = $submitted_text_fields_map_variant[$field_id] ?? null;
                    $text_value = isset($submitted_data['text_value']) ? trim($submitted_data['text_value']) : '';
                    if (!empty($text_value)) {
                        $custom_fields_price_modifier += $price_modifier_value;
                        $processed_custom_fields[] = ['field_id' => $field_id, 'field_name' => $field_name, 'field_type' => $field_type, 'text_value' => $text_value, 'font_id' => $submitted_data['font_id'] ?? null, 'price_modifier' => $price_modifier_value];
                    } elseif ($is_required) {
                        json_response(false, "Campo de texto personalizado '{$field_name}' obrigatório não preenchido.");
                        break 2;
                    }
                } elseif ($field_type === 'file-upload') {
                    $uploaded_file_data = $custom_field_files[$field_id] ?? null;
                    if ($uploaded_file_data && $uploaded_file_data['error'] === UPLOAD_ERR_OK) {
                        $file_info = handle_custom_field_file_upload($uploaded_file_data, $product_id, $field_id);
                        if ($file_info === false) {
                            json_response(false, "Erro ao processar o ficheiro '{$uploaded_file_data['name']}' carregado para '{$field_name}'.");
                            break 2;
                        }
                        $custom_fields_price_modifier += $price_modifier_value;
                        $processed_custom_fields[] = ['field_id' => $field_id, 'field_name' => $field_name, 'field_type' => $field_type, 'file_path' => $file_info['file_path'], 'file_name' => $file_info['original_name'], 'price_modifier' => $price_modifier_value];
                    } elseif ($is_required) {
                        json_response(false, "Upload de ficheiro obrigatório '{$field_name}' não realizado.");
                        break 2;
                    }
                }
            }
            $final_price += $custom_fields_price_modifier;
            $attributes_display = get_variation_attribute_string($variation_id);


            if (!empty($processed_custom_fields)) {
                 $cart_key = $product_id . '-' . $variation_id . '-' . md5(json_encode($processed_custom_fields));
            }

            $_SESSION['cart'] = $_SESSION['cart'] ?? [];
            if (isset($_SESSION['cart'][$cart_key])) {
                $_SESSION['cart'][$cart_key]['quantity'] = $requested_total_qty;
            } else {
                $vat_rate = $variation['vat_rate'] ?? null;
                $vat_description = $variation['vat_description'] ?? null;
                if ($vat_rate === null) {
                    $default_vat = get_default_vat_rate();
                    $vat_rate = $default_vat ? $default_vat['rate'] : get_setting('tax_rate_percent', 23.0);
                    $vat_description = $default_vat ? $default_vat['description'] : 'Taxa Normal';
                }
                $_SESSION['cart'][$cart_key] = [
                    'product_id' => $product_id,
                    'variation_id' => $variation_id,
                    'quantity' => $quantity,
                    'name' => $variation['product_name'],
                    'sku' => $variation['sku'],
                    'price' => $final_price,
                    'attributes_display' => $attributes_display,
                    'product_slug' => $variation['product_slug'],
                    'vat_rate' => $vat_rate,
                    'vat_description' => $vat_description,
                    'product_type' => $variation['product_type'],
                    'custom_fields' => $processed_custom_fields
                ];
            }
        } else {
             json_response(false, 'Tipo de produto desconhecido ou configuração inválida.');
             break;
        }


        if (session_status() === PHP_SESSION_ACTIVE) {
            session_write_close();
            session_start();
        }
        $cart_totals = calculate_cart_totals();
        json_response(true, [
            'message' => 'Item adicionado ao carrinho!',
            'item_count' => $cart_totals['total_items'],
            'cart_key' => $cart_key
        ]);
        break;


    case 'update_cart_item':
        $input_data = [];
        if (isset($_SERVER['CONTENT_TYPE']) && stripos($_SERVER['CONTENT_TYPE'], 'application/json') !== false) {
            $json_body = file_get_contents('php://input');
            $input_data = json_decode($json_body, true);
        } else {
            $input_data = $_POST;
        }


        $cart_key = isset($input_data['cart_key']) ? htmlspecialchars($input_data['cart_key'], ENT_QUOTES, 'UTF-8') :
                   (isset($input_data['item_key']) ? htmlspecialchars($input_data['item_key'], ENT_QUOTES, 'UTF-8') : null);


        $new_quantity = filter_var($input_data['quantity'] ?? null, FILTER_VALIDATE_INT);

        if ($new_quantity === false || $new_quantity < 0) {
            json_response(false, 'Dados inválidos.');
            break;
        }


        if ($cart_key === '') {

            if (!isset($_SESSION['cart'][''])) {



                $found_key = null;
                foreach ($_SESSION['cart'] as $key => $item) {
                    if (isset($item['product_id']) && $item['product_id'] == $input_data['product_id'] &&
                        (!isset($item['variation_id']) || $item['variation_id'] === null)) {
                        $found_key = $key;
                        break;
                    }
                }

                if ($found_key !== null) {
                    $cart_key = $found_key;
                } else {
                    json_response(false, 'Item não encontrado no carrinho.');
                    break;
                }
            }
        }

        else if (!$cart_key || !isset($_SESSION['cart'][$cart_key])) {



            $product_id = $input_data['product_id'] ?? null;
            $variation_id = $input_data['variation_id'] ?? null;

            if ($product_id) {
                $found_key = null;
                foreach ($_SESSION['cart'] as $key => $item) {
                    if (isset($item['product_id']) && $item['product_id'] == $product_id) {
                        if ($variation_id && isset($item['variation_id']) && $item['variation_id'] == $variation_id) {
                            $found_key = $key;
                            break;
                        } else if (!$variation_id && (!isset($item['variation_id']) || $item['variation_id'] === null)) {
                            $found_key = $key;
                            break;
                        }
                    }
                }

                if ($found_key !== null) {
                    $cart_key = $found_key;
                } else {
                    json_response(false, 'Item não encontrado no carrinho.');
                    break;
                }
            } else {
                json_response(false, 'Item não encontrado no carrinho.');
                break;
            }
        }

        $item = $_SESSION['cart'][$cart_key];
        $removed = false;

        if ($new_quantity === 0) {

            unset($_SESSION['cart'][$cart_key]);
            $removed = true;
        } else {

            if ($item['variation_id'] !== null) {

                $stock_check = db_query("SELECT pv.stock, p.product_type FROM product_variations pv JOIN products p ON pv.product_id = p.id WHERE pv.id = ?", [$item['variation_id']], true);
                $stock = $stock_check['stock'] ?? 0;
                $product_type = $stock_check['product_type'] ?? '';
            } else {

                $stock_check = db_query("SELECT stock, product_type FROM products WHERE id = ?", [$item['product_id']], true);
                $stock = $stock_check['stock'] ?? 0;
                $product_type = $stock_check['product_type'] ?? '';
            }


            if ($product_type === 'digital') {

                if ($new_quantity > 1) {
                    json_response(false, [
                        'error' => 'Produtos digitais são limitados a 1 unidade por pedido.',
                        'original_quantity' => $item['quantity']
                    ]);
                    break;
                }
            } else if ($stock < $new_quantity) {
                 json_response(false, [
                     'error' => 'Quantidade solicitada indisponível (' . $stock . ' em stock).',
                     'original_quantity' => $item['quantity']
                 ]);
                 break;
            }
            $_SESSION['cart'][$cart_key]['quantity'] = $new_quantity;
        }


        if (session_status() === PHP_SESSION_ACTIVE) {
            session_write_close();
            session_start();
        }


        $cart_totals = calculate_cart_totals();
        $currency_symbol = get_setting('currency_symbol', '€');


        $item_price = '';
        if (!$removed) {
            $item_price = format_price($item['price'] * $new_quantity, $currency_symbol);
        }

        json_response(true, [
            'message' => $removed ? 'Item removido.' : 'Quantidade atualizada.',
            'removed' => $removed,
            'item_count' => $cart_totals['total_items'],
            'subtotal' => $cart_totals['subtotal'],
            'subtotal_formatted' => format_price($cart_totals['subtotal'], $currency_symbol),
            'tax' => $cart_totals['tax_amount'],
            'shipping' => $cart_totals['shipping_cost'],
            'total' => $cart_totals['grand_total'],
            'total_formatted' => format_price($cart_totals['grand_total'], $currency_symbol),
            'discount' => $cart_totals['discount_amount'],
            'item_price' => $item_price,
            'vat_groups' => $cart_totals['vat_groups'] ?? [],
            'min_order_value' => $cart_totals['min_order_value'],
            'free_shipping_threshold' => $cart_totals['free_shipping_threshold'],
            'meets_min_order' => $cart_totals['meets_min_order'],
            'has_free_shipping' => $cart_totals['has_free_shipping'],
            'grand_total' => $cart_totals['grand_total']
        ]);
        break;


    case 'get_cart_count':
        try {
            require_once __DIR__ . '/digital_order_functions.php';


            $cart_items = $_SESSION['cart'] ?? [];
            $item_count = 0;


            foreach ($cart_items as $item) {
                $item_count += $item['quantity'] ?? 0;
            }

            // Count digital products in cart
            $digital_products_count = count_digital_products_in_cart($cart_items);


            json_response(true, [
                'item_count' => $item_count,
                'digital_products_count' => $digital_products_count
            ]);
        } catch (Exception $e) {
            json_response(false, [
                'error' => 'Erro ao contar itens do carrinho: ' . $e->getMessage(),
                'item_count' => 0,
                'digital_products_count' => 0
            ]);
        }
        break;


    case 'clear_cart':

        $_SESSION['cart'] = [];
        unset($_SESSION['cart_discount']);
        unset($_SESSION['applied_promo_code']);
        unset($_SESSION['applied_coupon_id']);


        session_write_close();
        session_start();


        $min_order_value = (float)get_setting('min_order_value', 0);
        $free_shipping_threshold = (float)get_setting('free_shipping_threshold', 0);


        json_response(true, [
            'message' => 'Carrinho limpo.',
            'item_count' => 0,
            'subtotal' => 0,
            'subtotal_formatted' => format_price(0),
            'tax' => 0,
            'tax_formatted' => format_price(0),
            'shipping' => 0,
            'shipping_formatted' => 'Grátis',
            'total' => 0,
            'total_formatted' => format_price(0),
            'min_order_value' => $min_order_value,
            'free_shipping_threshold' => $free_shipping_threshold,
            'meets_min_order' => ($min_order_value <= 0),
            'has_free_shipping' => ($free_shipping_threshold <= 0),
            'grand_total' => 0
        ]);
        break;


    case 'apply_promo_code':
        $input_data = [];
        if (isset($_SERVER['CONTENT_TYPE']) && stripos($_SERVER['CONTENT_TYPE'], 'application/json') !== false) {
            $json_body = file_get_contents('php://input');
            $input_data = json_decode($json_body, true);
        } else {
            $input_data = $_POST;
        }

        $promo_code = strtoupper(trim(isset($input_data['promo_code']) ? htmlspecialchars($input_data['promo_code'], ENT_QUOTES, 'UTF-8') : ''));
        $response_data = apply_promo_code($promo_code);


        $cart_totals = calculate_cart_totals();
        $currency_symbol = get_setting('currency_symbol', '€');
        $response_data = array_merge($response_data, [
             'subtotal' => $cart_totals['subtotal'],
             'subtotal_formatted' => format_price($cart_totals['subtotal'], $currency_symbol),
             'tax' => $cart_totals['tax_amount'],
             'shipping' => $cart_totals['shipping_cost'],
             'total' => $cart_totals['grand_total'],
             'total_formatted' => format_price($cart_totals['grand_total'], $currency_symbol),
             'discount_amount' => $cart_totals['discount_amount'],
             'discount_formatted' => format_price($cart_totals['discount_amount'], $currency_symbol),
             'vat_groups' => $cart_totals['vat_groups'] ?? [],
             'min_order_value' => $cart_totals['min_order_value'],
             'free_shipping_threshold' => $cart_totals['free_shipping_threshold'],
             'meets_min_order' => $cart_totals['meets_min_order'],
             'has_free_shipping' => $cart_totals['has_free_shipping'],
             'grand_total' => $cart_totals['grand_total']
        ]);

        json_response($response_data['success'], $response_data);
        break;


    case 'get_product_details':

        $product_id = filter_input(INPUT_GET, 'product_id', FILTER_VALIDATE_INT);
        if (!$product_id) {
            json_response(false, 'ID do produto inválido.');
            break;
        }


        $product = db_query("SELECT * FROM products WHERE id = :id AND is_active = 1", [':id' => $product_id], true);
        if (!$product) {
            json_response(false, 'Produto não encontrado.');
            break;
        }



        $variations_raw = db_query(
            "SELECT pv.id as variation_id, pv.sku, pv.stock, pv.is_active as variation_is_active, vv.value_id, av.value_pt, av.price_modifier as value_price_modifier, a.id as attribute_id, a.name_pt as attribute_name FROM product_variations pv JOIN variation_values vv ON pv.id = vv.variation_id JOIN attribute_values av ON vv.value_id = av.id JOIN attributes a ON av.attribute_id = a.id JOIN products p ON pv.product_id = p.id WHERE pv.product_id = :product_id AND pv.is_active = 1 AND p.is_active = 1 ORDER BY pv.id, a.name_pt, av.value_pt",
            [':product_id' => $product['id']], false, true );

        $attributes = []; $variation_data = []; $attribute_value_modifiers = [];
        $has_single_variation_with_stock = false;
        $single_variation_id = null;
        $single_variation_stock = 0;

        if ($variations_raw) {
             foreach ($variations_raw as $row) {
                if (!isset($attributes[$row['attribute_id']])) { $attributes[$row['attribute_id']] = ['name' => $row['attribute_name'], 'values' => []]; }
                if (!isset($attributes[$row['attribute_id']]['values'][$row['value_id']])) { $attributes[$row['attribute_id']]['values'][$row['value_id']] = $row['value_pt']; }
                if (!isset($variation_data[$row['variation_id']])) {

                    $stock = is_numeric($row['stock']) ? (int)$row['stock'] : 0;
                    $variation_data[$row['variation_id']] = ['sku' => $row['sku'], 'stock' => $stock, 'options' => []];
                }
                $variation_data[$row['variation_id']]['options'][$row['attribute_id']] = $row['value_id'];
                if (!isset($attribute_value_modifiers[$row['value_id']])) { $attribute_value_modifiers[$row['value_id']] = (float)$row['value_price_modifier']; }


                if (count($variations_raw) === 1 || !isset($single_variation_id)) {
                    $single_variation_id = $row['variation_id'];
                    $single_variation_stock = (int)$row['stock'];
                    $has_single_variation_with_stock = $single_variation_stock > 0;
                }

            }
        }

        $images = db_query( "SELECT id, filename, is_default FROM product_images WHERE product_id = :pid ORDER BY sort_order ASC", [':pid' => $product['id']], false, true );


        require_once __DIR__ . '/product_functions.php';
        $default_image = get_product_default_image($product['id']);
        $default_image_url = get_asset_url('images/placeholder.png');
        $default_image_id = null;

        if ($default_image && !empty($default_image['filename'])) {
            $default_image_url = get_product_image_url($default_image['filename']);
            $default_image_id = $default_image['id'];
        }

        $gallery_images = [];
        if ($images) {
            foreach ($images as $index => $img) {
                $img_url = get_product_image_url($img['filename']);
                $alt_text = sanitize_input($product['name_pt']) . ($index > 0 ? ' - Imagem ' . ($index + 1) : '');
                $gallery_images[] = [
                    'id' => $img['id'],
                    'url' => $img_url,
                    'alt' => $alt_text,
                    'filename' => $img['filename'],
                    'is_default' => ($img['id'] === $default_image_id)
                ];
            }
        }
        $currency_symbol = get_setting('currency_symbol', '€');


        ob_start();
        ?>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Product Images -->
            <div>
                <div class="bg-gray-800 rounded-lg overflow-hidden mb-4 aspect-square">
                    <img id="mainImage" src="<?= $default_image_url ?>" alt="<?= sanitize_input($product['name_pt']) ?>" class="w-full h-full object-cover">
                </div>
                <?php if (count($gallery_images) > 1): ?>
                    <div class="grid grid-cols-4 gap-2">
                        <?php foreach ($gallery_images as $index => $thumb): ?>
                            <div class="bg-gray-800 rounded cursor-pointer thumbnail-item <?= $index === 0 ? 'border-2 border-primary' : '' ?>" data-large-src="<?= $thumb['url'] ?>">
                                <img src="<?= $thumb['url'] ?>" alt="<?= $thumb['alt'] ?>" class="w-full h-24 object-cover">
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
            <!-- Product Info -->
            <div>
                 <h2 class="text-2xl font-semibold mb-2"><?= sanitize_input($product['name_pt']) ?></h2>
                 <div class="text-2xl font-bold mb-4" id="product-price-display"><?= format_price($product['base_price'], $currency_symbol) ?></div>
                 <div class="text-sm text-gray-400 mb-4">
                     SKU: <span id="product-sku-display">N/D</span> | Disponibilidade: <span id="product-stock-display" class="font-medium text-gray-300">Selecione opções</span>
                 </div>
                 <div class="prose prose-invert max-w-none text-gray-300 mb-4 text-sm">
                     <?= !empty($product['short_description_pt']) ? $product['short_description_pt'] : substr(strip_tags($product['description_pt'] ?? ''), 0, 150) . '...'  ?>
                 </div>

                 <!-- Variation Selection -->
                 <form id="variation-form-lightbox" class="space-y-4">
                     <input type="hidden" name="product_id" value="<?= $product['id'] ?>">
                     <input type="hidden" name="selected_variation_id" id="selected_variation_id" value="">
                     <?php if ($attributes): ?>
                         <?php foreach ($attributes as $attr_id => $attribute): ?>
                         <div>
                             <label for="attribute-lightbox-<?= $attr_id ?>" class="block text-sm font-medium text-gray-400 mb-1"><?= sanitize_input($attribute['name']) ?>:</label>
                             <select class="variation-select w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none appearance-none"
                                     id="attribute-lightbox-<?= $attr_id ?>" data-attribute-id="<?= $attr_id ?>" required>
                                 <option value="" selected disabled>-- Selecione --</option>
                                 <?php $sorted_values = $attribute['values']; asort($sorted_values); ?>
                                 <?php foreach ($sorted_values as $value_id => $value_name):
                                     $value_exists = false; foreach ($variation_data as $vd) { if (isset($vd['options'][$attr_id]) && $vd['options'][$attr_id] == $value_id) { $value_exists = true; break; } }
                                     if ($value_exists): ?>
                                     <?php

                                     $is_selected = ($has_single_variation_with_stock && count($variation_data) === 1 &&
                                                    isset($variation_data[$single_variation_id]['options'][$attr_id]) &&
                                                    $variation_data[$single_variation_id]['options'][$attr_id] == $value_id);
                                 ?>
                                 <option value="<?= $value_id ?>" <?= $is_selected ? 'selected' : '' ?>><?= sanitize_input($value_name) ?></option>
                                 <?php endif; endforeach; ?>
                             </select>
                         </div>
                         <?php endforeach; ?>
                     <?php else: if (count($variation_data) === 1) { $svid = key($variation_data); echo '<script>document.addEventListener("DOMContentLoaded", () => { window.singleVariationId = "' . $svid . '"; });</script>'; } endif; ?>

                     <?php

                     if (count($variation_data) === 1) {
                         $svid = key($variation_data);
                         echo '<script>document.addEventListener("DOMContentLoaded", () => {
                             window.singleVariationId = "' . $svid . '";
                             window.hasSingleVariation = true;
                             window.singleVariationStock = ' . $single_variation_stock . ';
                         });</script>';
                     }
                     ?>
                     <div id="variation-error" class="text-red-400 text-sm" style="display: none;">Combinação indisponível.</div>
                 </form>

                 <!-- Add to Cart Section -->
                 <div class="flex items-center mt-6">
                     <div class="flex items-center border border-gray-700 rounded-lg mr-4">
                         <button id="detail-minus-btn" class="px-3 py-2 text-gray-400 hover:text-white disabled:opacity-50" aria-label="Diminuir quantidade" disabled><i class="ri-subtract-line"></i></button>
                         <input type="number" id="detail-quantity" value="1" min="1" max="1" class="w-12 bg-transparent border-none text-center text-white focus:outline-none" aria-label="Quantidade" disabled>
                         <button id="detail-plus-btn" class="px-3 py-2 text-gray-400 hover:text-white disabled:opacity-50" aria-label="Aumentar quantidade" disabled><i class="ri-add-line"></i></button>
                     </div>
                     <button id="addToCartDetail" class="flex-1 bg-primary hover:bg-primary/90 text-white py-3 px-6 rounded-button font-medium whitespace-nowrap disabled:opacity-50 disabled:cursor-not-allowed"
                             data-product-id="<?= $product['id'] ?>" disabled>
                         Adicionar ao Carrinho
                     </button>
                 </div>
                 <a href="<?= get_product_url($product['slug'] ?? '')  ?>" class="block text-center text-sm text-primary hover:underline mt-4">Ver detalhes completos do produto</a>
            </div>
        </div>
         <!-- Embed JS directly for lightbox interactions -->
        <script>
            (function() { // IIFE to scope variables
                const lightboxContent = document.getElementById('lightbox-content'); // Get the container
                if (!lightboxContent) return;

                const productVariations = <?= json_encode($variation_data, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK) ?>;
                const attributeValueModifiers = <?= json_encode($attribute_value_modifiers, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK) ?>;
                // Removed attributeValueImages
                const productBasePrice = <?= $product['base_price'] ?>;
                const currencySymbol = '<?= $currency_symbol ?>';
                // Handle single variation case - if there's only one variation, we can pre-select it
                // even if it has attributes (this fixes the issue with products like "Brinde 1")
                const singleVariationId = <?= (count($variation_data) === 1) ? '"' . key($variation_data) . '"' : 'null' ?>;
                const hasSingleVariation = <?= (count($variation_data) === 1) ? 'true' : 'false' ?>;

                const variationSelects = lightboxContent.querySelectorAll('.variation-select');
                const priceDisplay = lightboxContent.querySelector('#product-price-display');
                const skuDisplay = lightboxContent.querySelector('#product-sku-display');
                const stockDisplay = lightboxContent.querySelector('#product-stock-display');
                const addToCartBtn = lightboxContent.querySelector('#addToCartDetail');
                const quantityInput = lightboxContent.querySelector('#detail-quantity');
                const minusBtn = lightboxContent.querySelector('#detail-minus-btn');
                const plusBtn = lightboxContent.querySelector('#detail-plus-btn');
                const variationError = lightboxContent.querySelector('#variation-error');
                const selectedVariationInput = lightboxContent.querySelector('#selected_variation_id');
                const mainImage = lightboxContent.querySelector('#mainImage');
                const thumbnails = lightboxContent.querySelectorAll('.thumbnail-item'); // Scope thumbnails to lightbox

                function formatPriceJS(amount) {
                    return (currencySymbol || '€') + parseFloat(amount).toFixed(2).replace('.', ',');
                }

                function updateVariationDetails() {
                    const selectedOptions = {};
                    let allSelected = true;
                    variationSelects.forEach(select => {
                        const attrId = select.getAttribute('data-attribute-id');
                        if (select.value) {
                            selectedOptions[attrId] = select.value;
                        } else {
                            allSelected = false;
                        }
                    });

                    let matchedVariationId = null;
                    let matchedVariation = null;

                    if (allSelected && Object.keys(selectedOptions).length === variationSelects.length) {
                        for (const varId in productVariations) {
                            const variation = productVariations[varId];
                            let optionsMatch = true;
                            for (const attrId in selectedOptions) {
                                if (!variation.options[attrId] || variation.options[attrId] != selectedOptions[attrId]) {
                                    optionsMatch = false;
                                    break;
                                }
                            }
                            if (optionsMatch && Object.keys(variation.options).length === Object.keys(selectedOptions).length) {
                                matchedVariationId = varId;
                                matchedVariation = variation;
                                break;
                            }
                        }
                    } else if (singleVariationId && (variationSelects.length === 0 || hasSingleVariation)) {
                        // Handle both cases:
                        // 1. No variation selects but has a single variation
                        // 2. Has variation selects but only one variation exists (pre-select it)
                        matchedVariationId = singleVariationId;
                        matchedVariation = productVariations[matchedVariationId];
                        allSelected = true;

                        // If we have a single variation with attributes, pre-select the options
                        if (variationSelects.length > 0 && hasSingleVariation) {
                            const variation = productVariations[singleVariationId];
                            // Pre-select the options in the dropdowns
                            variationSelects.forEach(select => {
                                const attrId = select.getAttribute('data-attribute-id');
                                if (variation.options[attrId]) {
                                    select.value = variation.options[attrId];
                                    selectedOptions[attrId] = variation.options[attrId];
                                }
                            });
                        }
                    }

                    if (matchedVariation) {
                        if(variationError) variationError.style.display = 'none';
                        if(selectedVariationInput) selectedVariationInput.value = matchedVariationId;
                        if(addToCartBtn) addToCartBtn.setAttribute('data-variation-id', matchedVariationId);

                        let currentPrice = productBasePrice;
                        for (const attrId in selectedOptions) {
                            const valueId = selectedOptions[attrId];
                            currentPrice += attributeValueModifiers[valueId] || 0;
                        }
                        if(priceDisplay) priceDisplay.textContent = formatPriceJS(currentPrice);

                        if(skuDisplay) skuDisplay.textContent = matchedVariation.sku || 'N/D';
                        const stock = parseInt(matchedVariation.stock);
                        if (stock > 0) {
                            if(stockDisplay) {
                                stockDisplay.textContent = `Em Stock (${stock} disponíveis)`;
                                stockDisplay.className = 'font-medium text-green-400';
                            }
                            if(quantityInput) {
                                quantityInput.max = stock;
                                quantityInput.disabled = false;
                                // Ensure current value isn't over max if stock changed
                                if (parseInt(quantityInput.value) > stock) quantityInput.value = stock;
                                if (parseInt(quantityInput.value) < 1) quantityInput.value = 1; // Ensure at least 1 if stock > 0
                            }
                            if(addToCartBtn) {
                                addToCartBtn.disabled = false;
                                addToCartBtn.classList.remove('disabled:opacity-50', 'disabled:cursor-not-allowed');
                            }
                        } else {
                             if(stockDisplay) {
                                stockDisplay.textContent = 'Esgotado';
                                stockDisplay.className = 'font-medium text-red-400';
                            }
                            if(quantityInput) {
                                quantityInput.max = 0;
                                quantityInput.value = 0;
                                quantityInput.disabled = true;
                            }
                            if(addToCartBtn) {
                                addToCartBtn.disabled = true;
                                addToCartBtn.classList.add('disabled:opacity-50', 'disabled:cursor-not-allowed');
                            }
                        }

                        // Removed image update based on value selection
                        // Ensure default image and first thumbnail are highlighted
                        if (mainImage) {
                            mainImage.src = '<?= $default_image_url ?>';
                            thumbnails.forEach((t, idx) => {
                                if (idx === 0) t.classList.add('border-primary', 'border-2');
                                else t.classList.remove('border-primary', 'border-2');
                            });
                        }

                    } else { // No matching variation found
                        if(selectedVariationInput) selectedVariationInput.value = '';
                        if(addToCartBtn) addToCartBtn.removeAttribute('data-variation-id');
                        if(priceDisplay) priceDisplay.textContent = formatPriceJS(productBasePrice);
                        if(skuDisplay) skuDisplay.textContent = 'N/D';
                        if(stockDisplay) {
                            stockDisplay.textContent = 'Selecione opções';
                            stockDisplay.className = 'font-medium text-gray-300';
                        }
                        if(quantityInput) {
                            quantityInput.max = 1;
                            quantityInput.value = 1;
                            quantityInput.disabled = true;
                        }
                        if(addToCartBtn) {
                            addToCartBtn.disabled = true;
                            addToCartBtn.classList.add('disabled:opacity-50', 'disabled:cursor-not-allowed');
                        }
                        if (allSelected && variationSelects.length > 0 && variationError) {
                            variationError.style.display = 'block';
                        } else if (variationError) {
                            variationError.style.display = 'none';
                        }
                        if (mainImage) {
                            mainImage.src = '<?= $default_image_url ?>';
                            thumbnails.forEach((t, idx) => {
                                if (idx === 0) t.classList.add('border-primary', 'border-2');
                                else t.classList.remove('border-primary', 'border-2');
                            });
                        }
                    }
                    updateQuantityButtonStates();
                }

                function updateQuantityButtonStates() {
                    if (!quantityInput || !minusBtn || !plusBtn) return;
                    const currentValue = parseInt(quantityInput.value);
                    const maxStock = parseInt(quantityInput.max);
                    minusBtn.disabled = currentValue <= 1 || quantityInput.disabled;
                    plusBtn.disabled = currentValue >= maxStock || quantityInput.disabled;
                }

                // --- Attach Event Listeners ---
                variationSelects.forEach(select => {
                    select.addEventListener('change', updateVariationDetails);
                });

                if (minusBtn && plusBtn && quantityInput) {
                    minusBtn.addEventListener('click', () => {
                        let value = parseInt(quantityInput.value);
                        if (value > 1) {
                            quantityInput.value = value - 1;
                            updateQuantityButtonStates();
                        }
                    });
                    plusBtn.addEventListener('click', () => {
                        let value = parseInt(quantityInput.value);
                        let max = parseInt(quantityInput.max);
                        if (value < max) {
                            quantityInput.value = value + 1;
                            updateQuantityButtonStates();
                        }
                    });
                    quantityInput.addEventListener('change', () => {
                        let value = parseInt(quantityInput.value);
                        let min = parseInt(quantityInput.min);
                        let max = parseInt(quantityInput.max);
                        if (isNaN(value) || value < min) quantityInput.value = min;
                        else if (value > max) quantityInput.value = max;
                        updateQuantityButtonStates();
                    });
                }

                thumbnails.forEach(thumbnail => {
                    thumbnail.addEventListener('click', function() {
                        thumbnails.forEach(t => t.classList.remove('border-primary', 'border-2'));
                        this.classList.add('border-primary', 'border-2');
                        const newSrc = this.getAttribute('data-large-src') || this.querySelector('img').src;
                        if (mainImage) mainImage.src = newSrc;
                    });
                });

                if (addToCartBtn) {
                    addToCartBtn.addEventListener('click', function() {
                        const productId = this.getAttribute('data-product-id');
                        const variationId = selectedVariationInput ? selectedVariationInput.value : null;
                        const quantity = quantityInput ? parseInt(quantityInput.value) : 0;

                        if (productId && variationId && quantity > 0) {
                            // Use the global ajaxRequest function for consistency
                            ajaxRequest('?action=add_to_cart', {
                                method: 'POST',
                                body: JSON.stringify({
                                    product_id: productId,
                                    variation_id: variationId,
                                    quantity: quantity
                                })
                            }).then(response => {
                                if (response.success) {
                                    if (typeof showToast === 'function') {
                                        showToast('Sucesso', 'Produto adicionado ao carrinho!', 'ri-check-line', 'border-green-500');
                                    } else { alert('Produto adicionado!'); }
                                    // Update cart badge (assuming fetchCartCount exists globally or is triggered)
                                    if (typeof fetchCartCount === 'function') fetchCartCount();
                                    // Close lightbox (assuming closeLightbox exists globally)
                                    if (typeof closeLightbox === 'function') closeLightbox();
                                } else {
                                    if (typeof showToast === 'function') {
                                        showToast('Erro', response.error || 'Erro ao adicionar.', 'ri-error-warning-line', 'border-red-500');
                                    } else { alert(response.error || 'Erro ao adicionar.'); }
                                }
                            }).catch(error => {
                                console.error('Add to cart error:', error);
                                if (typeof showToast === 'function') {
                                    showToast('Erro', 'Erro de comunicação.', 'ri-error-warning-line', 'border-red-500');
                                } else { alert('Erro de comunicação.'); }
                            });
                        } else {
                            if (typeof showToast === 'function') { // Use global showToast
                                showToast('Erro', 'Selecione uma opção válida e quantidade.', 'ri-error-warning-line', 'border-red-500');
                            } else { alert('Selecione uma opção válida e quantidade.'); }
                        }
                    });
                }

                // Initial run to set state based on defaults or single variation
                updateVariationDetails();

            })(); // End IIFE
        </script>
        <?php
        $html_content = ob_get_clean();


        $cart_item = [
            'product_id' => $product_id,
            'variation_id' => $variation_id,
            'quantity' => $quantity,
            'price' => $final_price,
            'name' => $variation['name_pt'],
            'slug' => $variation['product_slug'],
        ];


        $cart_key = $product_id . '-' . $variation_id;
        $_SESSION['cart'][$cart_key] = $cart_item;

        json_response(true, ['html' => $html_content]);
        break;


    case 'get_products':
        $category_id = filter_input(INPUT_GET, 'category_id', FILTER_VALIDATE_INT);
        $sort_option = filter_input(INPUT_GET, 'sort', FILTER_DEFAULT);
        $page = filter_input(INPUT_GET, 'page', FILTER_VALIDATE_INT);


        $sort_options_map = [
            'recent' => 'p.created_at DESC',
            'price_asc' => 'p.base_price ASC',
            'price_desc' => 'p.base_price DESC',
        ];
        $current_sort = $sort_option && array_key_exists($sort_option, $sort_options_map) ? $sort_option : 'recent';
        $order_by_clause = $sort_options_map[$current_sort];


        $category_id = ($category_id !== false && $category_id >= 0) ? $category_id : 0;


        $items_per_page = get_setting('items_per_page', 12);
        $current_page = ($page !== false && $page > 0) ? $page : 1;
        $offset = ($current_page - 1) * $items_per_page;


        $sql_select = "SELECT p.id, p.name_pt, p.slug, p.base_price";
        $sql_from = " FROM products p ";
        $sql_where = " WHERE p.is_active = 1 ";
        $sql_order_by = " ORDER BY " . $order_by_clause;
        $sql_limit_offset = " LIMIT :limit OFFSET :offset ";
        $params = [':limit' => $items_per_page, ':offset' => $offset];


        if ($category_id > 0) {
            $sql_from .= " JOIN product_categories pc ON p.id = pc.product_id ";
            $sql_where .= " AND pc.category_id = :category_id ";
            $params[':category_id'] = $category_id;
        }


        $sql_data = $sql_select . $sql_from . $sql_where . $sql_order_by . $sql_limit_offset;


        $products = db_query($sql_data, $params, false, true);

        if ($products === false) {
            json_response(false, 'Erro ao buscar produtos.');
            break;
        }


        $currency_symbol = get_setting('currency_symbol', '€');
        $formatted_products = [];
        if ($products) {
            foreach ($products as $product) {
                $image_data = db_query(
                    "SELECT filename FROM product_images WHERE product_id = :pid ORDER BY id ASC LIMIT 1",
                    [':pid' => $product['id']],
                    true
                );
                $product['display_image_url'] = ($image_data && !empty($image_data['filename']))
                                                ? get_product_image_url($image_data['filename'])
                                                : null;
                $product['product_url'] = get_product_url($product['slug']);
                $product['price_formatted'] = format_price($product['base_price'], $currency_symbol);
                $formatted_products[] = $product;
            }
        }


        $sql_count = "SELECT COUNT(p.id)" . $sql_from . $sql_where;
        $count_params = [];
        if ($category_id > 0) {
            $count_params[':category_id'] = $category_id;
        }
        $total_products_stmt = db_query($sql_count, $count_params);
        $total_products = ($total_products_stmt) ? (int)$total_products_stmt->fetchColumn() : 0;
        $total_pages = ceil($total_products / $items_per_page);

        json_response(true, [
            'products' => $formatted_products,
            'pagination' => [
                'currentPage' => $current_page,
                'totalPages' => $total_pages,
                'totalProducts' => $total_products
            ],
            'config' => [
                 'currencySymbol' => $currency_symbol,
                 'baseUrl' => BASE_URL
            ]
        ]);
        break;


    case 'get_pagination':
        $category_id = filter_input(INPUT_GET, 'category_id', FILTER_VALIDATE_INT);
        $sort_option = filter_input(INPUT_GET, 'sort', FILTER_DEFAULT);
        $page = filter_input(INPUT_GET, 'page', FILTER_VALIDATE_INT);


        $sort_options_map = [
            'recent' => 'p.created_at DESC',
            'price_asc' => 'p.base_price ASC',
            'price_desc' => 'p.base_price DESC',
        ];
        $current_sort = $sort_option && array_key_exists($sort_option, $sort_options_map) ? $sort_option : 'recent';


        $category_id = ($category_id !== false && $category_id >= 0) ? $category_id : 0;


        $items_per_page = get_setting('items_per_page', 12);
        $current_page = ($page !== false && $page > 0) ? $page : 1;


        $sql_from = " FROM products p ";
        $sql_where = " WHERE p.is_active = 1 ";
        $count_params = [];
        if ($category_id > 0) {
            $sql_from .= " JOIN product_categories pc ON p.id = pc.product_id ";
            $sql_where .= " AND pc.category_id = :category_id ";
            $count_params[':category_id'] = $category_id;
        }


        $sql_count = "SELECT COUNT(p.id)" . $sql_from . $sql_where;
        $total_products_stmt = db_query($sql_count, $count_params);
        $total_products = ($total_products_stmt) ? (int)$total_products_stmt->fetchColumn() : 0;
        $total_pages = ceil($total_products / $items_per_page);


        ob_start();
        if ($total_pages > 1): ?>
            <nav class="inline-flex rounded-md shadow-sm bg-gray-800" aria-label="Pagination">
                <?php

                $query_params = [];
                $query_params['view'] = 'home';
                if ($category_id > 0) $query_params['category_id'] = $category_id;
                if ($current_sort !== 'recent') $query_params['sort'] = $current_sort;


                $base_url = 'index.php?' . http_build_query($query_params) . '&';


                $prev_page = $current_page - 1;
                $prev_link_class = 'px-3 py-2 text-sm font-medium rounded-l-button ';
                $prev_link_href = $base_url . 'page=' . $prev_page;
                if ($current_page <= 1) {
                    $prev_link_class .= 'text-gray-600 bg-gray-700 cursor-not-allowed opacity-50';
                    $prev_tag = 'span';
                    $prev_link_href = '#';
                } else {
                    $prev_link_class .= 'text-gray-300 hover:bg-gray-700';
                    $prev_tag = 'a';
                }
                ?>
                <<?= $prev_tag ?> href="<?= $prev_link_href ?>" class="<?= $prev_link_class ?>" <?= ($prev_tag === 'a' ? 'aria-label="Previous"' : '') ?>>
                    <i class="ri-arrow-left-s-line"></i>
                </<?= $prev_tag ?>>

                <?php

                for ($i = 1; $i <= $total_pages; $i++):
                    $page_link_class = 'px-4 py-2 text-sm font-medium ';
                    $page_link_href = $base_url . 'page=' . $i;
                    $page_tag = 'a';
                    $aria_current = '';

                    if ($i == $current_page) {
                        $page_link_class .= 'text-white bg-primary z-10 cursor-default';
                        $page_tag = 'span';
                        $aria_current = 'aria-current="page"';
                        $page_link_href = '#';
                    } else {
                        $page_link_class .= 'text-gray-300 hover:bg-gray-700';
                    }
                ?>
                    <<?= $page_tag ?> href="<?= $page_link_href ?>" class="<?= $page_link_class ?>" <?= $aria_current ?>>
                        <?= $i ?>
                    </<?= $page_tag ?>>
                <?php endfor; ?>

                <?php

                $next_page = $current_page + 1;
                $next_link_class = 'px-3 py-2 text-sm font-medium rounded-r-button ';
                $next_link_href = $base_url . 'page=' . $next_page;
                if ($current_page >= $total_pages) {
                    $next_link_class .= 'text-gray-600 bg-gray-700 cursor-not-allowed opacity-50';
                    $next_tag = 'span';
                    $next_link_href = '#';
                } else {
                    $next_link_class .= 'text-gray-300 hover:bg-gray-700';
                    $next_tag = 'a';
                }
                ?>
                <<?= $next_tag ?> href="<?= $next_link_href ?>" class="<?= $next_link_class ?>" <?= ($next_tag === 'a' ? 'aria-label="Next"' : '') ?>>
                     <i class="ri-arrow-right-s-line"></i>
                </<?= $next_tag ?>>
            </nav>
        <?php endif;
        $pagination_html = ob_get_clean();

        json_response(true, ['pagination_html' => $pagination_html]);
        break;


    case 'create_vat_rate':

        require_once __DIR__ . '/vat_functions.php';


        $rate = filter_var($_POST['rate'] ?? 0, FILTER_VALIDATE_FLOAT);
        $description = trim($_POST['description'] ?? '');
        $is_default = filter_var($_POST['is_default'] ?? 0, FILTER_VALIDATE_INT);


        if ($rate === false || $rate < 0) {
            json_response(false, 'Taxa de IVA inválida.');
            break;
        }

        if (empty($description)) {
            json_response(false, 'Descrição da taxa de IVA é obrigatória.');
            break;
        }


        $vat_rate_id = create_vat_rate($rate, $description, $is_default == 1);

        if ($vat_rate_id) {
            json_response(true, 'Taxa de IVA criada com sucesso.');
        } else {
            json_response(false, 'Erro ao criar taxa de IVA.');
        }
        break;


    case 'update_vat_rate':

        require_once __DIR__ . '/vat_functions.php';


        $vat_rate_id = filter_var($_POST['vat_rate_id'] ?? 0, FILTER_VALIDATE_INT);
        $rate = filter_var($_POST['rate'] ?? 0, FILTER_VALIDATE_FLOAT);
        $description = trim($_POST['description'] ?? '');
        $is_default = filter_var($_POST['is_default'] ?? 0, FILTER_VALIDATE_INT);


        if ($vat_rate_id <= 0) {
            json_response(false, 'ID da taxa de IVA inválido.');
            break;
        }

        if ($rate === false || $rate < 0) {
            json_response(false, 'Taxa de IVA inválida.');
            break;
        }

        if (empty($description)) {
            json_response(false, 'Descrição da taxa de IVA é obrigatória.');
            break;
        }


        $data = [
            'rate' => $rate,
            'description' => $description,
            'is_default' => $is_default == 1
        ];
        $result = update_vat_rate($vat_rate_id, $data);

        if ($result) {
            json_response(true, 'Taxa de IVA atualizada com sucesso.');
        } else {
            json_response(false, 'Erro ao atualizar taxa de IVA.');
        }
        break;


    case 'delete_vat_rate':

        require_once __DIR__ . '/vat_functions.php';


        $vat_rate_id = filter_var($_POST['vat_rate_id'] ?? 0, FILTER_VALIDATE_INT);


        if ($vat_rate_id <= 0) {
            json_response(false, 'ID da taxa de IVA inválido.');
            break;
        }


        $result = delete_vat_rate($vat_rate_id);

        if ($result) {
            json_response(true, 'Taxa de IVA eliminada com sucesso.');
        } else {
            json_response(false, 'Erro ao eliminar taxa de IVA. Não é possível eliminar a taxa padrão se for a única existente.');
        }
        break;


    default:
        json_response(false, 'Ação inválida.');
        break;
}

while (ob_get_level() > 0) {
    ob_end_clean();
}
?>
