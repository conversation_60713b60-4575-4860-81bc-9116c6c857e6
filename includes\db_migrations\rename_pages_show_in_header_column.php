<![CDATA[
<?php

// Migration to rename the 'show_in_headerINTEGER' column to 'show_in_header' in the 'pages' table.

function migrate_rename_pages_show_in_header_column(PDO $pdo) {
    try {
        $pdo->beginTransaction();

        // Check if the problematic column 'show_in_headerINTEGER' exists
        $stmt = $pdo->query("PRAGMA table_info(pages);");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);

        $old_column_exists = in_array('show_in_headerINTEGER', $columns);
        $new_column_exists = in_array('show_in_header', $columns);

        if ($old_column_exists && !$new_column_exists) {
            // SQLite does not support RENAME COLUMN directly in older versions or with all constraints.
            // The safest way is to recreate the table if ALTER TABLE RENAME COLUMN is not available or problematic.
            // However, for a simple rename, ALTER TABLE RENAME COLUMN should work in modern SQLite.
            $pdo->exec("ALTER TABLE pages RENAME COLUMN show_in_headerINTEGER TO show_in_header;");
            error_log("Migration: Renamed column 'show_in_headerINTEGER' to 'show_in_header' in 'pages' table.");
            log_migration_applied($pdo, 'rename_pages_show_in_header_column'); // Pass $pdo
        } elseif ($new_column_exists) {
            // If the new column already exists and the old one doesn't, the migration might have been done manually
            // or the schema was already correct. We can mark it as applied if the old doesn't exist.
            if (!$old_column_exists) {
                 error_log("Migration: Column 'show_in_header' already exists and 'show_in_headerINTEGER' does not. Marking 'rename_pages_show_in_header_column' as applied if not already.");
                 // Ensure it's marked as applied if it wasn't, to prevent issues if it was manually fixed.
                 if (!is_migration_applied($pdo, 'rename_pages_show_in_header_column')) { // Pass $pdo
                    log_migration_applied($pdo, 'rename_pages_show_in_header_column'); // Pass $pdo
                 }
            } else {
                // This case (both old and new exist) is unlikely and indicates a problem.
                error_log("Migration Warning: Both 'show_in_headerINTEGER' and 'show_in_header' columns exist in 'pages' table. Manual intervention might be needed.");
            }
        } elseif (!$old_column_exists && !$new_column_exists) {
            // Neither column exists - this is unexpected.
            error_log("Migration Error: Neither 'show_in_headerINTEGER' nor 'show_in_header' column found in 'pages' table. Cannot apply 'rename_pages_show_in_header_column'.");
        }

        $pdo->commit();
        return true;
    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("Migration Error (rename_pages_show_in_header_column): " . $e->getMessage());
        return false;
    }
}

// Add this migration to the list of migrations to be run in db.php:
// 'rename_pages_show_in_header_column' => 'migrate_rename_pages_show_in_header_column',