<?php

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/functions.php';

$pdo = get_db_connection();

if (!$pdo) {
    die("Failed to connect to the database.");
}

$check_column_sql = "PRAGMA table_info(download_tokens)";
$columns = $pdo->query($check_column_sql)->fetchAll(PDO::FETCH_ASSOC);

$has_is_used_column = false;
$has_session_id_column = false;

foreach ($columns as $column) {
    if ($column['name'] === 'is_used') {
        $has_is_used_column = true;
    }
    if ($column['name'] === 'session_id') {
        $has_session_id_column = true;
    }
}

$pdo->beginTransaction();

try {
    
    if (!$has_is_used_column) {
        $pdo->exec("ALTER TABLE download_tokens ADD COLUMN is_used INTEGER DEFAULT 0");
        echo "Added is_used column to download_tokens table.\n";
    } else {
        echo "is_used column already exists in download_tokens table.\n";
    }

    
    if (!$has_session_id_column) {
        $pdo->exec("ALTER TABLE download_tokens ADD COLUMN session_id TEXT DEFAULT ''");
        echo "Added session_id column to download_tokens table.\n";
    } else {
        echo "session_id column already exists in download_tokens table.\n";
    }

    
    $pdo->exec("UPDATE download_tokens SET session_id = '' WHERE session_id IS NULL");
    echo "Updated existing records with empty session_id.\n";

    
    $pdo->commit();
    echo "Database update completed successfully.\n";
} catch (PDOException $e) {
    
    $pdo->rollBack();
    echo "Error updating database: " . $e->getMessage() . "\n";
    die();
}

echo "Done.\n";
