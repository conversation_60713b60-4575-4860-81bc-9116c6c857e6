<?php

require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/custom_field_functions.php';

echo "Checking order items for custom fields...\n";

$pdo = get_db_connection();

if ($pdo) {
    echo "Database connection successful.\n";

    
    $stmt = $pdo->query("SELECT * FROM order_items ORDER BY id DESC;");
    $order_items = $stmt->fetchAll();

    echo "Number of order items in the database: " . count($order_items) . "\n";

    foreach ($order_items as $item) {
        echo "\nOrder Item ID: " . $item['id'] . "\n";
        echo "Order ID: " . $item['order_id'] . "\n";
        echo "Product ID: " . $item['product_id'] . "\n";

        
        $details = json_decode($item['product_details_json'], true);

        if (!empty($details['custom_fields'])) {
            echo "Custom Fields Found:\n";
            foreach ($details['custom_fields'] as $custom_field) {
                echo "- Field Name: " . ($custom_field['field_name'] ?? 'N/A') . "\n";
                echo "  Field Type: " . ($custom_field['field_type'] ?? 'N/A') . "\n";

                if (isset($custom_field['text_value'])) {
                    echo "  Text Value: " . $custom_field['text_value'] . "\n";
                }

                if (isset($custom_field['file_path'])) {
                    echo "  File Path: " . $custom_field['file_path'] . "\n";

                    
                    $file_path = __DIR__ . $custom_field['file_path'];
                    if (file_exists($file_path)) {
                        echo "  File exists at: " . $file_path . "\n";
                    } else {
                        echo "  File does NOT exist at: " . $file_path . "\n";
                    }
                }

                echo "\n";
            }

            
            echo "Saving custom fields to order_item_custom_fields table...\n";

            foreach ($details['custom_fields'] as $custom_field) {
                $field_id = $custom_field['field_id'] ?? 0;
                $field_name = $custom_field['field_name'] ?? '';
                $field_type = $custom_field['field_type'] ?? '';
                $price_modifier = $custom_field['price_modifier'] ?? 0.0;

                if ($field_type === 'custom-text') {
                    
                    $field_value = $custom_field['text_value'] ?? '';
                    $font_id = $custom_field['font_id'] ?? null;

                    $result = save_order_item_custom_field(
                        $item['id'],
                        $field_id,
                        $field_name,
                        $field_value,
                        null, 
                        $font_id,
                        $price_modifier
                    );

                    echo "Saved text custom field: " . ($result ? "Success (ID: $result)" : "Failed") . "\n";
                } elseif ($field_type === 'file-upload') {
                    
                    $file_path = $custom_field['file_path'] ?? '';

                    if (!empty($file_path)) {
                        $result = save_order_item_custom_field(
                            $item['id'],
                            $field_id,
                            $field_name,
                            null, 
                            $file_path,
                            null, 
                            $price_modifier
                        );

                        echo "Saved file custom field: " . ($result ? "Success (ID: $result)" : "Failed") . "\n";
                    }
                }
            }
        } else {
            echo "No custom fields found.\n";
        }
    }
} else {
    echo "ERROR: Failed to connect to the database.\n";
}

echo "\nCheck completed.\n";
?>
