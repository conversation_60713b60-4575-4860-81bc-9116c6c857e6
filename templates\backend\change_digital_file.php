<?php

if (!$is_admin_logged_in) {
    header('Location: admin.php?section=login&' . get_session_id_param());
    exit;
}

$digital_product_id = $item_id ?? ($_GET['id'] ?? 0);

$digital_product = null;
$product_data = null;

if ($digital_product_id > 0) {
    $digital_product = db_query("SELECT dp.*, df.original_filename AS current_original_filename, df.display_name AS current_display_name, df.file_path AS current_file_path
                                 FROM digital_products dp
                                 LEFT JOIN digital_files df ON dp.digital_file_id = df.id
                                 WHERE dp.id = :id", [':id' => $digital_product_id], true);

    if (!$digital_product) {
        add_flash_message('Produto digital não encontrado.', 'danger');
        header('Location: admin.php?section=products&' . get_session_id_param());
        exit;
    }

    $product_data = db_query("SELECT * FROM products WHERE id = :id", [':id' => $digital_product['product_id']], true);

    if (!$product_data) {
        add_flash_message('Produto não encontrado.', 'danger');
        header('Location: admin.php?section=products&' . get_session_id_param());
        exit;
    }
}

$digital_files = get_all_digital_files();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
        add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
        header('Location: admin.php?section=digital_files&action=change_file&id=' . $digital_product_id . '&' . get_session_id_param());
        exit;
    }

    $action = $_POST['action'] ?? '';

    if ($action === 'change_file') {
        $file_id = (int)($_POST['file_id'] ?? 0);

        if ($file_id <= 0) {
            add_flash_message('Por favor, selecione um arquivo.', 'danger');
        } else {
            
            if (update_digital_product_file($digital_product_id, $file_id)) {
                add_flash_message('Arquivo do produto digital atualizado com sucesso.', 'success');
                header('Location: admin.php?section=products&action=edit&id=' . $digital_product['product_id'] . '&' . get_session_id_param());
                exit;
            } else {
                add_flash_message('Erro ao atualizar arquivo do produto digital.', 'danger');
            }
        }
    } elseif ($action === 'upload_new') {
        
        if (isset($_FILES['digital_file']) && $_FILES['digital_file']['error'] !== UPLOAD_ERR_NO_FILE) {
            
            if ($_FILES['digital_file']['error'] !== UPLOAD_ERR_OK) {
                add_flash_message('Erro ao carregar o arquivo: ' . get_upload_error_message($_FILES['digital_file']['error']), 'danger');
            } else {
                
                $max_size = 100 * 1024 * 1024; 
                if ($_FILES['digital_file']['size'] > $max_size) {
                    add_flash_message('O arquivo é muito grande. O tamanho máximo permitido é 100MB.', 'danger');
                } else {
                    
                    $upload_dir = get_setting('digital_products_directory', '../digital_products');

                    
                    if (strpos($upload_dir, '../') === 0) {
                        $upload_dir = dirname(dirname(__DIR__)) . '/' . substr($upload_dir, 3);
                    } elseif (strpos($upload_dir, './') === 0) {
                        $upload_dir = dirname(__DIR__) . '/' . substr($upload_dir, 2);
                    }

                    if (!file_exists($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }

                    
                    $original_filename = $_FILES['digital_file']['name'];
                    $file_extension = pathinfo($original_filename, PATHINFO_EXTENSION);
                    $new_filename = uniqid('digital_') . '_' . time() . '.' . $file_extension;
                    $file_path = $upload_dir . '/' . $new_filename;

                    
                    if (move_uploaded_file($_FILES['digital_file']['tmp_name'], $file_path)) {
                        
                        $display_name_input = trim($_POST['new_file_display_name'] ?? '');
                        if (empty($display_name_input) && $product_data) {
                            $display_name_input = $product_data['name_pt']; 
                        }
                        if (empty($display_name_input)) {
                            $display_name_input = $original_filename; 
                        }

                        
                        $file_data = [
                            'original_filename' => $original_filename,
                            'display_name' => $display_name_input,
                            'file_path' => $file_path,
                            'file_size' => $_FILES['digital_file']['size'],
                            'file_type' => $_FILES['digital_file']['type'],
                            'description' => $_POST['new_file_description'] ?? ''
                        ];

                        $file_id = create_digital_file($file_data);
                        if ($file_id) {
                            
                            if (update_digital_product_file($digital_product_id, $file_id)) {
                                add_flash_message('Arquivo do produto digital atualizado com sucesso.', 'success');
                                header('Location: admin.php?section=products&action=edit&id=' . $digital_product['product_id'] . '&' . get_session_id_param());
                                exit;
                            } else {
                                add_flash_message('Erro ao atualizar arquivo do produto digital.', 'danger');
                            }
                        } else {
                            add_flash_message('Erro ao criar registro do arquivo digital.', 'danger');
                        }
                    } else {
                        add_flash_message('Falha ao mover o arquivo carregado.', 'danger');
                    }
                }
            }
        } else {
            add_flash_message('Por favor, selecione um arquivo para carregar.', 'danger');
        }
    }
}

display_flash_messages();
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Alterar Arquivo do Produto Digital</h1>
        <a href="admin.php?section=products&action=edit&id=<?= $digital_product['product_id'] ?>&<?= get_session_id_param() ?>" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Voltar ao Produto
        </a>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informações do Produto</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">Nome do Produto</label>
                        <p class="form-control-static"><?= sanitize_input($product_data['name_pt']) ?></p>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Arquivo Atual</label>
                        <p class="form-control-static">
                            <?php
                                $current_file_label = '';
                                if (!empty($digital_product['current_display_name'])) {
                                    $current_file_label = sanitize_input($digital_product['current_display_name']);
                                } elseif (!empty($digital_product['current_original_filename'])) {
                                    $current_file_label = sanitize_input($digital_product['current_original_filename']);
                                }

                                if ($current_file_label):
                            ?>
                                <span class="badge bg-info">
                                    <?= $current_file_label ?>
                                    <?php if (!empty($digital_product['current_original_filename']) && $digital_product['current_original_filename'] !== $current_file_label): ?>
                                        (<?= sanitize_input($digital_product['current_original_filename']) ?>)
                                    <?php endif; ?>
                                </span>
                            <?php else: ?>
                                <span class="badge bg-warning">Nenhum arquivo definido</span>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Carregar Novo Arquivo</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="admin.php?section=digital_files&action=change_file&id=<?= $digital_product_id ?>&item_id=<?= $digital_product_id ?>&<?= get_session_id_param() ?>" enctype="multipart/form-data">
                        <?= csrf_input_field() ?>
                        <input type="hidden" name="action" value="upload_new">

                        <div class="mb-3">
                            <label for="digital_file" class="form-label">Arquivo Digital *</label>
                            <input type="file" class="form-control" id="digital_file" name="digital_file" required>
                            <div class="form-text">Tamanho máximo: 100MB.</div>
                        </div>

                        <div class="mb-3">
                            <label for="new_file_display_name" class="form-label">Nome de Exibição do Arquivo</label>
                            <input type="text" class="form-control" id="new_file_display_name" name="new_file_display_name" placeholder="Deixe em branco para usar o nome do produto ou original">
                            <div class="form-text">Nome amigável para este arquivo. Se deixado em branco, será usado o nome do produto ou o nome original do arquivo.</div>
                        </div>

                        <div class="mb-3">
                            <label for="new_file_description" class="form-label">Descrição</label>
                            <textarea class="form-control" id="new_file_description" name="new_file_description" rows="3"></textarea>
                            <div class="form-text">Uma descrição opcional para identificar este arquivo.</div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-upload"></i> Carregar e Usar Novo Arquivo
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Selecionar Arquivo Existente</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($digital_files)): ?>
                        <div class="alert alert-info mb-0">
                            Nenhum arquivo digital encontrado. Carregue um novo arquivo usando o formulário ao lado.
                        </div>
                    <?php else: ?>
                        <form method="POST" action="admin.php?section=digital_files&action=change_file&id=<?= $digital_product_id ?>&item_id=<?= $digital_product_id ?>&<?= get_session_id_param() ?>">
                            <?= csrf_input_field() ?>
                            <input type="hidden" name="action" value="change_file">

                            <div class="mb-3">
                                <label for="file_id" class="form-label">Selecione um Arquivo</label>
                                <select class="form-select" id="file_id" name="file_id" required>
                                    <option value="">-- Selecione um Arquivo --</option>
                                    <?php foreach ($digital_files as $file): ?>
                                        <option value="<?= $file['id'] ?>" <?= ($digital_product['digital_file_id'] == $file['id']) ? 'selected' : '' ?>>
                                            <?= sanitize_input(!empty($file['display_name']) ? $file['display_name'] : $file['original_filename']) ?>
                                            <?php if (!empty($file['display_name']) && $file['display_name'] !== $file['original_filename']): ?>
                                                (<?= sanitize_input($file['original_filename']) ?>)
                                            <?php endif; ?>
                                            (<?= format_file_size($file['file_size']) ?>)
                                            <?= !empty($file['description']) ? ' - ' . sanitize_input($file['description']) : '' ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-lg"></i> Usar Arquivo Selecionado
                            </button>
                        </form>

                        <hr>

                        <div class="mt-4">
                            <h6>Arquivos Disponíveis</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            <th>Nome de Exibição</th>
                                            <th>Nome Original</th>
                                            <th>Tamanho</th>
                                            <th>Descrição</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($digital_files as $file): ?>
                                            <tr>
                                                <td><?= sanitize_input(!empty($file['display_name']) ? $file['display_name'] : $file['original_filename']) ?></td>
                                                <td><?= sanitize_input($file['original_filename']) ?></td>
                                                <td><?= format_file_size($file['file_size']) ?></td>
                                                <td><?= sanitize_input($file['description'] ?? '') ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <div class="mt-3">
                                <a href="admin.php?section=digital_files&<?= get_session_id_param() ?>" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-folder"></i> Gerenciar Todos os Arquivos
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
