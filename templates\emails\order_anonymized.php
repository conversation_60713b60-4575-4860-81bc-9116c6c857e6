<?php

if (!isset($order)) {
    exit('Direct access not permitted');
}

$order_date = date('d/m/Y', strtotime($order['created_at']));
?>
<!DOCTYPE html>
<html lang="pt-PT">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> da Encomenda Anonimizados</title>
    <style>
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .header {
            background-color: #1a1a2e;
            padding: 25px;
            text-align: center;
            border-bottom: 3px solid #0f3460;
        }
        .content {
            padding: 30px;
            color: #444;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            font-size: 0.8em;
            color: #6c757d;
            border-top: 1px solid #dee2e6;
        }
        h1 {
            color: #ffffff;
            margin: 0;
            font-weight: 300;
            letter-spacing: 1px;
        }
        .info-box {
            background-color: #f0f7ff;
            border-left: 4px solid #0f3460;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 4px 4px 0;
        }
        .order-info {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 25px 0;
            border: 1px solid #e9ecef;
        }
        p, ul, li {
            margin-bottom: 16px;
            color: #444;
        }
        strong {
            color: #222;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 8px;
        }
        a {
            color: #0f3460;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Informação Importante Sobre a Sua Encomenda</h1>
        </div>

        <div class="content">
            <p>Prezado(a) Cliente,</p>

            <p>Gostaria de informar que, como parte do meu compromisso com a proteção de dados e segurança, os dados pessoais associados à sua encomenda <strong>#<?= $order['order_ref'] ?></strong> (de <?= $order_date ?>) foram anonimizados dentro do portal www.joaocesarsilva.com.</p>
            <br>
            <div class="info-box">
                <p><strong>O que isto significa?</strong></p>
                <p>Os seus dados pessoais (nome, endereço, contactos) foram removidos dos registos online ativos para esta encomenda específica. Esta é uma medida de segurança padrão que implementamos para proteger a privacidade dos nossos clientes.</p>
            </div>
            <br>
            <p>Esta anonimização é um procedimento normal e não afeta:</p>
            <ul>
                <li>O histórico da sua encomenda</li>
                <li>Quaisquer garantias associadas aos produtos adquiridos</li>
                <li>O seu acesso a produtos digitais (se aplicável)</li>
                <li>A sua capacidade de contactar o nosso suporte ao cliente</li>
            </ul>
            <br>
            <p>Se necessitar de assistência relacionada com esta encomenda, por favor contacte o serviço de apoio ao cliente com o número da encomenda ou responda a esta mensagem de email.</p>
            <br>
            <div class="order-info">
                <p><strong>Referência da Encomenda:</strong> <?= $order['order_ref'] ?></p>
                <p><strong>Data da Encomenda:</strong> <?= $order_date ?></p>
            </div>
            <br><br>
            <p>Agradecendo a sua compreensão e preferência.</p>

            <p>Com os melhores cumprimentos,<br>
            Equipa <?= $store_name ?></p>
        </div>

        <div class="footer">
            <p>Este é um email automático mas monitorizado, pode responder diretamente.</p>
            <p>&copy; <?= date('Y') ?> <?= $store_name ?>. Todos os direitos reservados.</p>
        </div>
    </div>
</body>
</html>
