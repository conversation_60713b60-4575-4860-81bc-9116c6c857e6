<?php
require_once 'includes/db.php';
require_once 'includes/digital_order_functions.php';

// Create a test session with various products
$test_session = [
    // Regular products
    '1' => [
        'product_id' => 1,
        'variation_id' => null,
        'quantity' => 1,
        'product_type' => 'regular'
    ],
    '2' => [
        'product_id' => 2,
        'variation_id' => null,
        'quantity' => 2,
        'product_type' => 'regular'
    ],
    // Digital products
    '3' => [
        'product_id' => 3,
        'variation_id' => null,
        'quantity' => 1,
        'product_type' => 'digital'
    ],
    '4' => [
        'product_id' => 4,
        'variation_id' => null,
        'quantity' => 1,
        'product_type' => 'digital'
    ]
];

// Test with 2 digital products
echo "<h1>Test Digital Product Count</h1>";
echo "<h2>Test 1: Cart with 2 digital products</h2>";
$count = count_digital_products_in_cart($test_session);
echo "Digital products count: " . $count . " (Expected: 2)<br>";
echo "Is count > 10? " . ($count > 10 ? 'Yes' : 'No') . " (Expected: No)<br><br>";

// Test with 11 digital products
echo "<h2>Test 2: Cart with 11 digital products</h2>";
// Add 9 more digital products
for ($i = 5; $i <= 13; $i++) {
    $test_session[$i] = [
        'product_id' => $i,
        'variation_id' => null,
        'quantity' => 1,
        'product_type' => 'digital'
    ];
}
$count = count_digital_products_in_cart($test_session);
echo "Digital products count: " . $count . " (Expected: 11)<br>";
echo "Is count > 10? " . ($count > 10 ? 'Yes' : 'No') . " (Expected: Yes)<br><br>";

// Test with products that don't have product_type set
echo "<h2>Test 3: Cart with products missing product_type</h2>";
$test_session_no_type = [
    '1' => [
        'product_id' => 1,
        'variation_id' => null,
        'quantity' => 1
    ],
    '3' => [
        'product_id' => 3,
        'variation_id' => null,
        'quantity' => 1
    ]
];

// Mock the database query for this test
function mock_db_query($sql, $params, $single = false) {
    if (strpos($sql, 'product_type') !== false && isset($params[':id'])) {
        // Return digital for product ID 3
        if ($params[':id'] == 3) {
            return ['product_type' => 'digital'];
        } else {
            return ['product_type' => 'regular'];
        }
    }
    return false;
}

// Override the db_query function for this test
function db_query($sql, $params = [], $single = false, $debug = false) {
    return mock_db_query($sql, $params, $single);
}

$count = count_digital_products_in_cart($test_session_no_type);
echo "Digital products count: " . $count . " (Expected: 1)<br>";
echo "Is count > 10? " . ($count > 10 ? 'Yes' : 'No') . " (Expected: No)<br><br>";

echo "<p>Tests completed. If all counts match expected values, the function is working correctly.</p>";
