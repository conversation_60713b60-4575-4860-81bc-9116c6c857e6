<?php

$is_editing = ($action === 'edit' && isset($item_id));
$form_title = $is_editing ? "Editar Cupão" : "Criar Novo Cupão";
$coupon_data = null;

if ($is_editing) {
    
    $coupon_data = get_coupon_by_id($item_id);
    if (!$coupon_data) {
        echo '<div class="alert alert-danger">Cupão não encontrado.</div>';
        return; 
    }
    $form_title .= " (#" . $coupon_data['id'] . ")";
}

$form_data = $form_data ?? [];

$code = $form_data['code'] ?? ($coupon_data['code'] ?? '');
$description = $form_data['description'] ?? ($coupon_data['description'] ?? '');
$discount_type = $form_data['discount_type'] ?? ($coupon_data['discount_type'] ?? 'percentage');
$discount_value = $form_data['discount_value'] ?? ($coupon_data['discount_value'] ?? '');
$min_order_value = $form_data['min_order_value'] ?? ($coupon_data['min_order_value'] ?? '0.00');
$usage_limit = $form_data['usage_limit'] ?? ($coupon_data['usage_limit'] ?? '');
$is_active = isset($form_data['is_active']) ? $form_data['is_active'] : ($coupon_data['is_active'] ?? 1);
$start_date = $form_data['start_date'] ?? ($coupon_data['start_date'] ?? '');
$end_date = $form_data['end_date'] ?? ($coupon_data['end_date'] ?? '');

if (!empty($start_date)) {
    $start_date = date('Y-m-d', strtotime($start_date));
}
if (!empty($end_date)) {
    $end_date = date('Y-m-d', strtotime($end_date));
}
?>

<h1><?= $form_title ?></h1>
<hr>

<?php display_flash_messages(); ?>

<form method="POST" action="admin.php?section=coupons&action=<?= $is_editing ? 'edit&id=' . $item_id : 'new' ?>&<?= get_session_id_param() ?>">
    <?= csrf_input_field() ?>
    
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informações Básicas</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="code" class="form-label">Código do Cupão *</label>
                        <input type="text" class="form-control" id="code" name="code" value="<?= htmlspecialchars($code) ?>" required>
                        <div class="form-text">O código que os clientes irão inserir para aplicar o desconto.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Descrição</label>
                        <textarea class="form-control" id="description" name="description" rows="3"><?= htmlspecialchars($description) ?></textarea>
                        <div class="form-text">Uma descrição interna para este cupão (não visível para os clientes).</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Tipo de Desconto *</label>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="discount_type" id="discount_type_percentage" value="percentage" <?= $discount_type === 'percentage' ? 'checked' : '' ?>>
                            <label class="form-check-label" for="discount_type_percentage">
                                Percentagem (%)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="discount_type" id="discount_type_fixed" value="fixed" <?= $discount_type === 'fixed' ? 'checked' : '' ?>>
                            <label class="form-check-label" for="discount_type_fixed">
                                Valor Fixo (€)
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="discount_value" class="form-label">Valor do Desconto *</label>
                        <input type="number" class="form-control" id="discount_value" name="discount_value" value="<?= htmlspecialchars($discount_value) ?>" step="0.01" min="0" required>
                        <div class="form-text">Para percentagem, insira o valor sem o símbolo % (ex: 10 para 10%).</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Restrições e Validade</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="min_order_value" class="form-label">Valor Mínimo de Encomenda</label>
                        <div class="input-group">
                            <span class="input-group-text">€</span>
                            <input type="number" class="form-control" id="min_order_value" name="min_order_value" value="<?= htmlspecialchars($min_order_value) ?>" step="0.01" min="0">
                        </div>
                        <div class="form-text">Valor mínimo de encomenda necessário para aplicar o cupão. Deixe 0 para não ter mínimo.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="usage_limit" class="form-label">Limite de Utilização</label>
                        <input type="number" class="form-control" id="usage_limit" name="usage_limit" value="<?= htmlspecialchars($usage_limit) ?>" min="1">
                        <div class="form-text">Número máximo de vezes que este cupão pode ser usado. Deixe em branco para uso ilimitado.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="start_date" class="form-label">Data de Início</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?= htmlspecialchars($start_date) ?>">
                        <div class="form-text">Data a partir da qual o cupão é válido. Deixe em branco para começar imediatamente.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="end_date" class="form-label">Data de Fim</label>
                        <input type="date" class="form-control" id="end_date" name="end_date" value="<?= htmlspecialchars($end_date) ?>">
                        <div class="form-text">Data até à qual o cupão é válido. Deixe em branco para não ter data de expiração.</div>
                    </div>
                    
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" <?= $is_active ? 'checked' : '' ?>>
                        <label class="form-check-label" for="is_active">Cupão Ativo</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <a href="admin.php?section=coupons&<?= get_session_id_param() ?>" class="btn btn-secondary">Cancelar</a>
                <div>
                    <button type="submit" name="save_and_continue" class="btn btn-success me-2">Guardar e Continuar</button>
                    <button type="submit" name="save_and_return" class="btn btn-primary">Guardar e Voltar</button>
                </div>
            </div>
        </div>
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle discount value label based on discount type
    const percentageRadio = document.getElementById('discount_type_percentage');
    const fixedRadio = document.getElementById('discount_type_fixed');
    const discountValueInput = document.getElementById('discount_value');
    const discountValueText = discountValueInput.nextElementSibling;
    
    function updateDiscountValueLabel() {
        if (percentageRadio.checked) {
            discountValueText.textContent = 'Para percentagem, insira o valor sem o símbolo % (ex: 10 para 10%).';
        } else {
            discountValueText.textContent = 'Valor fixo do desconto em euros.';
        }
    }
    
    percentageRadio.addEventListener('change', updateDiscountValueLabel);
    fixedRadio.addEventListener('change', updateDiscountValueLabel);
    
    // Initialize on page load
    updateDiscountValueLabel();
    
    // Validate end date is after start date
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    
    function validateDates() {
        if (startDateInput.value && endDateInput.value) {
            if (new Date(endDateInput.value) < new Date(startDateInput.value)) {
                endDateInput.setCustomValidity('A data de fim deve ser posterior à data de início');
            } else {
                endDateInput.setCustomValidity('');
            }
        } else {
            endDateInput.setCustomValidity('');
        }
    }
    
    startDateInput.addEventListener('change', validateDates);
    endDateInput.addEventListener('change', validateDates);
});
</script>
