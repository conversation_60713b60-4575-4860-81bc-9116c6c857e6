<?php
// Enable error reporting for debugging. Disable in production.
ini_set('display_errors', 1);
error_reporting(E_ALL);

// --- Configuration ---
$feedUrl    = "https://joaocesarsilva.com/scrape/produtos_jcs.xml";
$sitemapUrl = "https://joaocesarsilva.com/sitemap.xml";

// Your IndexNow settings, as provided
$indexNowKey = "9968fc656ba0468bbfb0f3369373e219";
// If you are not hosting the key file at the root, set it here:
$keyLocation = "https://www.joaocesarsilva.com/9968fc656ba0468bbfb0f3369373e219.txt";
$host        = "www.joaocesarsilva.com"; // Your domain name

// Define multiple endpoints for IndexNow submissions
$indexNowEndpoints = [
    'Bing'   => 'https://www.bing.com/indexnow',
    'Yep' => 'https://indexnow.yep.com/indexnow',
    'Naver' => 'https://searchadvisor.naver.com/indexnow',
    'IndexNow' => 'https://api.indexnow.org/indexnow'
    // Add additional endpoints if available:
    // 'AnotherSearchEngine' => 'https://another.search.endpoint/indexnow'
];

// --- Function to fetch remote content ---
function fetchContent($url) {
    $content = @file_get_contents($url);
    if ($content === false) {
        // Fallback to cURL if file_get_contents fails (e.g. allow_url_fopen disabled)
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        $content = curl_exec($ch);
        curl_close($ch);
    }
    return $content;
}

// --- 1. Get the feed URLs from the products XML ---
$feedContent = fetchContent($feedUrl);
if ($feedContent === false) {
    die("Error fetching feed content from $feedUrl");
}

$feedXml = simplexml_load_string($feedContent);
if ($feedXml === false) {
    die("Error parsing XML from feed file.");
}

$feedUrls = [];
if (isset($feedXml->channel->item)) {
    foreach ($feedXml->channel->item as $item) {
        // Extract URL from the <link> tag
        if (isset($item->link)) {
            $linkUrl = trim((string)$item->link);
            if (!empty($linkUrl)) {
                $feedUrls[] = $linkUrl;
            }
        }
        // Extract URL from the <guid> tag
        if (isset($item->guid)) {
            $guidUrl = trim((string)$item->guid);
            if (!empty($guidUrl)) {
                $feedUrls[] = $guidUrl;
            }
        }
        // Note: Extraction from the <image> tag has been intentionally removed.
    }
}

// --- 2. Get the sitemap URLs ---
$sitemapContent = fetchContent($sitemapUrl);
if ($sitemapContent === false) {
    die("Error fetching sitemap content from $sitemapUrl");
}

$sitemapXml = simplexml_load_string($sitemapContent);
if ($sitemapXml === false) {
    die("Error parsing XML from sitemap file.");
}

$sitemapUrls = [];
// The sitemap structure: <urlset><url><loc>...</loc></url></urlset>
foreach ($sitemapXml->url as $urlNode) {
    if (isset($urlNode->loc)) {
        $url = trim((string)$urlNode->loc);
        if (!empty($url)) {
            $sitemapUrls[] = $url;
        }
    }
}

// --- 3. Merge URLs (remove duplicates) ---
$allUrls = array_unique(array_merge($feedUrls, $sitemapUrls));

if (empty($allUrls)) {
    die("No URLs found in feed or sitemap.");
}

// --- 4. Build the JSON payload ---
// According to the documentation, you must include the "host", "key", optionally "keyLocation", and a list of URLs.
$payload = [
    "host"    => $host,
    "key"     => $indexNowKey,
    "urlList" => array_values($allUrls)
];

// Uncomment the next line to include keyLocation if you're not hosting the key file at the root.
// $payload["keyLocation"] = $keyLocation;

$jsonPayload = json_encode($payload, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
if ($jsonPayload === false) {
    die("Error encoding JSON payload.");
}

// --- 5. Submit to each IndexNow endpoint ---
$results = []; // To store results from each endpoint

foreach ($indexNowEndpoints as $engineName => $endpointUrl) {
    $ch = curl_init($endpointUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Content-Type: application/json; charset=utf-8",
        "Content-Length: " . strlen($jsonPayload)
    ]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonPayload);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);

    $response   = curl_exec($ch);
    $httpCode   = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError  = curl_error($ch);
    curl_close($ch);

    $results[$engineName] = [
        'endpoint' => $endpointUrl,
        'httpCode' => $httpCode,
        'response' => $response ? $response : "[empty]",
        'error'    => $curlError
    ];
}

// --- 6. Output the results ---
echo "<h2>IndexNow Submission Results</h2>";
foreach ($results as $engine => $result) {
    echo "<strong>$engine</strong> (Endpoint: {$result['endpoint']})<br>";
    if (!empty($result['error'])) {
        echo "Error: {$result['error']}<br>";
    } else {
        echo "HTTP Code: {$result['httpCode']}<br>";
        echo "Response: {$result['response']}<br>";
    }
    echo "<br>";
}

// For logging purposes, list the submitted URLs
echo "<h3>Submitted URLs:</h3>";
foreach ($allUrls as $url) {
    echo "$url<br>";
}
?>
