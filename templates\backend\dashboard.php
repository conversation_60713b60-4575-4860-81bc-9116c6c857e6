<?php

$date_range = $_GET['date_range'] ?? '30days';
$date_filters = [
    'today' => ['start' => "datetime('now', 'localtime', 'start of day')", 'end' => "datetime('now', 'localtime')"],
    '7days' => ['start' => "datetime('now', 'localtime', '-7 days')", 'end' => "datetime('now', 'localtime')"],
    '30days' => ['start' => "datetime('now', 'localtime', '-30 days')", 'end' => "datetime('now', 'localtime')"],
    'thismonth' => ['start' => "datetime('now', 'localtime', 'start of month')", 'end' => "datetime('now', 'localtime')"],
    'lastmonth' => ['start' => "datetime('now', 'localtime', 'start of month', '-1 month')", 'end' => "datetime('now', 'localtime', 'start of month', '-1 day')"],
    'thisyear' => ['start' => "datetime('now', 'localtime', 'start of year')", 'end' => "datetime('now', 'localtime')"],
    'all' => ['start' => "datetime('1970-01-01')", 'end' => "datetime('now', 'localtime')"]
];

$start_date = $date_filters[$date_range]['start'];
$end_date = $date_filters[$date_range]['end'];

$order_count = db_query("SELECT COUNT(id) as count FROM orders", [], true)['count'] ?? 0;
$product_count = db_query("SELECT COUNT(id) as count FROM products WHERE is_active = 1", [], true)['count'] ?? 0;
$pending_orders = db_query("SELECT COUNT(id) as count FROM orders WHERE status = 'pending'", [], true)['count'] ?? 0;
$new_messages = db_query("SELECT COUNT(id) as count FROM contacts WHERE status = 'new'", [], true)['count'] ?? 0;

$total_sales = db_query("SELECT SUM(total_amount) as total FROM orders WHERE created_at BETWEEN {$start_date} AND {$end_date}", [], true)['total'] ?? 0;
$completed_orders = db_query("SELECT COUNT(id) as count FROM orders WHERE status = 'completed'", [], true)['count'] ?? 0;
$digital_products = db_query("SELECT COUNT(id) as count FROM digital_products", [], true)['count'] ?? 0;
$blog_posts = db_query("SELECT COUNT(id) as count FROM blog_posts WHERE is_published = 1", [], true)['count'] ?? 0;

$recent_orders = db_query(
    "SELECT id, order_ref, total_amount, status, created_at,
            json_extract(customer_info_json, '$.customer_name') as customer_name,
            json_extract(customer_info_json, '$.customer_email') as customer_email
     FROM orders
     ORDER BY created_at DESC
     LIMIT 5",
    [],
    false,
    true
) ?? [];

$recent_messages = db_query(
    "SELECT id, name, email, subject, created_at, status
     FROM contacts
     ORDER BY created_at DESC
     LIMIT 5",
    [],
    false,
    true
) ?? [];

$order_status_data = db_query(
    "SELECT status, COUNT(id) as count
     FROM orders
     GROUP BY status
     ORDER BY count DESC",
    [],
    false,
    true
) ?? [];

$sales_data = db_query(
    "WITH RECURSIVE dates(date) AS (
        VALUES(date('now', '-6 days'))
        UNION ALL
        SELECT date(date, '+1 day') FROM dates WHERE date < date('now')
    )
    SELECT dates.date, COALESCE(SUM(o.total_amount), 0) as amount
    FROM dates
    LEFT JOIN orders o ON date(o.created_at) = dates.date
    GROUP BY dates.date
    ORDER BY dates.date",
    [],
    false,
    true
) ?? [];

$category_data = db_query(
    "SELECT c.name, COUNT(pc.product_id) as count
     FROM categories c
     LEFT JOIN product_categories pc ON c.id = pc.category_id
     GROUP BY c.id
     ORDER BY count DESC
     LIMIT 10",
    [],
    false,
    true
) ?? [];

$db_size = db_query("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()", [], true)['size'] ?? 0;
$db_size_mb = round($db_size / (1024 * 1024), 2);

$sales_chart_data = json_encode($sales_data);
$order_status_chart_data = json_encode($order_status_data);
$category_chart_data = json_encode($category_data);

$current_datetime = date('d/m/Y H:i');
?>

<!-- Dashboard Header with Date Filter -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="mb-0">Dashboard</h1>
        <p class="text-muted">Visão geral da sua loja</p>
    </div>
    <div>
        <form method="get" action="admin.php" class="d-flex align-items-center">
            <input type="hidden" name="section" value="dashboard">
            <input type="hidden" name="<?= session_name() ?>" value="<?= session_id() ?>">
            <label for="dashboardDateRange" class="me-2">Período:</label>
            <select id="dashboardDateRange" name="date_range" class="form-select form-select-sm">
                <option value="today" <?= $date_range == 'today' ? 'selected' : '' ?>>Hoje</option>
                <option value="7days" <?= $date_range == '7days' ? 'selected' : '' ?>>Últimos 7 dias</option>
                <option value="30days" <?= $date_range == '30days' ? 'selected' : '' ?>>Últimos 30 dias</option>
                <option value="thismonth" <?= $date_range == 'thismonth' ? 'selected' : '' ?>>Este mês</option>
                <option value="lastmonth" <?= $date_range == 'lastmonth' ? 'selected' : '' ?>>Mês passado</option>
                <option value="thisyear" <?= $date_range == 'thisyear' ? 'selected' : '' ?>>Este ano</option>
                <option value="all" <?= $date_range == 'all' ? 'selected' : '' ?>>Todo o período</option>
            </select>
        </form>
    </div>
</div>

<!-- Welcome Section -->
<div class="welcome-section mb-4">
    <div class="row">
        <div class="col-md-8">
            <h2>Bem-vindo à área de administração</h2>
            <p>Gerencie sua loja, produtos, encomendas e conteúdo em um só lugar.</p>
        </div>
        <div class="col-md-4 text-md-end">
            <div class="date-time">
                <i class="bi bi-calendar3"></i> <?= $current_datetime ?>
            </div>
        </div>
    </div>
</div>

<!-- Stats Overview -->
<div class="row g-4 mb-4">
    <!-- Sales Stats -->
    <div class="col-md-3">
        <div class="card stat-card bg-primary text-white h-100">
            <div class="card-body">
                <div class="stat-icon">
                    <i class="bi bi-currency-euro"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-value"><?= number_format($total_sales, 2, ',', '.') ?> €</div>
                    <div class="stat-label">Vendas Totais</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders Stats -->
    <div class="col-md-3">
        <div class="card stat-card bg-success text-white h-100">
            <div class="card-body">
                <div class="stat-icon">
                    <i class="bi bi-box-seam"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-value"><?= $order_count ?></div>
                    <div class="stat-label">Encomendas</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Orders Stats -->
    <div class="col-md-3">
        <div class="card stat-card bg-warning text-dark h-100">
            <div class="card-body">
                <div class="stat-icon">
                    <i class="bi bi-hourglass-split"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-value"><?= $pending_orders ?></div>
                    <div class="stat-label">Encomendas Pendentes</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages Stats -->
    <div class="col-md-3">
        <div class="card stat-card bg-info text-white h-100">
            <div class="card-body">
                <div class="stat-icon">
                    <i class="bi bi-envelope"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-value"><?= $new_messages ?></div>
                    <div class="stat-label">Novas Mensagens</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Stats -->
<div class="row g-4 mb-4">
    <!-- Products Stats -->
    <div class="col-md-3">
        <div class="card stat-card h-100">
            <div class="card-body">
                <div class="stat-icon text-primary">
                    <i class="bi bi-bag"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-value"><?= $product_count ?></div>
                    <div class="stat-label">Produtos Ativos</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Digital Products Stats -->
    <div class="col-md-3">
        <div class="card stat-card h-100">
            <div class="card-body">
                <div class="stat-icon text-primary">
                    <i class="bi bi-file-earmark-zip"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-value"><?= $digital_products ?></div>
                    <div class="stat-label">Produtos Digitais</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Completed Orders Stats -->
    <div class="col-md-3">
        <div class="card stat-card h-100">
            <div class="card-body">
                <div class="stat-icon text-success">
                    <i class="bi bi-check-circle"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-value"><?= $completed_orders ?></div>
                    <div class="stat-label">Encomendas Concluídas</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Blog Posts Stats -->
    <div class="col-md-3">
        <div class="card stat-card h-100">
            <div class="card-body">
                <div class="stat-icon text-primary">
                    <i class="bi bi-file-earmark-text"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-value"><?= $blog_posts ?></div>
                    <div class="stat-label">Artigos Publicados</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Dashboard Content -->
<div class="row">
    <!-- Single Column Layout -->
    <div class="col-12">
        <!-- Sales Chart Widget -->
        <div id="sales-widget" class="dashboard-widget mb-4">
            <div class="widget-header">
                <h5 class="widget-title"><i class="bi bi-graph-up me-2"></i> Vendas Recentes</h5>
                <div class="widget-actions">
                    <button type="button" class="btn btn-sm btn-outline-secondary refresh-widget" data-widget="sales-widget">
                        <i class="bi bi-arrow-repeat"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary widget-collapse-icon">
                        <i class="bi bi-chevron-up"></i>
                    </button>
                </div>
            </div>
            <div class="widget-body show">
                <div class="chart-container">
                    <canvas id="salesChart" data-sales='<?= $sales_chart_data ?>'></canvas>
                </div>
            </div>
        </div>

        <!-- Recent Orders Widget -->
        <div id="recent-orders-widget" class="dashboard-widget mb-4">
            <div class="widget-header">
                <h5 class="widget-title"><i class="bi bi-box-seam me-2"></i> Encomendas Recentes</h5>
                <div class="widget-actions">
                    <a href="admin.php?section=orders&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-primary">
                        Ver Todas
                    </a>
                    <button type="button" class="btn btn-sm btn-outline-secondary refresh-widget" data-widget="recent-orders-widget">
                        <i class="bi bi-arrow-repeat"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary widget-collapse-icon">
                        <i class="bi bi-chevron-up"></i>
                    </button>
                </div>
            </div>
            <div class="widget-body show">
                <?php if (empty($recent_orders)): ?>
                    <div class="alert alert-info">
                        Nenhuma encomenda encontrada.
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover recent-items-table">
                            <thead>
                                <tr>
                                    <th>Ref.</th>
                                    <th>Cliente</th>
                                    <th>Data</th>
                                    <th>Valor</th>
                                    <th>Estado</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_orders as $order): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($order['order_ref']) ?></td>
                                        <td><?= htmlspecialchars($order['customer_name']) ?></td>
                                        <td><?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></td>
                                        <td><?= number_format($order['total_amount'], 2, ',', '.') ?> €</td>
                                        <td>
                                            <?php
                                            $status_class = '';
                                            switch ($order['status']) {
                                                case 'pending': $status_class = 'pending'; break;
                                                case 'processing': $status_class = 'processing'; break;
                                                case 'completed': $status_class = 'completed'; break;
                                                case 'cancelled': $status_class = 'cancelled'; break;
                                                default: $status_class = ''; break;
                                            }
                                            ?>
                                            <span class="status-badge <?= $status_class ?>"><?= htmlspecialchars($order['status']) ?></span>
                                        </td>
                                        <td>
                                            <a href="admin.php?section=orders&action=detail&id=<?= $order['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Messages Widget -->
        <div id="recent-messages-widget" class="dashboard-widget mb-4">
            <div class="widget-header">
                <h5 class="widget-title"><i class="bi bi-envelope me-2"></i> Mensagens Recentes</h5>
                <div class="widget-actions">
                    <a href="admin.php?section=messages&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-primary">
                        Ver Todas
                    </a>
                    <button type="button" class="btn btn-sm btn-outline-secondary refresh-widget" data-widget="recent-messages-widget">
                        <i class="bi bi-arrow-repeat"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary widget-collapse-icon">
                        <i class="bi bi-chevron-up"></i>
                    </button>
                </div>
            </div>
            <div class="widget-body show">
                <?php if (empty($recent_messages)): ?>
                    <div class="alert alert-info">
                        Nenhuma mensagem encontrada.
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover recent-items-table">
                            <thead>
                                <tr>
                                    <th>Nome</th>
                                    <th>Email</th>
                                    <th>Assunto</th>
                                    <th>Data</th>
                                    <th>Estado</th>
                                    <th>Ações</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_messages as $message): ?>
                                    <tr>
                                        <td><?= htmlspecialchars($message['name']) ?></td>
                                        <td><?= htmlspecialchars($message['email']) ?></td>
                                        <td><?= htmlspecialchars($message['subject']) ?></td>
                                        <td><?= date('d/m/Y H:i', strtotime($message['created_at'])) ?></td>
                                        <td>
                                            <?php if ($message['status'] == 'new'): ?>
                                                <span class="badge bg-danger">Novo</span>
                                            <?php elseif ($message['status'] == 'read'): ?>
                                                <span class="badge bg-info">Lido</span>
                                            <?php elseif ($message['status'] == 'replied'): ?>
                                                <span class="badge bg-success">Respondido</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary"><?= htmlspecialchars($message['status']) ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="admin.php?section=messages&action=view&id=<?= $message['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <!-- Quick Actions Widget -->
        <div id="quick-actions-widget" class="dashboard-widget mb-4">
            <div class="widget-header">
                <h5 class="widget-title"><i class="bi bi-lightning-charge me-2"></i> Ações Rápidas</h5>
                <div class="widget-actions">
                    <button type="button" class="btn btn-sm btn-outline-secondary widget-collapse-icon">
                        <i class="bi bi-chevron-up"></i>
                    </button>
                </div>
            </div>
            <div class="widget-body show">
                <div class="quick-actions-grid">
                    <a href="admin.php?section=products&action=new&<?= get_session_id_param() ?>" class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="bi bi-plus-circle"></i>
                        </div>
                        <div class="quick-action-title">Novo Produto</div>
                        <div class="quick-action-description">Adicionar produto ao catálogo</div>
                    </a>

                    <a href="admin.php?section=blog_posts&action=new&<?= get_session_id_param() ?>" class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="bi bi-file-earmark-plus"></i>
                        </div>
                        <div class="quick-action-title">Novo Artigo</div>
                        <div class="quick-action-description">Criar artigo para o blog</div>
                    </a>

                    <a href="admin.php?section=pages&action=new&<?= get_session_id_param() ?>" class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="bi bi-file-earmark-text"></i>
                        </div>
                        <div class="quick-action-title">Nova Página</div>
                        <div class="quick-action-description">Adicionar página ao site</div>
                    </a>

                    <a href="admin.php?section=coupons&action=new&<?= get_session_id_param() ?>" class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="bi bi-ticket-perforated"></i>
                        </div>
                        <div class="quick-action-title">Novo Cupão</div>
                        <div class="quick-action-description">Criar cupão de desconto</div>
                    </a>

                    <a href="admin.php?section=settings&<?= get_session_id_param() ?>" class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="bi bi-gear"></i>
                        </div>
                        <div class="quick-action-title">Configurações</div>
                        <div class="quick-action-description">Ajustar configurações da loja</div>
                    </a>

                    <a href="admin.php?section=maintenance&<?= get_session_id_param() ?>" class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="bi bi-tools"></i>
                        </div>
                        <div class="quick-action-title">Manutenção</div>
                        <div class="quick-action-description">Tarefas de manutenção</div>
                    </a>

                    <a href="admin.php?section=digital_files&<?= get_session_id_param() ?>" class="quick-action-card">
                        <div class="quick-action-icon">
                            <i class="bi bi-file-earmark-zip"></i>
                        </div>
                        <div class="quick-action-title">Arquivos Digitais</div>
                        <div class="quick-action-description">Gerenciar arquivos digitais</div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Order Status Chart Widget -->
        <div id="order-status-widget" class="dashboard-widget mb-4">
            <div class="widget-header">
                <h5 class="widget-title"><i class="bi bi-pie-chart me-2"></i> Estado das Encomendas</h5>
                <div class="widget-actions">
                    <button type="button" class="btn btn-sm btn-outline-secondary refresh-widget" data-widget="order-status-widget">
                        <i class="bi bi-arrow-repeat"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary widget-collapse-icon">
                        <i class="bi bi-chevron-up"></i>
                    </button>
                </div>
            </div>
            <div class="widget-body show">
                <div class="chart-container">
                    <canvas id="orderStatusChart" data-status='<?= $order_status_chart_data ?>'></canvas>
                </div>
            </div>
        </div>

        <!-- Product Categories Chart Widget -->
        <div id="product-categories-widget" class="dashboard-widget mb-4">
            <div class="widget-header">
                <h5 class="widget-title"><i class="bi bi-bar-chart me-2"></i> Categorias de Produtos</h5>
                <div class="widget-actions">
                    <button type="button" class="btn btn-sm btn-outline-secondary refresh-widget" data-widget="product-categories-widget">
                        <i class="bi bi-arrow-repeat"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary widget-collapse-icon">
                        <i class="bi bi-chevron-up"></i>
                    </button>
                </div>
            </div>
            <div class="widget-body show">
                <div class="chart-container">
                    <canvas id="productCategoryChart" data-categories='<?= $category_chart_data ?>'></canvas>
                </div>
            </div>
        </div>

        <!-- System Status Widget -->
        <div id="system-status-widget" class="dashboard-widget mb-4">
            <div class="widget-header">
                <h5 class="widget-title"><i class="bi bi-info-circle me-2"></i> Estado do Sistema</h5>
                <div class="widget-actions">
                    <button type="button" class="btn btn-sm btn-outline-secondary refresh-widget" data-widget="system-status-widget">
                        <i class="bi bi-arrow-repeat"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary widget-collapse-icon">
                        <i class="bi bi-chevron-up"></i>
                    </button>
                </div>
            </div>
            <div class="widget-body show">
                <div class="system-status-item">
                    <div class="system-status-icon healthy">
                        <i class="bi bi-database-check"></i>
                    </div>
                    <div class="system-status-content">
                        <div class="system-status-label">Tamanho da Base de Dados</div>
                        <div class="system-status-value"><?= $db_size_mb ?> MB</div>
                    </div>
                </div>

                <div class="system-status-item">
                    <div class="system-status-icon healthy">
                        <i class="bi bi-hdd-stack"></i>
                    </div>
                    <div class="system-status-content">
                        <div class="system-status-label">Versão PHP</div>
                        <div class="system-status-value"><?= phpversion() ?></div>
                    </div>
                </div>

                <div class="system-status-item">
                    <div class="system-status-icon healthy">
                        <i class="bi bi-clock-history"></i>
                    </div>
                    <div class="system-status-content">
                        <div class="system-status-label">Última Atualização</div>
                        <div class="system-status-value"><?= date('d/m/Y H:i', filemtime(__FILE__)) ?></div>
                    </div>
                </div>

                <div class="mt-3">
                    <a href="admin.php?section=maintenance&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-primary w-100">
                        <i class="bi bi-tools me-2"></i> Ir para Manutenção
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
