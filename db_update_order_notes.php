<?php

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/functions.php';

set_time_limit(300);

$pdo = get_db_connection();
if (!$pdo) {
    die("Failed to connect to the database.");
}

echo "Starting migration to add order_notes field to customer_info_json for existing orders...\n";

try {
    
    $sql = "SELECT id, customer_info_json FROM orders";
    $stmt = $pdo->query($sql);
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $total_orders = count($orders);
    $updated_orders = 0;
    
    echo "Found {$total_orders} orders to process.\n";
    
    foreach ($orders as $order) {
        $order_id = $order['id'];
        $customer_info = json_decode($order['customer_info_json'], true);
        
        
        if (!isset($customer_info['order_notes'])) {
            
            $customer_info['order_notes'] = '';
            
            
            $update_sql = "UPDATE orders SET customer_info_json = :customer_info_json WHERE id = :id";
            $update_stmt = $pdo->prepare($update_sql);
            $update_stmt->execute([
                ':id' => $order_id,
                ':customer_info_json' => json_encode($customer_info)
            ]);
            
            $updated_orders++;
        }
    }
    
    echo "Migration completed successfully. Updated {$updated_orders} out of {$total_orders} orders.\n";
    
} catch (PDOException $e) {
    echo "Error during migration: " . $e->getMessage() . "\n";
}
