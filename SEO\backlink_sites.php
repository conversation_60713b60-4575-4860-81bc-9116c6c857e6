<?php
// Backlink sites list for the backlink pinging tool

$backlink_sites = [
    'Ping-O-Matic' => 'http://pingomatic.com/ping/',
    'Blogsearch' => 'http://blogsearch.google.com/ping',
    'Pingler' => 'https://pingler.com/',
    'Pingfarm' => 'http://www.pingfarm.com/',
    'Pingoat' => 'http://www.pingoat.com/',
    'Pingmylinks' => 'http://www.pingmylinks.com/ping/',
    'Pingbomb' => 'http://pingbomb.com/',
    'Pingking' => 'http://www.pingking.com/',
    'Pingability' => 'http://www.pingability.com/',
    'Pingoo' => 'http://www.pingoo.com/',
    'Pingomatic' => 'https://pingomatic.com/',
    'FreeBacklinks' => 'https://free-backlinks.net/ping-my-url.html',
    'PingMyLink' => 'https://ping-my-url.net/',
    'PrePostSEO' => 'https://www.prepostseo.com/ping-multiple-urls-online',
    'WebNots' => 'https://www.webnots.com/seo-tools/ping-multiple-urls-online',
    'DupliChecker' => 'https://www.duplichecker.com/search-engine-pinging-website-tool.php',
    'NimTools' => 'https://nimtools.com/online-ping-website-tool',
    'SEOPolarity' => 'https://seopolarity.com/online-ping-website-tool',
    'CodersTool' => 'https://www.coderstool.com/ping-website-url',
    'IPAddressGuide' => 'https://www.ipaddressguide.com/ping',
    'CoderDuck' => 'https://www.coderduck.com/ping-website-tool-fetch-as-google',
    'TheSEOTools' => 'https://theseotools.net/online-ping-website-tool',
    'W3era' => 'https://www.w3era.com/tool/online-ping-website-tool',
    'SEO1seotools' => 'https://seo1seotools.com/ping-my-url/',
    'AToZSEOTool' => 'https://www.dotcominfoway.com/free-seo-tools/online-ping-website-tool',
    'ExciteSubmit' => 'https://www.excitesubmit.com/',
    'Ping.in' => 'https://ping.in/',
    'MassPing' => 'https://wmtools.me/mass-ping',
    'AutoPinger' => 'https://www.autopinger.com/',
    'DomainPinger' => 'https://www.domainpinger.com/',
    'Ping' => 'https://www.ping.eu/',
    'PingBlog' => 'https://www.pingblog.com/',
    'PingTool' => 'https://pingtool.org/',
    'Feedburner' => 'https://feedburner.google.com/',
    'Blogs Yandex' => 'https://blogs.yandex.ru',
    'blogsearch google ping' => 'https://blogsearch.google.com/ping',
    'blogsearch google' => 'https://blogsearch.google.com',
];