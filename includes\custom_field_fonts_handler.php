<?php

function handle_custom_field_fonts_form($action, $font_id = null) {
    
    $font_name = trim($_POST['name'] ?? '');
    $font_type = $_POST['font_type'] ?? '';
    $google_font_name = trim($_POST['google_font_name'] ?? '');
    $is_active = isset($_POST['is_active']) ? (int)$_POST['is_active'] : 1;
    
    
    $errors = [];
    if (empty($font_name)) {
        $errors[] = "Nome da fonte é obrigatório.";
    }
    
    if ($font_type === 'google' && empty($google_font_name)) {
        $errors[] = "Nome da fonte do Google é obrigatório.";
    }
    
    if ($font_type === 'file' && $action === 'create' && (!isset($_FILES['font_file']) || $_FILES['font_file']['error'] === UPLOAD_ERR_NO_FILE)) {
        $errors[] = "Ficheiro de fonte é obrigatório.";
    }
    
    if (!empty($errors)) {
        foreach ($errors as $error) {
            add_flash_message($error, 'danger');
        }
        $_SESSION['form_data'] = $_POST;
        $redirect_url = 'admin.php?section=custom_field_fonts&action=';
        $redirect_url .= ($action === 'create') ? 'new' : 'edit&id=' . $font_id;
        header('Location: ' . $redirect_url . '&' . get_session_id_param());
        exit;
    }
    
    try {
        
        $font_data = [
            'name' => $font_name,
            'is_active' => $is_active
        ];
        
        
        if ($font_type === 'google') {
            $font_data['google_font_name'] = $google_font_name;
            $font_data['file_path'] = null;
        } elseif ($font_type === 'file') {
            $font_data['google_font_name'] = null;
            
            
            if (isset($_FILES['font_file']) && $_FILES['font_file']['error'] === UPLOAD_ERR_OK) {
                $upload_dir = __DIR__ . '/../public/assets/fonts/';
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }
                
                $file_name = $_FILES['font_file']['name'];
                $file_tmp = $_FILES['font_file']['tmp_name'];
                $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
                
                
                $allowed_exts = ['ttf', 'otf', 'woff', 'woff2'];
                if (!in_array($file_ext, $allowed_exts)) {
                    throw new Exception("Tipo de ficheiro não suportado. Formatos permitidos: TTF, OTF, WOFF, WOFF2.");
                }
                
                
                $new_filename = 'font_' . time() . '_' . uniqid() . '.' . $file_ext;
                $file_path = $upload_dir . $new_filename;
                
                
                if (move_uploaded_file($file_tmp, $file_path)) {
                    $font_data['file_path'] = 'public/assets/fonts/' . $new_filename;
                } else {
                    throw new Exception("Falha ao mover o ficheiro carregado.");
                }
            } elseif ($action === 'update') {
                
                if ($font_id) {
                    $existing_font = get_custom_field_font($font_id);
                    if ($existing_font && !empty($existing_font['file_path'])) {
                        $font_data['file_path'] = $existing_font['file_path'];
                    }
                }
            }
        }
        
        
        if ($action === 'create') {
            $result = create_custom_field_font($font_data);
            if ($result) {
                add_flash_message('Fonte criada com sucesso!', 'success');
            } else {
                throw new Exception("Falha ao criar fonte.");
            }
        } else { 
            if (!$font_id) {
                throw new Exception("ID da fonte inválido.");
            }
            
            $result = update_custom_field_font($font_id, $font_data);
            if ($result) {
                add_flash_message('Fonte atualizada com sucesso!', 'success');
            } else {
                throw new Exception("Falha ao atualizar fonte.");
            }
        }
        
        header('Location: admin.php?section=custom_field_fonts&' . get_session_id_param());
        exit;
        
    } catch (Exception $e) {
        add_flash_message('Erro ao guardar fonte: ' . $e->getMessage(), 'danger');
        $_SESSION['form_data'] = $_POST;
        $redirect_url = 'admin.php?section=custom_field_fonts&action=';
        $redirect_url .= ($action === 'create') ? 'new' : 'edit&id=' . $font_id;
        header('Location: ' . $redirect_url . '&' . get_session_id_param());
        exit;
    }
}
