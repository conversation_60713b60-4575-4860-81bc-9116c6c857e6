<?php

require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/order_functions.php';

function cleanup_orphaned_custom_field_files() {
    $results = [
        'success' => true,
        'message' => 'Custom field files cleanup completed successfully.',
        'details' => [],
        'deleted' => 0,
        'failed' => 0,
        'not_found' => 0
    ];

    try {
        $pdo = get_db_connection();

        
        $sql = "SELECT DISTINCT file_path FROM order_item_custom_fields
                WHERE file_path IS NOT NULL AND file_path != ''";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();

        $db_file_paths = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            if (!empty($row['file_path'])) {
                $db_file_paths[] = $row['file_path'];
            }
        }

        
        $upload_dir = PROJECT_ROOT . '/public/uploads/custom_fields/';
        if (!file_exists($upload_dir)) {
            $results['details'][] = "Custom fields upload directory does not exist.";
            return $results;
        }

        $files = scandir($upload_dir);
        $orphaned_files = [];

        
        $files = array_diff($files, ['.', '..']);

        foreach ($files as $file) {
            $file_path = '/public/uploads/custom_fields/' . $file;

            
            if (!in_array($file_path, $db_file_paths)) {
                $orphaned_files[] = $file_path;
            }
        }

        
        $delete_results = delete_custom_field_files($orphaned_files);

        $results['deleted'] = $delete_results['deleted'];
        $results['failed'] = $delete_results['failed'];
        $results['not_found'] = $delete_results['not_found'];
        $results['details'][] = "Found " . count($orphaned_files) . " orphaned custom field files.";
        $results['details'][] = "Deleted {$delete_results['deleted']} orphaned files, failed to delete {$delete_results['failed']}.";

    } catch (Exception $e) {
        $error_message = "Custom field files cleanup error: " . $e->getMessage();

        $results['success'] = false;
        $results['message'] = 'Error during custom field files cleanup.';
        $results['details'][] = $error_message;
    }

    return $results;
}

function cleanup_database() {
    $pdo = get_db_connection();
    $results = [
        'success' => true,
        'message' => 'Database cleanup completed successfully.',
        'details' => []
    ];

    try {
        
        $pdo->beginTransaction();

        
        $sql = "DELETE FROM order_item_custom_fields
                WHERE order_item_id NOT IN (SELECT id FROM order_items)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $count = $stmt->rowCount();
        $results['details'][] = "Removed $count orphaned custom field records.";

        
        $sql = "DELETE FROM order_items
                WHERE order_id NOT IN (SELECT id FROM orders)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $count = $stmt->rowCount();
        $results['details'][] = "Removed $count orphaned order item records.";

        
        $sql = "DELETE FROM order_item_custom_fields
                WHERE id NOT IN (
                    SELECT MIN(id)
                    FROM order_item_custom_fields
                    GROUP BY order_item_id, custom_field_id, field_name, field_value, file_path
                )";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $count = $stmt->rowCount();
        $results['details'][] = "Removed $count duplicate custom field records.";

        
        $pdo->commit();

        

        
        $file_cleanup_results = cleanup_orphaned_custom_field_files();
        $results['details'] = array_merge($results['details'], $file_cleanup_results['details']);

        if (!$file_cleanup_results['success']) {
            $results['message'] = 'Database cleanup completed with file cleanup errors.';
        }

    } catch (PDOException $e) {
        
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }

        $error_message = "Database cleanup error: " . $e->getMessage();

        $results['success'] = false;
        $results['message'] = 'Error during database cleanup.';
        $results['details'][] = $error_message;
    }

    return $results;
}

function run_database_maintenance() {
    $results = [
        'success' => true,
        'message' => 'Database maintenance completed successfully.',
        'details' => []
    ];

    
    $cleanup_results = cleanup_database();
    $results['details'][] = $cleanup_results['message'];
    $results['details'] = array_merge($results['details'], $cleanup_results['details']);

    
    if (!$cleanup_results['success']) {
        $results['success'] = false;
        $results['message'] = 'Database maintenance encountered errors.';
    }

    return $results;
}
