---

**24. Admin: Product Creation Date (`created_at`) Not Saving for Digital/Variation Products**
*   **Issue**: When editing a product in `admin.php?section=products&action=edit`, if a new "Data de Criação" (`created_at`) was set, this new date was not being saved to the database for products of type 'digital' or 'variation'. It only saved correctly for 'regular' (simple) products.
*   **Root Cause**: The POST request handling logic in [`admin.php`](admin.php:1) for updating products (specifically within the `elseif ($action === 'edit' && $item_id)` block for `section=products`) only included the conditional update of the `created_at` field in its SQL `UPDATE` statement for 'regular' products. The logic for 'digital' and 'variation' product types did not include this conditional check and field assignment, even if `$_POST['created_at']` was set and parsed into `$formatted_created_at`.
*   **Solution**:
    1.  Modified the product update logic in [`admin.php`](admin.php:1) (around lines [`admin.php:2091`](admin.php:2091) and [`admin.php:2122`](admin.php:2122)) for 'digital' and 'variation' product types.
    2.  The SQL `UPDATE` string and the `$params` array are now built dynamically for these types as well, similar to 'regular' products. If `$formatted_created_at` (derived from `$_POST['created_at']`) is valid, `, created_at = :created_at` is added to the SQL query and `:created_at` is added to the parameters.
    3.  This ensures that if a new, valid creation date is submitted through the form, it will be saved for all product types.
    4.  The help text for the `created_at` input field in [`templates/backend/product_form.php`](templates/backend/product_form.php:142) was also corrected from "YYYY-MM-DD HH:MM:SS" to "YYYY-MM-DDTHH:MM" to match the `datetime-local` input's expected format and the backend parsing.
*   **Prevention & Key Learnings**:
    *   When implementing conditional field updates in CRUD operations, ensure the logic consistently applies to all relevant subtypes or conditions.
    *   Verify that UI help text accurately reflects the data format expected by both the input field and the backend processing logic.
    *   Code for similar product types should be carefully reviewed to ensure all fields are handled consistently where appropriate.
*   **Status**: Fixed.
---

**23. UI: Floating Buttons Overlap & Filter Bar State Not Persisting**
*   **Issue 1 (Overlap)**: On pages displaying the product filter toggle button (e.g., homepage, search results), this button would overlap with the site-wide "scroll-to-top" button when the page was scrolled down, particularly on smaller screens.
*   **Root Cause 1**: The CSS positioning classes (Tailwind CSS) for the two fixed-position buttons did not provide sufficient vertical separation and consistent horizontal alignment across different screen sizes. The filter toggle is in [`templates/frontend/partials/collapsible_filters.php`](templates/frontend/partials/collapsible_filters.php) and the scroll-to-top is in [`templates/frontend/partials/footer.php`](templates/frontend/partials/footer.php).
*   **Solution 1 (Iterative)**:
    1.  Initial attempt: Increased the `bottom` offset of the scroll-to-top button. This reduced overlap but didn't align them vertically.
    2.  Final solution:
        *   Modified the scroll-to-top button's classes in [`templates/frontend/partials/footer.php`](templates/frontend/partials/footer.php) from `bottom-6 right-6` to `bottom-24 right-4 md:right-8`. This aligns it horizontally with the filter button (which uses `right-4 md:right-8`) and provides significantly more vertical clearance (`bottom-24` or 6rem vs. filter button's `bottom-4` or `md:bottom-8`).
*   **Issue 2 (State Persistence)**: The collapsible product filter bar's visibility state (expanded/collapsed) was not remembered across page loads or refreshes.
*   **Root Cause 2**: The filter bar's state was managed only in client-side JavaScript for the current page view without any persistence mechanism.
*   **Solution 2**:
    *   Modified the JavaScript in [`templates/frontend/partials/collapsible_filters.php`](templates/frontend/partials/collapsible_filters.php).
    *   Used `localStorage` to store the filter bar's state (`'expanded'` or `'collapsed'`) under the key `'filterBarState'`.
    *   On `DOMContentLoaded`, the script reads this state from `localStorage` and applies it to the filter bar.
    *   When the toggle button is clicked, the new state is saved to `localStorage`.
*   **Prevention & Key Learnings**:
    *   Thoroughly test fixed-position UI elements across various screen sizes, scroll depths, and in relation to other fixed elements.
    *   Use `localStorage` for simple, non-critical client-side UI state persistence to enhance user experience. Ensure graceful degradation if `localStorage` is unavailable (though widely supported).
    *   Iterative refinement of CSS utility classes is often necessary for achieving precise responsive layouts with multiple fixed elements.
    *   When restoring state on page load, ensure that dynamic properties like `scrollHeight` are accessed after the DOM is fully settled (e.g., using a minimal `setTimeout` or ensuring script runs late).
*   **Status**: Fixed.
---

**22. UI: Product Form Icon Selector Fails After Image Swap Implementation (Informações Adicionais)**
*   **Issue**: After implementing an image order swapping feature in [`public/assets/js/admin-product-images.js`](public/assets/js/admin-product-images.js), the icon selector dropdowns for "Informações Adicionais do Produto" stopped working. The issue was present on both the product edit page ([`admin.php?section=products&action=edit`](admin.php?section=products&action=edit)) and the new product page ([`admin.php?section=products&action=new`](admin.php?section=products&action=new)), particularly for dynamically added fields.
*   **Root Causes**:
    1.  **Event Propagation Conflict**: The primary cause appeared to be an event propagation conflict. The original `admin-icon-selector.js` might have been stopping event propagation in a way that interfered with Bootstrap's default dropdown handling, or another script (potentially `admin-product-images.js` or a global handler) was interfering with the dropdown's event chain.
    2.  **Bootstrap Initialization for Dynamic Elements**: Bootstrap's Dropdown component was not reliably initializing for icon selector buttons added dynamically to the DOM (e.g., when clicking "Adicionar Campo" on the new product page). Standard `data-bs-toggle="dropdown"` attributes were not always sufficient without explicit JavaScript initialization for these dynamic elements.
*   **Solution (Multi-step)**:
    *   **In [`public/assets/js/admin-icon-selector.js`](public/assets/js/admin-icon-selector.js):**
        1.  **Enable Event Bubbling for Toggle Button**: Ensured `event.stopPropagation()` was **active** (uncommented) in the direct click listener for the `dropdownButton`. This allows the button's click to be fully processed by its own handler.
        2.  **Bootstrap Auto-Close Behavior**: Confirmed the `dropdownButton` had the `data-bs-auto-close="outside"` attribute. This is crucial for allowing interaction with elements *inside* the dropdown (like tabs or search) without it closing prematurely.
        3.  **Explicit Bootstrap Dropdown Initialization**: At the end of the `createIconSelector` function, added logic to explicitly initialize a Bootstrap Dropdown instance for the newly created `dropdownButton` (`new bootstrap.Dropdown(dropdownButton)`), but only if an instance didn't already exist (checked via `!bootstrap.Dropdown.getInstance(dropdownButton)`). This ensures Bootstrap is aware of and manages dynamically added dropdown toggles.
    *   **In [`public/assets/js/admin-product-info-fields.js`](public/assets/js/admin-product-info-fields.js):**
        1.  **Deferred Icon Selector Creation**: The call to `window.AdminIconSelector.createIconSelector(newIconInput)` (which triggers the creation and initialization of the icon selector DOM and its Bootstrap component) was wrapped in a `setTimeout(..., 0)`. This slight deferral can help ensure the DOM is fully updated and other scripts have settled before the icon selector and its Bootstrap features are initialized.
*   **Key Learnings & Prevention**:
    *   When integrating custom JavaScript with UI libraries like Bootstrap, be mindful of event propagation (`event.stopPropagation()`, `event.preventDefault()`). How these are used in custom handlers versus the library's own internal handlers can lead to conflicts.
    *   Dynamically added components that rely on JavaScript libraries (like Bootstrap dropdowns, tooltips, modals) often require explicit initialization via the library's JavaScript API after they are added to the DOM. Relying solely on data attributes might not always work for dynamic content.
    *   Checking for existing instances before re-initializing library components (e.g., `bootstrap.Dropdown.getInstance()`) is important to prevent errors or unexpected behavior from double initializations.
    *   Using a `setTimeout(..., 0)` can be a useful (though sometimes last-resort) technique to address timing issues when initializing complex components or interacting with third-party libraries after DOM manipulations.
    *   Thorough testing on all relevant pages (e.g., 'new' vs. 'edit' forms) is crucial, as dynamic content behavior can vary.
*   **Status**: Fixed.

---
# Memory Bank File: priorSolvedIssuesAndProblems.md

This file documents issues that have been resolved, providing a quick reference for understanding how past problems were addressed.

---

**21. UI: Product Form Icon Selector Unreliable (Informações Adicionais)**
*   **Issue**: The icon selector in "Informações Adicionais do Produto" on the product edit page ([`admin.php?section=products&action=edit`](admin.php?section=products&action=edit)) was not working correctly. It failed to initialize for newly added fields, and even when it appeared, it would become unresponsive or close prematurely when interacting with internal elements like category tabs or the search bar.
*   **Root Causes**:
    1.  **Initialization for Dynamic Fields**: The original mechanism in [`public/assets/js/admin-icon-selector.js`](public/assets/js/admin-icon-selector.js) used an unreliable `setTimeout` to initialize selectors on new fields added by [`public/assets/js/admin-product-info-fields.js`](public/assets/js/admin-product-info-fields.js).
    2.  **Event Propagation & State Conflicts**: Clicks on internal elements of the dropdown (tabs, search) were likely bubbling up and causing the Bootstrap dropdown to close. There were also potential conflicts in managing the dropdown's open/closed state between Bootstrap's default behavior, custom `keep-open` logic in [`public/assets/js/bootstrap-dropdown-fix.js`](public/assets/js/bootstrap-dropdown-fix.js), and the specific event handling within [`public/assets/js/admin-icon-selector.js`](public/assets/js/admin-icon-selector.js).
*   **Solution (Multi-step)**:
    1.  **Explicit Initialization**: Modified [`public/assets/js/admin-product-info-fields.js`](public/assets/js/admin-product-info-fields.js) to directly call `window.AdminIconSelector.createIconSelector(newIconInput)` immediately after a new product information field (and its icon input) is appended to the DOM.
    2.  **Removed Redundant `setTimeout`**: Deleted the unreliable `setTimeout`-based initialization for new fields from [`public/assets/js/admin-icon-selector.js`](public/assets/js/admin-icon-selector.js:292-305).
    3.  **Prevent Re-initialization**: Added a check at the start of `createIconSelector` in [`public/assets/js/admin-icon-selector.js`](public/assets/js/admin-icon-selector.js:43) to prevent re-initializing an already processed input field.
    4.  **Forceful Dropdown Close**: Enhanced the icon click handler in [`public/assets/js/admin-icon-selector.js`](public/assets/js/admin-icon-selector.js:177) to more robustly close the dropdown and reset its state (e.g., removing `.show` class, setting `aria-expanded="false"`).
    5.  **Bootstrap Auto-Close Behavior**: Added `data-bs-auto-close="outside"` to the dropdown toggle button in [`public/assets/js/admin-icon-selector.js`](public/assets/js/admin-icon-selector.js:66). This tells Bootstrap to only close the dropdown when a click occurs *outside* of it.
    6.  **Improved `hidden.bs.dropdown` Listener**: Corrected the event listener in [`public/assets/js/bootstrap-dropdown-fix.js`](public/assets/js/bootstrap-dropdown-fix.js:55) to accurately find the `dropdownMenu` associated with the toggle event and remove the `keep-open` class.
    7.  **Controlled Internal Clicks**: Added a click event listener directly to the `dropdownMenu` within `createIconSelector` in [`public/assets/js/admin-icon-selector.js`](public/assets/js/admin-icon-selector.js:264). This listener calls `event.stopPropagation()` for clicks on internal elements (like tabs, search bar, empty areas) to prevent them from closing the dropdown, while allowing clicks on actual icon buttons (`.icon-btn`) to propagate to their handlers.
*   **Prevention**:
    *   For dynamically added UI components requiring JavaScript enhancements, the script adding the element should explicitly call a dedicated initialization function for that component immediately after DOM insertion.
    *   Avoid `setTimeout` for DOM readiness checks when direct control over element addition is possible.
    *   Carefully manage event propagation (`event.stopPropagation()`, `event.preventDefault()`) within complex interactive components like custom dropdowns, especially when integrating with UI libraries (e.g., Bootstrap).
    *   Utilize library-specific attributes (like `data-bs-auto-close`) to control default behaviors where appropriate.
    *   Ensure robust state management (e.g., `aria-expanded`, `.show` class) and cleanup for components.
*   **Status**: Fixed.

---

**1. Inaccurate Cookieless Visit Tracking**
*   **Problem:** The `is_first_visit` flag in the `order_visits` table was not being updated correctly, leading to incorrect display of notices intended for revisits.
*   **Solution:** The system logic for updating the `is_first_visit` flag was corrected. Now, the blue notice (indicating a revisit) is shown only when `is_first_visit = 0`.
*   **Status:** Fixed.

---

**2. Non-Specific Token Generation for Orders**
*   **Problem:** The "Gerar novo token" button in the admin order detail view ([`admin.php?section=orders&action=detail`](admin.php?section=orders&action=detail)) might have been generating tokens that were not strictly tied to the specific order being viewed.
*   **Solution:** The button's functionality was refined to ensure it now correctly generates a token exclusively for the specific order currently being detailed.
*   **Status:** Fixed.

---

**3. Encrypted Customer Data in Download Statistics**
*   **Problem:** The download statistics page was displaying customer data in its encrypted form, making it unreadable for administrators.
*   **Root Cause:** The download_statistics.php template was directly displaying the encrypted data from the database without using the decryption functions that exist in the system.
*   **Solution:**
    * Modified SQL queries to include the `is_encrypted` flag from the licenses table
    * Added code to decrypt customer names and emails using the existing `get_decrypted_license_name()` and `get_decrypted_license_email()` functions
    * Applied decryption to all places where customer data is displayed (Top 5 Customers section, customer email dropdown, and detailed download history)
    * Updated the email filtering logic to use exact matching instead of LIKE for encrypted data
*   **Prevention:** Implement a consistent data processing layer that automatically handles decryption of sensitive data before display in the UI. Standardize data access patterns for encrypted data across the application.
*   **Status:** Fixed.

---

**4. Email Templates Not Saving Correctly**
*   **Problem:** Email templates configured in settings were not being saved, likely due to missing validation.
*   **Solution:** Validation rules for email templates were added in [`settings_handler.php`](includes/settings_handler.php).
*   **Status:** Fixed.

---

**5. Database Bloat from Old `order_visits` Records**
*   **Problem:** The `order_visits` table was accumulating old records without a cleanup mechanism.
*   **Solution:** A maintenance action `cleanup_old_order_visits` was implemented with a handler in [`admin.php`](admin.php) and UI in [`templates/backend/maintenance_simple.php`](templates/backend/maintenance_simple.php).
*   **Status:** Fully Implemented.

---

**6. Stock Management: Non-Digital Products Stock Not Reducing/Restoring**
*   **Issue**: Non-digital products were not having their stock reduced when orders were placed, and no mechanism to restore stock for canceled/refunded orders.
*   **Problem**: Inconsistent stock reduction during checkout; no stock restoration process.
*   **Solution**:
    *   Added `stock_reduced` and `stock_restored` columns to `order_items` table.
    *   Created [`stock_functions.php`](includes/stock_functions.php) with comprehensive stock management functions.
    *   Implemented `update_variation_stock()` to reduce stock on order placement.
    *   Created `restore_order_item_stock()` and `restore_order_stock()` functions.
    *   Automated stock restoration for canceled/refunded orders.
    *   Added "Restore Stock" button on order detail page for manual restoration.
    *   Implemented tracking to prevent double stock restoration.
*   **Prevention**: Implement robust tracking mechanisms (e.g., database flags) for critical operations like stock changes to track state and prevent duplicate actions. Ensure comprehensive transaction management.

---

**7. Digital Products: Email Verification for License Downloads Failing**
*   **Issue**: Email verification for license downloads failed even with correct email.
*   **Problem**: System compared encrypted email from DB with non-encrypted user input.
*   **Solution**:
    *   Updated `verify_license_email` function to decrypt the stored email before comparison.
    *   Ensured proper inclusion of [`license_encryption_functions.php`](includes/license_encryption_functions.php).
    *   Updated templates and email functions to use/display decrypted customer data where appropriate.
    *   Ensured consistent decryption across all license-related functionality.
*   **Prevention**: When implementing encryption, ensure all comparison operations decrypt stored data before comparing with plaintext input. Consistently handle encryption/decryption in all related data processing and display functions.

---

**8. Digital Products: License Status vs. Download Validity Confusion**
*   **Issue**: Expired download period incorrectly showed license status as "Expirada".
*   **Problem**: Download period expiration was conflated with license expiration, causing user confusion.
*   **Solution**:
    *   Clearly separated "License Status" from "Download Validity" in the UI (e.g., license detail page).
    *   Added a new "Validade do Download" field.
    *   Updated error messages and `check_license_validity` function to clarify that the *download period* expired, not necessarily the license itself.
*   **Prevention**: Maintain clear conceptual and UI separation for distinct status types that can exist independently. Use precise terminology in messages and UI labels.

---

**9. Database Maintenance: Backup Functionality Failing (Undefined Constant)**
*   **Issue**: Database backup failed with an undefined constant error.
*   **Problem**: `backup_database()` in [`maintenance_functions.php`](includes/maintenance_functions.php) used undefined `DATABASE_FILE`.
*   **Solution**: Updated function to use the correct constant `DB_PATH` (defined in [`config.php`](config.php)).
*   **Prevention**: Always verify constant definitions and ensure necessary configuration files are included before using constants. Use consistent naming for critical path variables.

---

**10. UI: HTML in Flash Messages Displayed as Raw Text**
*   **Issue**: HTML tags in flash messages were not rendered.
*   **Problem**: `display_flash_messages()` function was over-sanitizing message content with `sanitize_input()`, converting HTML to entities.
*   **Solution**: Modified the function to not sanitize the message content intended for HTML display, allowing HTML to be rendered. Ensure sanitization occurs *before* storing if the source is untrusted, but not on display if HTML is intended.
*   **Prevention**: Be selective with sanitization. Understand when HTML is intended output versus when all input must be treated as plain text. Sanitize input upon receipt, not necessarily upon display if HTML is desired.

---

**11. UI: Maintenance Section Modals Stuck/Unreliable**
*   **Issue**: Maintenance section buttons reacted, but modals got stuck or operations failed.
*   **Problem**: Complex AJAX/modal implementation led to reliability issues with initialization and request handling. User preference is against complex modals for such actions.
*   **Solution**: Replaced AJAX/modal implementation with simpler direct form submissions and flash messages for feedback. Page reloads after action.
*   **Prevention**: Prefer simpler, more reliable implementations (like direct forms and page reloads for admin CRUD) over complex client-side solutions, especially when user preference aligns. Test thoroughly across different states.

---

**12. UI: Maintenance Operations Lacked Confirmation Dialogs**
*   **Issue**: Permanent database changes could be triggered by a single click in Maintenance.
*   **Problem**: Risk of accidental data modification/loss. User preference is for confirmations.
*   **Solution**: Added JavaScript `confirm()` dialogs with specific messages for each destructive operation type before form submission.
*   **Prevention**: Always implement confirmation dialogs before actions that cause permanent database modifications or other irreversible changes, adhering to user preferences.

---

**13. UI: Dashboard Charts & Interactive Elements Not Working with AJAX Navigation**
*   **Issue**: Dashboard charts and buttons (collapsible widgets, refresh) failed after navigating to dashboard via AJAX sidebar links.
*   **Problem**: Event handlers were not being (re)attached to DOM elements loaded via AJAX.
*   **Solution**:
    *   Created a global `initDashboardButtons` JavaScript function to initialize/re-initialize event handlers.
    *   Called this function on `DOMContentLoaded` and after AJAX content loads.
    *   Used direct `onclick` attributes for some simple handlers to improve compatibility.
    *   Implemented a `MutationObserver` to watch for DOM changes (e.g., widget loading) and trigger re-initialization.
    *   Used direct style manipulation for toggling widget visibility.
*   **Prevention**: For AJAX-loaded dynamic content:
    *   Use event delegation for handlers where possible.
    *   If direct binding is used, re-bind events after new content is loaded.
    *   Global initialization functions that can be called on demand are useful.
    *   Consider `MutationObserver` for complex dynamic UIs.
    *   Test AJAX navigation paths thoroughly.

---

**14. UI: Dashboard Layout and UI Usability/Aesthetics**
*   **Issue**: Basic dashboard layout, poor contrast, non-preferred two-column layout.
*   **Problem**: Sub-optimal admin user experience.
*   **Solution**:
    *   Implemented a single-column layout for dashboard widgets.
    *   Used a darker color scheme for widgets with better text contrast (light text on dark backgrounds).
    *   Added more statistics and data visualizations.
    *   Improved quick actions section.
    *   Added system status information.
    *   Implemented collapsible widgets.
*   **Prevention**: Adhere to user UI preferences (e.g., single column for quick actions, dark backgrounds for widgets, high contrast text). Regularly solicit feedback on UI/UX.

---

**15. License Deletion AJAX Error / CSRF Error**
*   **Issue**: Deleting licenses in [`admin.php?section=licenses`](admin.php?section=licenses) resulted in a CSRF error or 500 Internal Server Error for AJAX requests.
*   **Root Cause**: AJAX GET request handler in [`admin.php`](admin.php) called `get_csrf_token()` which was not accessible in that scope.
*   **Solution**: Modified AJAX GET handler to call `generate_csrf_token();` first, then assign `$admin_view_data['csrf_token'] = $_SESSION['csrf_token'] ?? '';`.
*   **Prevention**: Ensure view data arrays consistently provide necessary variables (like CSRF tokens) for AJAX-loaded templates. Check variable scope when functions are undefined.

---

**16. "Alterar Arquivo" Redirect to Dashboard**
*   **Issue**: Clicking "Alterar Arquivo" in digital product editing redirected to dashboard instead of the change file page.
*   **Root Cause**: The standard (non-AJAX) GET request handler in [`admin.php`](admin.php) lacked a `case 'digital_files':` for `action=change_file`.
*   **Solution**: Added the missing `case 'digital_files':` to the standard GET request handler in [`admin.php`](admin.php) to correctly include [`templates/backend/change_digital_file.php`](templates/backend/change_digital_file.php).
*   **Prevention**: Ensure all admin sections and actions accessible via direct links have corresponding handlers in standard GET routing. Test all navigation paths.

---

**17. Digital Products List and File Selection Issues**
*   **Issue**: Digital products list ([`admin.php?section=digital_products&action=list`](admin.php?section=digital_products&action=list)) was not displaying products; "Selecionar Existente" tab in digital product form wasn't populating fields.
*   **Root Causes**: Database schema inconsistency (`display_name` in `digital_files`); routing issues; JavaScript event handling; form submission handling.
*   **Solution**: Fixed `get_all_digital_products()` with dynamic SQL; corrected routing in [`admin.php`](admin.php) for the list; improved JS for tab handling and form population; ensured proper data handling in `admin.php` for file selection.
*   **Prevention**: Check for column existence or use dynamic SQL fallbacks. Use explicit routing. Test JS across scenarios. Add comprehensive logging.

---

**18. Digital File Upload and Associations Not Saving (Updated 2025-05-13)**
*   **Issue 1**: "Erro ao criar registro do arquivo digital" (Error creating digital file record) on new uploads.
*   **Root Cause 1**: `digital_files` table missing `display_name` column.
*   **Solution 1**: Added `display_name` column via migration ([`add_display_name_to_digital_files.php`](includes/db_migrations/add_display_name_to_digital_files.php)) integrated into [`db.php`](includes/db.php). Enhanced logging in [`digital_files_functions.php`](includes/digital_files_functions.php) and [`db.php`](includes/db.php).
*   **Issue 2**: "Erro ao guardar o produto digital: Falha ao remover associações de tipos de ficheiro existentes." (Error saving digital product: Failed to remove existing file type associations) when editing.
*   **Root Cause 2**: SQLite referencing a non-existent `digital_products_old` table due to an incomplete prior migration.
*   **Solution 2**: Created migration ([`recreate_digital_product_file_type_associations.php`](includes/db_migrations/recreate_digital_product_file_type_associations.php)) to drop and correctly recreate `digital_product_file_type_associations` table, fixing foreign key issues.
*   **Issue 3**: Saved file type associations not persisting after editing.
*   **Root Cause 3**: The `recreate_digital_product_file_type_associations_table` migration was running on every page load.
*   **Solution 3**: Commented out the unconditional call to `recreate_digital_product_file_type_associations_table($pdo);` in [`db.php`](includes/db.php) after its initial successful run. Added logging to `update_digital_product` to trace association inserts.
*   **Issue 4 (HTML error in "Ações" column on digital_products_list.php)**: PHP warnings ("Undefined array key 'product_id'") outputting directly into HTML.
*   **Root Cause 4**: `product_id` key was not explicitly available in the `$product` array fetched by `get_all_digital_products()`.
*   **Solution 4**: Modified the SQL query in `get_all_digital_products()` (in [`includes/digital_files_functions.php`](includes/digital_files_functions.php)) to explicitly select `p.id as product_id` instead of relying on `p.*`.
*   **Prevention**:
    *   Database migrations for structural changes should be idempotent or run once.
    *   Use detailed logging for database errors.
    *   Ensure foreign keys are correctly re-established after table recreations.
    *   Explicitly select and alias columns in SQL queries when specific keys are expected in the resulting arrays, rather than relying on `SELECT *`.

---

**19. Admin Page for "Produtos Digitais" Not Loading Correctly via AJAX Sidebar (Updated 2025-05-13)**
*   **Issue**: Clicking "Produtos Digitais" ([`admin.php?section=digital_products&action=list`](admin.php?section=digital_products&action=list)) in the sidebar showed the dashboard content until a manual page refresh.
*   **Root Cause**: The AJAX GET request handler in [`admin.php`](admin.php) was incorrectly falling through to the `default` case (which loads the dashboard) *before* or in addition to processing the `case 'digital_products'`. This was identified by observing the order of log messages: the "Reached DEFAULT case" log appeared before the "Including digital_products_list.php" log, and the JavaScript received dashboard HTML in the AJAX response.
*   **Solution**: Implemented an `if` condition specifically for `section === 'digital_products' && action === 'list'` *before* the main AJAX `switch` statement in `admin.php`. This isolated block handles the data preparation, template inclusion, output buffering, and JSON response with an `exit;`, bypassing the problematic `switch` behavior for this particular section.
*   **Prevention**:
    *   When AJAX navigation shows content from a different section or requires a refresh, check if the server-side AJAX handler is correctly identifying the section and action.
    *   Log the `$section` and `$action` variables just before the `switch` statement in the AJAX handler.
    *   Log entry into the `default` case of the AJAX `switch` to detect fall-throughs.
    *   Inspect the JavaScript AJAX success callback to see the `data.section`, `data.action`, and `data.content` received from the server. If `data.content` is incorrect, the server-side AJAX handler for that section is likely at fault.
    *   Ensure `break;` statements are present and correctly functioning in all `case` blocks of server-side `switch` statements handling AJAX routing. If fall-through is suspected despite `break;`, consider isolating problematic cases with `if/else if` structures.

---

**20. UI: Product Price Prefix "Desde: " Logic Incorrect**
*   **Issue**: The "Desde: " (From: ) prefix for product prices was always shown if variations or custom fields existed, even after all options were selected and the final price was determined. It was also missing from product listing cards.
*   **Problem**: The logic didn't differentiate between a product *having* options and the user having *selected* all required options to see the final price. The prefix should only indicate a starting price when choices remain.
*   **Solution**:
    *   **Product Detail Page ([`templates/frontend/product_detail.php`](templates/frontend/product_detail.php)):**
        *   Modified the initial PHP price display to show "Desde: " + base price only if options (variations or custom fields) exist.
        *   Updated the `updateVariationDetails` JavaScript function:
            *   Added a helper function `areAllOptionsSelected()` to check if all required variation selects and custom field inputs (textareas, file uploads, font selects) have valid values.
            *   Modified the price calculation logic:
                *   If `areAllOptionsSelected()` is true, calculate the final price (base + variation mods + custom field mods) and display it *without* the "Desde: " prefix.
                *   If `areAllOptionsSelected()` is false but options exist, display "Desde: " + base price.
            *   Added event listeners to custom field inputs (`change`/`input`) to trigger `updateVariationDetails`, ensuring the price updates correctly when custom fields are modified.
    *   **Product Card Partial ([`templates/frontend/partials/product_card.php`](templates/frontend/partials/product_card.php)):**
        *   Modified the price display to check if `$has_variations` or `$has_custom_fields` is true and prepend "Desde: " to the formatted base price accordingly. This correctly indicates a starting price on list views where options cannot be selected.
*   **Prevention**: When implementing dynamic price displays based on user selections, ensure JavaScript logic accurately reflects the state of *all* required inputs (variations, custom fields, etc.) before determining whether to show a final price or a starting price indicator. Add listeners to all relevant inputs that affect the final price.
*   **Status**: Fixed.