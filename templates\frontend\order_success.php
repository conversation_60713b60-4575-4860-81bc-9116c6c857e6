<?php

require_once __DIR__ . '/../../includes/order_functions.php';
require_once __DIR__ . '/../../includes/order_statuses.php';

if (!isset($_GET['token'])) {
    echo '<div class="bg-red-800 border border-red-700 text-red-100 px-6 py-4 rounded-lg mb-6">
            <div class="flex items-center">
                <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
                <span class="text-lg font-medium">Erro</span>
            </div>
            <p class="mt-2">Não foi possível encontrar os detalhes da encomenda.</p>
        </div>';
    return;
}

$access_token = $_GET['token'];

$provided_order_id = isset($_GET['order_id']) ? (int)$_GET['order_id'] : null;

$order_id = validate_order_access_token($access_token, $provided_order_id);

if (!$order_id) {
    echo '<div class="bg-red-800 border border-red-700 text-red-100 px-6 py-4 rounded-lg mb-6">
            <div class="flex items-center">
                <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
                <span class="text-lg font-medium">Erro</span>
            </div>
            <p class="mt-2">Link inválido ou expirado.</p>
        </div>';
    return;
}

$order = db_query("SELECT * FROM orders WHERE id = :id", [':id' => $order_id], true);

if (!$order) {
    echo '<div class="bg-red-800 border border-red-700 text-red-100 px-6 py-4 rounded-lg mb-6">
            <div class="flex items-center">
                <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
                <span class="text-lg font-medium">Erro</span>
            </div>
            <p class="mt-2">Encomenda não encontrada.</p>
        </div>';
    return;
}

if (isset($_GET['order_id'])) {
    
    
    $new_token = create_order_access_token($order_id);
    if ($new_token && $new_token !== $access_token) {
        $redirect_url = add_session_param_to_url(BASE_URL . '/index.php?view=order_success&token=' . $new_token);
        echo '<script>window.location.href = "' . $redirect_url . '";</script>';
        return;
    }
}

$customer_info = json_decode($order['customer_info_json'], true);

$order_items = get_order_items($order_id);

$has_digital_products = false;
if (isset($order['has_digital_products']) && $order['has_digital_products']) {
    $has_digital_products = true;

    
    require_once __DIR__ . '/../../includes/digital_product_functions.php';

    
    $licenses = db_query("SELECT * FROM licenses WHERE order_id = :order_id", [':order_id' => $order_id], false, true);
}

require_once __DIR__ . '/../../includes/payment_methods.php';
$payment_method_id = $order['payment_method'];
$payment_instructions = null;

if (is_numeric($payment_method_id)) {
    $payment_method = get_payment_method($payment_method_id);
    if ($payment_method) {
        $payment_instructions = $payment_method['instructions'];
    }
} else {
    
    
    if ($payment_method_id === 'bank_transfer') {
        $payment_method = db_query("SELECT * FROM payment_methods WHERE title LIKE '%Transferência%' AND is_active = 1 LIMIT 1", [], true);
        if ($payment_method) {
            $payment_instructions = $payment_method['instructions'];
        }
    }
}

$currency_symbol = get_setting('currency_symbol', '€');

$_SESSION['cart'] = [];
unset($_SESSION['cart_discount']);
unset($_SESSION['applied_promo_code']);
unset($_SESSION['applied_coupon_id']);

session_write_close();
session_start();
?>

<!-- Add JavaScript to ensure cart is completely cleared -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Clear cart in UI
    if (typeof updateCartCounter === 'function') {
        updateCartCounter(0);
    }
    if (typeof updateCartBadge === 'function') {
        updateCartBadge(0);
    }
    // Store cart count in localStorage as a backup
    localStorage.setItem('eshop_cart_count', '0');

    // Use the same AJAX call that the "Limpar Carrinho" button uses
    // This ensures the cart is completely cleared in the database
    function clearCart() {
        return new Promise((resolve, reject) => {
            fetch('<?= add_session_param_to_url(BASE_URL . "/index.php?action=clear_cart") ?>', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json',
                    'X-Session-UUID': window.eshopSessionId || ''
                },
                body: JSON.stringify({})
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                resolve(data);
            })
            .catch(error => {
                console.error('Error clearing cart:', error);
                reject(error);
            });
        });
    }

    // Try clearing the cart multiple times to ensure it's properly cleared
    // This is a workaround for potential race conditions with session handling
    clearCart()
        .then(() => {
            // Wait a moment and clear again to be extra sure
            setTimeout(() => {
                clearCart()
                    .then(() => {})
                    .catch(error => {});
            }, 1000);
        })
        .catch(error => {
            // Try again after a delay
            setTimeout(clearCart, 2000);
        });
});
</script>

<?php

require_once __DIR__ . '/../../includes/order_functions.php';

record_order_visit($order_id, $access_token);

$is_revisit = is_order_revisit($order_id, $order);

$verification_submitted = false;
$verification_success = false;
$verification_error = '';
$email_submitted = false;
$email_sent = false;
$email_error = '';
$show_uncensored_data = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'verify_code' && isset($_POST['verification_code']) && isset($_POST['order_id'])) {
        $verification_submitted = true;
        $verification_code = sanitize_input($_POST['verification_code']);
        $verification_order_id = (int)$_POST['order_id'];

        
        if ($verification_order_id === $order_id && verify_order_verification_code($order_id, $verification_code)) {
            $verification_success = true;
            $show_uncensored_data = true;
            
            $_SESSION['verified_order_' . $order_id] = true;
        } else {
            $verification_error = 'Código de verificação inválido. Por favor, tente novamente.';
        }
    } elseif ($_POST['action'] === 'send_code' && isset($_POST['email']) && isset($_POST['order_id'])) {
        $email_submitted = true;
        $email = sanitize_input($_POST['email']);
        $email_order_id = (int)$_POST['order_id'];

        
        if ($email_order_id === $order_id && send_order_verification_code($order_id, $email)) {
            $email_sent = true;
        } else {
            $email_error = 'Não foi possível enviar o código de verificação. Verifique se o email está correto.';
        }
    } elseif ($_POST['action'] === 'resend_details' && isset($_POST['email']) && isset($_POST['order_id'])) {
        $email_submitted = true;
        $email = sanitize_input($_POST['email']);
        $email_order_id = (int)$_POST['order_id'];

        
        if ($email_order_id === $order_id && resend_order_details_to_email($order_id, $email)) {
            $email_sent = true;
        } else {
            $email_error = 'Não foi possível reenviar os detalhes da encomenda. Verifique se o email está correto.';
        }
    }
}

if (isset($_SESSION['verified_order_' . $order_id]) && $_SESSION['verified_order_' . $order_id] === true) {
    $show_uncensored_data = true;
}

$display_customer_info = $customer_info;
if ($is_revisit && !$show_uncensored_data) {
    $display_customer_info = get_censored_customer_data($customer_info);
}
?>

<div class="max-w-4xl mx-auto">
    <?php if (!$is_revisit): ?>
    <!-- Initial visit message -->
    <div class="bg-green-800 border border-green-700 text-green-100 px-6 py-4 rounded-lg mb-6">
        <div class="flex items-center">
            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span class="text-lg font-medium">Encomenda Recebida!</span>
        </div>
        <p class="mt-2">Obrigado pela sua encomenda. O seu pedido foi recebido e está a ser processado.</p>
    </div>
    <?php else: ?>
    <!-- Revisit message -->
    <div class="bg-blue-800 border border-blue-700 text-blue-100 px-6 py-4 rounded-lg mb-6">
        <div class="flex items-center">
            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="text-lg font-medium">Detalhes da Encomenda</span>
        </div>
        <p class="mt-2">Olá de novo! Estes são os dados da sua encomenda. Sempre que a mesma mudar de estado (enviada, cancelada, reembolsada, etc) isso será refletido aqui! Poderá consultar esta página sempre que necessitar de obter mais detalhes sobre a sua encomenda.</p>
        <p class="mt-2">Note que os seus dados pessoais estão parcialmente ocultos por motivos de segurança. Para visualizar os dados completos, por favor verifique a sua identidade abaixo.</p>
    </div>
    <?php endif; ?>

    <div class="bg-gray-900 rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4 border-b border-gray-700 pb-2">Detalhes da Encomenda</h2>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <p><strong>Número da Encomenda:</strong> <?= sanitize_input($order['order_ref']) ?></p>
                <p><strong>Data:</strong> <?= date('d/m/Y H:i', strtotime($order['created_at'])) ?></p>
                <p><strong>Estado:</strong> <?= sanitize_input(get_status_name_pt($order['status'])) ?></p>
                <p><strong>Total:</strong> <?= format_price($order['total_amount'], $currency_symbol) ?></p>
                <p><strong>Método de Pagamento:</strong> <?= sanitize_input($payment_method['title'] ?? $order['payment_method']) ?></p>
                <?php if (!empty($order['tracking_number'])): ?>
                <p><strong>Número de Tracking:</strong> <?= sanitize_input($order['tracking_number']) ?></p>
                <?php if (!empty($order['tracking_url'])): ?>
                <p><a href="<?= sanitize_input($order['tracking_url']) ?>" target="_blank" class="text-blue-400 hover:text-blue-300">Acompanhar Encomenda <i class="ri-external-link-line"></i></a></p>
                <?php endif; ?>
                <?php endif; ?>
                <p><strong>Notas para este pedido:<br></strong><?= nl2br(sanitize_input($customer_info['order_notes'])) ?></p>
            </div>

            <div>
                <p><strong>Nome:</strong> <?= sanitize_input($display_customer_info['customer_name'] ?? '') ?></p>
                <p><strong>Email:</strong> <?= sanitize_input($display_customer_info['customer_email'] ?? '') ?></p>
                <p><strong>Telefone:</strong> <?= sanitize_input($display_customer_info['customer_phone'] ?? '') ?></p>
                <p><strong>Morada:</strong> <?= sanitize_input($display_customer_info['shipping_address'] ?? '') ?></p>
                <p><?= sanitize_input($display_customer_info['shipping_zip'] ?? '') ?> <?= sanitize_input($display_customer_info['shipping_city'] ?? '') ?>, <?= sanitize_input($display_customer_info['shipping_country'] ?? '') ?></p>

                <?php if ($is_revisit && !$show_uncensored_data): ?>
                <div class="mt-4 pt-4 border-t border-gray-700">
                    <p class="text-yellow-400 text-sm mb-2"><i class="ri-information-line mr-1"></i> Os dados pessoais estão parcialmente ocultos por motivos de segurança.</p>
                    <a href="#verify-identity" class="text-blue-400 hover:text-blue-300 text-sm">
                        <i class="ri-lock-unlock-line mr-1"></i> Verificar identidade para ver dados completos
                    </a>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php if (!empty($order_items)): ?>
    <div class="bg-gray-900 rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4 border-b border-gray-700 pb-2">Itens da Encomenda</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full bg-gray-800 rounded-lg overflow-hidden">
                <thead class="bg-gray-700">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Produto</th>
                        <th class="px-4 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">Quantidade</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Preço Unit.</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">Total</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-700">
                    <?php foreach ($order_items as $item):
                        $item_details = json_decode($item['product_details_json'], true);
                        $item_name = $item_details['name'] ?? 'Produto';
                        $item_attributes = $item_details['attributes_display'] ?? '';
                        $item_custom_fields = $item_details['custom_fields'] ?? [];
                    ?>
                    <tr>
                        <td class="px-4 py-3">
                            <?= sanitize_input($item_name) ?>
                            <?php if (!empty($item_attributes)): ?>
                                <br><small class="text-gray-400"><?= sanitize_input($item_attributes) ?></small>
                            <?php endif; ?>
                            <?php if (!empty($item_custom_fields)):
                                require_once __DIR__ . '/../../includes/custom_field_functions.php';
                                foreach ($item_custom_fields as $custom_field): ?>
                                    <br><small class="text-gray-400"><?= sanitize_input(get_cart_custom_field_display($custom_field)) ?></small>
                                <?php endforeach;
                            endif; ?>
                        </td>
                        <td class="px-4 py-3 text-center"><?= (int)$item['quantity'] ?></td>
                        <td class="px-4 py-3 text-right"><?= format_price($item['price_at_purchase'], $currency_symbol) ?></td>
                        <td class="px-4 py-3 text-right"><?= format_price($item['price_at_purchase'] * $item['quantity'], $currency_symbol) ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot class="bg-gray-700">
                    <tr>
                        <td colspan="3" class="px-4 py-3 text-right font-medium">Subtotal:</td>
                        <td class="px-4 py-3 text-right"><?= format_price($order['total_amount'] - ($order['shipping_cost'] ?? 0) - ($order['tax_amount'] ?? 0) + ($order['discount_amount'] ?? 0), $currency_symbol) ?></td>
                    </tr>
                    <?php if (!empty($order['discount_amount']) && $order['discount_amount'] > 0): ?>
                    <tr>
                        <td colspan="3" class="px-4 py-3 text-right font-medium">Desconto:
                            <?php if (!empty($order['coupon_code'])): ?>
                                <span class="inline-block px-2 py-1 text-xs rounded bg-blue-900 text-blue-100"><?= sanitize_input($order['coupon_code']) ?></span>
                            <?php endif; ?>
                        </td>
                        <td class="px-4 py-3 text-right text-red-400">-<?= format_price($order['discount_amount'] ?? 0, $currency_symbol) ?></td>
                    </tr>
                    <?php endif; ?>
                    <tr>
                        <td colspan="3" class="px-4 py-3 text-right font-medium">Envio:</td>
                        <td class="px-4 py-3 text-right"><?= format_price($order['shipping_cost'] ?? 0, $currency_symbol) ?></td>
                    </tr>
                    <?php
                    
                    $tax_details = json_decode($order['tax_details_json'] ?? '{}', true);
                    if (!empty($tax_details) || ($order['tax_amount'] ?? 0) > 0):
                    ?>
                    <tr>
                        <td colspan="3" class="px-4 py-3 text-right font-medium">IVA:</td>
                        <td class="px-4 py-3 text-right"><?= format_price($order['tax_amount'] ?? 0, $currency_symbol) ?></td>
                    </tr>
                    <?php endif; ?>
                    <tr>
                        <td colspan="3" class="px-4 py-3 text-right font-medium text-lg">Total:</td>
                        <td class="px-4 py-3 text-right font-bold text-lg"><?= format_price($order['total_amount'], $currency_symbol) ?></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <?php endif; ?>

    <?php if ($payment_instructions): ?>
    <div class="bg-gray-900 rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4 border-b border-gray-700 pb-2">Instruções de Pagamento</h2>
        <div class="bg-gray-800 p-4 rounded-lg">
            <pre class="whitespace-pre-wrap text-gray-300"><?= sanitize_input($payment_instructions) ?></pre>
        </div>
        <p class="mt-4 text-gray-400">Estas instruções também foram enviadas para o seu email.</p>
    </div>
    <?php endif; ?>

    <?php if ($has_digital_products): ?>
    <div class="bg-gray-900 rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4 border-b border-gray-700 pb-2">Produtos Digitais</h2>

        <div class="bg-blue-900/30 border border-blue-800 rounded-lg p-4 mb-4">
            <div class="flex items-start">
                <div class="flex-shrink-0 mt-0.5">
                    <i class="ri-information-line text-blue-400 text-lg"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-400">Informação sobre os seus Produtos Digitais</h3>
                    <div class="mt-2 text-sm text-blue-300">
                        <p>A sua encomenda contém produtos digitais. Após a confirmação do pagamento, receberá um email com as instruções de download e os códigos de licença para aceder aos seus produtos.</p>

                        <?php if ($order['status'] === 'paid' || $order['status'] === 'completed'): ?>
                            <p class="mt-2">O seu pagamento já foi confirmado. Verifique o seu email para as instruções de download ou aceda à página de downloads através do botão abaixo.</p>
                        <?php else: ?>
                            <p class="mt-2">Assim que o seu pagamento for confirmado, os seus produtos digitais ficarão disponíveis para download.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!empty($licenses)): ?>
            <div class="mb-6">
                <h3 class="text-lg font-medium mb-3">Suas Licenças (guarde este código)</h3>

                <div class="overflow-x-auto">
                    <table class="min-w-full bg-gray-800 rounded-lg overflow-hidden">
                        <thead class="bg-gray-700">
                            <tr>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Código de Licença</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Expiração</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Downloads</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Ações</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-700">
                            <?php foreach ($licenses as $license): ?>
                                <?php
                                
                                $status_class = 'bg-green-900 text-green-100';
                                $status_text = 'Ativo';

                                if ($license['status'] === 'waiting_payment') {
                                    $status_class = 'bg-yellow-900 text-yellow-100';
                                    $status_text = 'Aguardando Pagamento';
                                } elseif ($license['status'] === 'revoked') {
                                    $status_class = 'bg-red-900 text-red-100';
                                    $status_text = 'Revogada';
                                } elseif (strtotime($license['expiry_date']) < time()) {
                                    $status_class = 'bg-red-900 text-red-100';
                                    $status_text = 'Expirada';
                                } elseif ($license['downloads_used'] >= $license['download_limit']) {
                                    $status_class = 'bg-orange-900 text-orange-100';
                                    $status_text = 'Limite Atingido';
                                }

                                
                                $days_left = 0;
                                if (strtotime($license['expiry_date']) > time()) {
                                    $days_left = ceil((strtotime($license['expiry_date']) - time()) / (60 * 60 * 24));
                                }

                                
                                $downloads_left = $license['download_limit'] - $license['downloads_used'];
                                ?>
                                <tr>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span class="font-mono text-sm"><?= sanitize_input($license['license_code']) ?></span>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <span class="inline-block px-2 py-1 text-xs rounded <?= $status_class ?>"><?= $status_text ?></span>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?= date('d/m/Y', strtotime($license['expiry_date'])) ?>
                                        <?php if ($days_left > 0): ?>
                                            <span class="text-xs text-gray-400">(<?= $days_left ?> dias)</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?= $license['downloads_used'] ?> de <?= $license['download_limit'] ?>
                                    </td>
                                    <td class="px-4 py-3 whitespace-nowrap">
                                        <?php if ($license['status'] === 'active' &&
                                                  strtotime($license['expiry_date']) > time() &&
                                                  $license['downloads_used'] < $license['download_limit']): ?>
                                            <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download&code=' . urlencode($license['license_code'])) ?>"
                                               class="inline-block bg-primary hover:bg-primary/90 text-white py-1 px-3 rounded text-sm">
                                                <i class="ri-download-line mr-1"></i> Download
                                            </a>
                                        <?php else: ?>
                                            <button class="inline-block bg-gray-700 text-gray-300 py-1 px-3 rounded text-sm cursor-not-allowed" disabled>
                                                <i class="ri-download-line mr-1"></i> Download
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <?php if ($is_revisit && !$show_uncensored_data): ?>
    <!-- Verification Forms for Revisit -->
    <div id="verify-identity" class="bg-gray-900 rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4 border-b border-gray-700 pb-2">Verificar Identidade</h2>

        <?php if ($verification_submitted && $verification_success): ?>
        <!-- Verification Success Message -->
        <div class="bg-green-900/50 border border-green-800 text-green-100 px-4 py-3 rounded mb-6">
            <div class="flex items-center">
                <div class="w-8 h-8 flex-shrink-0 flex items-center justify-center bg-green-800 rounded-full mr-3">
                    <i class="ri-check-line text-lg"></i>
                </div>
                <div>
                    <p class="font-bold">Identidade Verificada</p>
                    <p class="text-sm">A sua identidade foi verificada com sucesso. Agora pode ver os dados completos da encomenda.</p>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=order_success&token=' . $access_token) ?>" class="inline-block bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-button text-sm">
                <i class="ri-refresh-line mr-1"></i> Atualizar Página
            </a>
        </div>
        <?php else: ?>

        <div class="grid grid-cols-1 gap-6">
            <!-- Verification Code Form -->
            <div class="bg-gray-800 rounded-lg p-4">
                <h3 class="text-lg font-medium mb-3">Verificar com Código</h3>

                <?php if ($verification_submitted && !$verification_success): ?>
                <div class="bg-red-900/50 border border-red-800 text-red-100 px-4 py-3 rounded mb-4">
                    <p><i class="ri-error-warning-line mr-1"></i> <?= $verification_error ?></p>
                </div>
                <?php endif; ?>

                <?php if ($email_submitted && $email_sent): ?>
                <div class="bg-green-900/50 border border-green-800 text-green-100 px-4 py-3 rounded mb-4">
                    <p><i class="ri-mail-check-line mr-1"></i> Um código de verificação foi enviado para o seu email.</p>
                </div>
                <?php endif; ?>

                <form method="post" action="<?= add_session_param_to_url(BASE_URL . '/index.php?view=order_success&token=' . $access_token) ?>#verify-identity">
                    <input type="hidden" name="action" value="verify_code">
                    <input type="hidden" name="order_id" value="<?= $order_id ?>">

                    <div class="mb-4">
                        <label for="verification_code" class="block text-sm font-medium text-gray-300 mb-1">Código de Verificação</label>
                        <input type="text" id="verification_code" name="verification_code" class="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Digite o código de 6 dígitos" required>
                    </div>

                    <div class="flex justify-between items-center">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-button text-sm">
                            Verificar Código
                        </button>

                        <button type="button" id="toggle-send-code" class="text-blue-400 hover:text-blue-300 text-sm">
                            Não tem um código?
                        </button>
                    </div>
                </form>
            </div>

            <!-- Request Code Form -->
            <div id="send-code-form" class="bg-gray-800 rounded-lg p-4" style="display: none;">
                <h3 class="text-lg font-medium mb-3">Solicitar Código de Verificação</h3>

                <?php if ($email_submitted && !$email_sent): ?>
                <div class="bg-red-900/50 border border-red-800 text-red-100 px-4 py-3 rounded mb-4">
                    <p><i class="ri-error-warning-line mr-1"></i> <?= $email_error ?></p>
                </div>
                <?php endif; ?>

                <form method="post" action="<?= add_session_param_to_url(BASE_URL . '/index.php?view=order_success&token=' . $access_token) ?>#verify-identity">
                    <input type="hidden" name="action" value="send_code">
                    <input type="hidden" name="order_id" value="<?= $order_id ?>">

                    <div class="mb-4">
                        <label for="email" class="block text-sm font-medium text-gray-300 mb-1">Email</label>
                        <input type="email" id="email" name="email" class="w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Digite o email usado na encomenda" required>
                    </div>

                    <div class="flex justify-between items-center">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-button text-sm">
                            Enviar Código
                        </button>

                        <button type="button" id="toggle-verify-code" class="text-blue-400 hover:text-blue-300 text-sm">
                            Já tem um código?
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const toggleSendCode = document.getElementById('toggle-send-code');
                const toggleVerifyCode = document.getElementById('toggle-verify-code');
                const sendCodeForm = document.getElementById('send-code-form');

                toggleSendCode.addEventListener('click', function() {
                    sendCodeForm.style.display = 'block';
                    toggleSendCode.parentElement.parentElement.parentElement.style.display = 'none';
                });

                toggleVerifyCode.addEventListener('click', function() {
                    sendCodeForm.style.display = 'none';
                    toggleSendCode.parentElement.parentElement.parentElement.style.display = 'block';
                });

                <?php if ($email_submitted): ?>
                sendCodeForm.style.display = 'block';
                toggleSendCode.parentElement.parentElement.parentElement.style.display = 'none';
                <?php endif; ?>
            });
        </script>
        <?php endif; ?>
    </div>
    <?php endif; ?>

    <div class="text-center mt-8">
        <?php if (!$is_revisit): ?>
        <!-- Initial visit message -->
        <p class="text-gray-400 mb-4">Um email de confirmação foi enviado para <?= sanitize_input($customer_info['customer_email'] ?? '') ?></p>
        <a href="<?= add_session_param_to_url(BASE_URL . '/index.php') ?>" class="inline-block bg-primary hover:bg-primary/90 text-white py-3 px-6 rounded-button font-medium">
            Continuar a Comprar
        </a>
        <?php else: ?>
        <!-- Revisit message -->
        <div class="mb-6">
            <p class="text-gray-400 mb-2">Se desejar receber novamente os dados iniciais da encomenda por email (incluindo os dados de pagamento), por favor confirme o mesmo aqui:</p>

            <form method="post" action="<?= add_session_param_to_url(BASE_URL . '/index.php?view=order_success&token=' . $access_token) ?>" class="flex flex-col sm:flex-row justify-center items-center gap-2 max-w-md mx-auto">
                <input type="hidden" name="action" value="resend_details">
                <input type="hidden" name="order_id" value="<?= $order_id ?>">

                <input type="email" name="email" class="w-full sm:w-auto flex-grow bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Digite o seu email*" required>

                <button type="submit" class="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-button text-sm">
                    Reenviar Detalhes
                </button>
            </form>
            <span style="text-size: 0.8rem; color: red;"><i>* O email TEM corresponder ao email utilizado na encomenda.</i></span>

            <?php if ($email_submitted && $email_sent && $_POST['action'] === 'resend_details'): ?>
            <div class="mt-4 bg-green-900/50 border border-green-800 text-green-100 px-4 py-3 rounded">
                <p><i class="ri-mail-check-line mr-1"></i> Os detalhes da encomenda foram reenviados para o seu email.</p>
            </div>
            <?php endif; ?>
        </div>

        <a href="<?= add_session_param_to_url(BASE_URL . '/index.php') ?>" class="inline-block bg-primary hover:bg-primary/90 text-white py-3 px-6 rounded-button font-medium">
            Voltar à Loja
        </a>
        <?php endif; ?>
    </div>
</div>
