<?php

function reduce_order_item_stock(int $order_item_id, ?PDO $pdo_connection = null): bool
{
    if ($order_item_id <= 0) {
        return false;
    }

    try {
        $pdo = $pdo_connection ?? get_db_connection();
        if (!$pdo) {
            return false;
        }

        $start_transaction = !$pdo->inTransaction();
        if ($start_transaction) {
            $pdo->beginTransaction();
        }

        
        $stmt = $pdo->prepare("
            SELECT oi.id, oi.product_id, oi.variation_id, oi.quantity, oi.stock_reduced,
                   p.product_type, p.stock as product_stock
            FROM order_items oi
            JOIN products p ON oi.product_id = p.id
            WHERE oi.id = ?
        ");
        $stmt->execute([$order_item_id]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$item) {
            if ($start_transaction) $pdo->rollBack();
            return false;
        }

        
        if ($item['product_type'] === 'digital') {
            
            $update_stmt = $pdo->prepare("UPDATE order_items SET stock_reduced = 1 WHERE id = ? AND stock_reduced = 0");
            $update_stmt->execute([$order_item_id]);

            if ($start_transaction) $pdo->commit();
            return true;
        }

        
        if ($item['stock_reduced'] == 1) {
            if ($start_transaction) $pdo->commit(); 
            return true;
        }

        $quantity_to_reduce = (int)$item['quantity'];
        if ($quantity_to_reduce <= 0) {
            if ($start_transaction) $pdo->rollBack();
            return false;
        }

        $stock_updated = false;
        $log_message_entity = "";

        
        $has_custom_fields = false;
        $custom_fields_stmt = $pdo->prepare("
            SELECT COUNT(*) as count FROM order_item_custom_fields WHERE order_item_id = ?
        ");
        $custom_fields_stmt->execute([$order_item_id]);
        $custom_fields_result = $custom_fields_stmt->fetch(PDO::FETCH_ASSOC);
        if ($custom_fields_result && $custom_fields_result['count'] > 0) {
            $has_custom_fields = true;
        }

        if (!empty($item['variation_id']) && $item['variation_id'] > 0) {
            
            $variation_id = (int)$item['variation_id'];
            $log_message_entity = "variation ID: {$variation_id}" . ($has_custom_fields ? " with custom fields" : "");

            
            $stmt = $pdo->prepare("SELECT stock FROM product_variations WHERE id = ?");
            $stmt->execute([$variation_id]);
            $variation_stock_data = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$variation_stock_data) {

                
                $check_stmt = $pdo->prepare("SELECT COUNT(*) as count FROM product_variations WHERE product_id = ?");
                $check_stmt->execute([$variation_id]);
                $check_result = $check_stmt->fetch(PDO::FETCH_ASSOC);

                if ($check_result && $check_result['count'] > 0) {
                    
                    
                    $product_id = (int)$item['product_id'];

                    $var_stmt = $pdo->prepare("SELECT id, stock FROM product_variations WHERE id = ?");
                    $var_stmt->execute([$product_id]);
                    $actual_variation = $var_stmt->fetch(PDO::FETCH_ASSOC);

                    if ($actual_variation) {
                        $variation_id = $product_id; 
                        $variation_stock_data = $actual_variation;

                        
                        $fix_stmt = $pdo->prepare("UPDATE order_items SET product_id = ?, variation_id = ? WHERE id = ?");
                        if ($fix_stmt->execute([$variation_id, $product_id, $order_item_id])) {
                        }
                    } else {
                        if ($start_transaction) $pdo->rollBack();
                        return false;
                    }
                } else {
                    if ($start_transaction) $pdo->rollBack();
                    return false;
                }
            }

            $current_stock = (int)$variation_stock_data['stock'];
            $new_stock = max(0, $current_stock - $quantity_to_reduce);

            $update_stmt = $pdo->prepare("UPDATE product_variations SET stock = ? WHERE id = ?");
            if ($update_stmt->execute([$new_stock, $variation_id])) {
                $stock_updated = true;
            } else {
            }
        } else {
            
            $product_id = (int)$item['product_id'];
            $log_message_entity = "product ID: {$product_id}" . ($has_custom_fields ? " with custom fields" : "");

            $current_stock = (int)$item['product_stock']; 
            $new_stock = max(0, $current_stock - $quantity_to_reduce);

            $update_stmt = $pdo->prepare("UPDATE products SET stock = ?, updated_at = datetime('now', 'localtime') WHERE id = ?");
            if ($update_stmt->execute([$new_stock, $product_id])) {
                $stock_updated = true;
            } else {
            }
        }

        if ($stock_updated) {
            $stmt = $pdo->prepare("UPDATE order_items SET stock_reduced = 1 WHERE id = ?");
            if (!$stmt->execute([$order_item_id])) {
                if ($start_transaction) $pdo->rollBack();
                return false;
            }
        } else {
            
            if ($start_transaction) $pdo->rollBack();
            return false;
        }

        if ($start_transaction) {
            $pdo->commit();
        }
        return true;

    } catch (PDOException $e) {
        if (isset($pdo) && $start_transaction && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function restore_order_item_stock(int $order_item_id, ?PDO $pdo_connection = null): bool
{
    if ($order_item_id <= 0) {
        return false;
    }

    try {
        $pdo = $pdo_connection ?? get_db_connection();
        if (!$pdo) {
            return false;
        }

        $start_transaction = !$pdo->inTransaction();
        if ($start_transaction) {
            $pdo->beginTransaction();
        }

        
        $stmt = $pdo->prepare("
            SELECT oi.id, oi.product_id, oi.variation_id, oi.quantity, oi.stock_reduced, oi.stock_restored,
                   p.product_type, p.stock as product_stock
            FROM order_items oi
            JOIN products p ON oi.product_id = p.id
            WHERE oi.id = ?
        ");
        $stmt->execute([$order_item_id]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$item) {
            if ($start_transaction) $pdo->rollBack();
            return false;
        }

        
        if ($item['product_type'] === 'digital') {
            
            $update_stmt = $pdo->prepare("UPDATE order_items SET stock_restored = 1 WHERE id = ? AND stock_reduced = 1 AND stock_restored = 0");
            $update_stmt->execute([$order_item_id]);
            if ($start_transaction) $pdo->commit();
            return true;
        }

        
        if ($item['stock_reduced'] != 1 || $item['stock_restored'] == 1) {
            if ($start_transaction) $pdo->commit();
            return true;
        }

        $quantity_to_restore = (int)$item['quantity'];
        if ($quantity_to_restore <= 0) {
            if ($start_transaction) $pdo->rollBack();
            return false;
        }

        $stock_updated = false;
        $log_message_entity = "";

        
        $has_custom_fields = false;
        $custom_fields_stmt = $pdo->prepare("
            SELECT COUNT(*) as count FROM order_item_custom_fields WHERE order_item_id = ?
        ");
        $custom_fields_stmt->execute([$order_item_id]);
        $custom_fields_result = $custom_fields_stmt->fetch(PDO::FETCH_ASSOC);
        if ($custom_fields_result && $custom_fields_result['count'] > 0) {
            $has_custom_fields = true;
        }

        if (!empty($item['variation_id']) && $item['variation_id'] > 0) {
            
            $variation_id = (int)$item['variation_id'];
            $log_message_entity = "variation ID: {$variation_id}" . ($has_custom_fields ? " with custom fields" : "");

            
            $stmt = $pdo->prepare("SELECT stock FROM product_variations WHERE id = ?");
            $stmt->execute([$variation_id]);
            $variation_stock_data = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$variation_stock_data) {

                
                $check_stmt = $pdo->prepare("SELECT COUNT(*) as count FROM product_variations WHERE product_id = ?");
                $check_stmt->execute([$variation_id]);
                $check_result = $check_stmt->fetch(PDO::FETCH_ASSOC);

                if ($check_result && $check_result['count'] > 0) {
                    
                    
                    $product_id = (int)$item['product_id'];

                    $var_stmt = $pdo->prepare("SELECT id, stock FROM product_variations WHERE id = ?");
                    $var_stmt->execute([$product_id]);
                    $actual_variation = $var_stmt->fetch(PDO::FETCH_ASSOC);

                    if ($actual_variation) {
                        $variation_id = $product_id; 
                        $variation_stock_data = $actual_variation;

                        
                        $fix_stmt = $pdo->prepare("UPDATE order_items SET product_id = ?, variation_id = ? WHERE id = ?");
                        if ($fix_stmt->execute([$variation_id, $product_id, $order_item_id])) {
                        }
                    } else {
                        if ($start_transaction) $pdo->rollBack();
                        return false;
                    }
                } else {
                    if ($start_transaction) $pdo->rollBack();
                    return false;
                }
            }

            $current_stock = (int)$variation_stock_data['stock'];
            $new_stock = $current_stock + $quantity_to_restore;

            $update_stmt = $pdo->prepare("UPDATE product_variations SET stock = ? WHERE id = ?");
            if ($update_stmt->execute([$new_stock, $variation_id])) {
                $stock_updated = true;
            } else {
            }
        } else {
            
            $product_id = (int)$item['product_id'];
            $log_message_entity = "product ID: {$product_id}" . ($has_custom_fields ? " with custom fields" : "");

            $current_stock = (int)$item['product_stock']; 
            $new_stock = $current_stock + $quantity_to_restore;

            $update_stmt = $pdo->prepare("UPDATE products SET stock = ?, updated_at = datetime('now', 'localtime') WHERE id = ?");
            if ($update_stmt->execute([$new_stock, $product_id])) {
                $stock_updated = true;
            } else {
            }
        }

        if ($stock_updated) {
            $stmt = $pdo->prepare("UPDATE order_items SET stock_restored = 1 WHERE id = ?");
            if (!$stmt->execute([$order_item_id])) {
                if ($start_transaction) $pdo->rollBack();
                return false;
            }
        } else {
            
            if ($start_transaction) $pdo->rollBack();
            return false;
        }

        if ($start_transaction) {
            $pdo->commit();
        }
        return true;

    } catch (PDOException $e) {
        if (isset($pdo) && $start_transaction && $pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function restore_order_stock(int $order_id, ?PDO $pdo_connection = null): array
{
    if ($order_id <= 0) {
        return ['success' => false, 'message' => 'ID da encomenda inválido.'];
    }

    try {
        $pdo = $pdo_connection ?? get_db_connection();
        if (!$pdo) {
             return ['success' => false, 'message' => 'Erro ao conectar à base de dados.'];
        }

        
        
        
        

        
        $sql = "
            SELECT oi.id, p.product_type
            FROM order_items oi
            JOIN products p ON oi.product_id = p.id
            WHERE oi.order_id = ?
        ";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$order_id]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);

        if (empty($items)) {
            return ['success' => true, 'message' => 'Nenhum item encontrado para esta encomenda.']; 
        }

        $restored_count = 0;
        $skipped_count = 0;
        $error_count = 0;

        foreach ($items as $item_summary) {
            
            if ($item_summary['product_type'] === 'digital') {
                $skipped_count++;
                continue;
            }

            
            $result = restore_order_item_stock((int)$item_summary['id'], $pdo);

            if ($result) {
                
                $item_after_restore_stmt = $pdo->prepare("SELECT stock_restored, stock_reduced FROM order_items WHERE id = ?");
                $item_after_restore_stmt->execute([(int)$item_summary['id']]);
                $item_details_after = $item_after_restore_stmt->fetch(PDO::FETCH_ASSOC);

                if ($item_details_after && $item_details_after['stock_reduced'] == 1 && $item_details_after['stock_restored'] == 1) {
                    $restored_count++;
                } else {
                    $skipped_count++;
                }
            } else {
                $error_count++;
            }
        }

        if ($error_count > 0) {
            return [
                'success' => false,
                'message' => "Ocorreram erros ao restaurar o stock. Itens efetivamente restaurados: {$restored_count}, Erros: {$error_count}, Itens ignorados/não aplicável: {$skipped_count}."
            ];
        }

        if ($restored_count === 0 && $skipped_count > 0 && $error_count === 0) {
             return [
                'success' => true,
                'message' => "Nenhum item precisava de restauração de stock ou eram produtos digitais. Itens ignorados/não aplicável: {$skipped_count}."
            ];
        }

        return [
            'success' => true,
            'message' => "Stock processado para {$order_id}. Itens efetivamente restaurados: {$restored_count}. Itens ignorados/não aplicável: {$skipped_count}."
        ];

    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Erro ao restaurar o stock da encomenda: ' . $e->getMessage()];
    }
}

if (!function_exists('get_db_connection')) {
    function get_db_connection_stock_internal(): ?PDO {
        static $pdo_stock = null;
        if ($pdo_stock === null) {
            require_once __DIR__ . '/functions.php'; 
            $pdo_stock = get_db_connection();
        }
        return $pdo_stock;
    }
    
    
    if(!function_exists('get_db_connection')){
        function get_db_connection(): ?PDO {
            return get_db_connection_stock_internal();
        }
    }
}

?>
