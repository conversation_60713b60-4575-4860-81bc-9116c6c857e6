<?php

?>

<h1><PERSON><PERSON><PERSON> para Campos Personalizados</h1>
<a href="admin.php?section=custom_field_fonts&action=new&<?= get_session_id_param() ?>" class="btn btn-success mb-3">
    <i class="bi bi-plus-circle"></i> Adicionar Nova Fonte
</a>

<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Lista de Fontes</h5>
    </div>
    <div class="card-body">
        <?php if (empty($fonts)): ?>
            <div class="alert alert-info">
                Nenhuma fonte encontrada. Clique no botão acima para adicionar uma nova.
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nome</th>
                            <th>Tipo</th>
                            <th>Pré-visualização</th>
                            <th>Estado</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($fonts as $font): ?>
                            <tr>
                                <td><?= $font['id'] ?></td>
                                <td><?= sanitize_input($font['name']) ?></td>
                                <td>
                                    <?php if (!empty($font['file_path'])): ?>
                                        <span class="badge bg-primary">Ficheiro Carregado</span>
                                    <?php elseif (!empty($font['google_font_name'])): ?>
                                        <span class="badge bg-info">Google Fonts</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Não definido</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($font['google_font_name'])): ?>
                                        <div class="font-preview" style="font-family: '<?= sanitize_input($font['google_font_name']) ?>', sans-serif;">
                                            Abc 123
                                        </div>
                                    <?php elseif (!empty($font['file_path'])): ?>
                                        <div class="font-preview">
                                            <img src="<?= get_asset_url('images/font-preview.png') ?>" alt="Font Preview" width="80" height="24">
                                        </div>
                                    <?php else: ?>
                                        <span class="text-muted">Sem pré-visualização</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($font['is_active']): ?>
                                        <span class="badge bg-success">Ativo</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Inativo</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="admin.php?section=custom_field_fonts&action=edit&id=<?= $font['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-primary" title="Editar">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger delete-item-btn" 
                                                data-bs-toggle="modal" 
                                                data-bs-target="#deleteModal" 
                                                data-item-id="<?= $font['id'] ?>"
                                                data-item-name="<?= sanitize_input($font['name']) ?>"
                                                title="Eliminar">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirmar Eliminação</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Tem a certeza que deseja eliminar a fonte <strong id="itemName"></strong>?</p>
                <p class="text-danger">Esta ação não pode ser desfeita.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">Eliminar</a>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle delete confirmation modal
    const deleteModal = document.getElementById('deleteModal');
    if (deleteModal) {
        deleteModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const itemId = button.getAttribute('data-item-id');
            const itemName = button.getAttribute('data-item-name');
            
            document.getElementById('itemName').textContent = itemName;
            document.getElementById('confirmDeleteBtn').href = 'admin.php?section=custom_field_fonts&action=delete&id=' + itemId + '&<?= get_session_id_param() ?>';
        });
    }
    
    // Load Google Fonts if needed
    const googleFonts = [];
    document.querySelectorAll('tbody tr').forEach(row => {
        const fontCell = row.querySelector('td:nth-child(3)');
        if (fontCell && fontCell.textContent.trim() === 'Google Fonts') {
            const fontName = row.querySelector('td:nth-child(2)').textContent.trim();
            googleFonts.push(fontName);
        }
    });
    
    if (googleFonts.length > 0) {
        const fontFamilies = googleFonts.join('|').replace(/ /g, '+');
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = `https://fonts.googleapis.com/css2?family=${fontFamilies}&display=swap`;
        document.head.appendChild(link);
    }
});
</script>
