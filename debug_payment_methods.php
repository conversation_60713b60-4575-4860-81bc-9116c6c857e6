<?php

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/payment_methods.php';

ensure_payment_methods_table_exists();

$test_data = [
    'title' => 'Test Payment Method ' . time(),
    'instructions' => 'These are test instructions for the payment method.',
    'is_active' => 1,
    'sort_order' => 1
];

echo "Testing add_payment_method()...\n";
$new_id = add_payment_method($test_data);
if ($new_id) {
    echo "Success! Added payment method with ID: $new_id\n";
    
    
    echo "\nTesting get_payment_method()...\n";
    $payment_method = get_payment_method($new_id);
    if ($payment_method) {
        echo "Success! Retrieved payment method:\n";
        echo "ID: {$payment_method['id']}\n";
        echo "Title: {$payment_method['title']}\n";
        echo "Instructions: {$payment_method['instructions']}\n";
        echo "Is Active: {$payment_method['is_active']}\n";
        echo "Sort Order: {$payment_method['sort_order']}\n";
        
        
        echo "\nTesting update_payment_method()...\n";
        $update_data = [
            'title' => $payment_method['title'] . ' (Updated)',
            'instructions' => $payment_method['instructions'] . ' (Updated)',
            'is_active' => 0,
            'sort_order' => 2
        ];
        $updated = update_payment_method($new_id, $update_data);
        if ($updated) {
            echo "Success! Updated payment method.\n";
            
            
            $updated_method = get_payment_method($new_id);
            echo "Updated payment method:\n";
            echo "ID: {$updated_method['id']}\n";
            echo "Title: {$updated_method['title']}\n";
            echo "Instructions: {$updated_method['instructions']}\n";
            echo "Is Active: {$updated_method['is_active']}\n";
            echo "Sort Order: {$updated_method['sort_order']}\n";
            
            
            echo "\nTesting delete_payment_method()...\n";
            $deleted = delete_payment_method($new_id);
            if ($deleted) {
                echo "Success! Deleted payment method.\n";
                
                
                $deleted_method = get_payment_method($new_id);
                if (!$deleted_method) {
                    echo "Verified: Payment method no longer exists.\n";
                } else {
                    echo "Error: Payment method still exists after deletion.\n";
                }
            } else {
                echo "Error: Failed to delete payment method.\n";
            }
        } else {
            echo "Error: Failed to update payment method.\n";
        }
    } else {
        echo "Error: Failed to retrieve payment method.\n";
    }
} else {
    echo "Error: Failed to add payment method.\n";
}

echo "\nListing all payment methods:\n";
$all_methods = get_payment_methods();
if (count($all_methods) > 0) {
    foreach ($all_methods as $method) {
        echo "- ID: {$method['id']}, Title: {$method['title']}, Active: {$method['is_active']}\n";
    }
} else {
    echo "No payment methods found.\n";
}

echo "\nDebug completed.\n";
