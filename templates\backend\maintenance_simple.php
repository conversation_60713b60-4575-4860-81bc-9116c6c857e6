<?php

require_once __DIR__ . '/../../includes/maintenance_functions.php';
require_once __DIR__ . '/../../includes/order_functions.php';

$download_tokens_stats = get_download_tokens_stats();
$order_access_tokens_stats = get_order_access_tokens_stats();
$order_visits_stats = get_order_visits_stats();

display_flash_messages();

$csrf_token = generate_csrf_token();
?>

<h1>Manutenção do Sistema</h1>
<p class="lead">Ferramentas para manutenção e otimização do sistema.</p>
<hr>

<script>
// Add confirmation to maintenance forms
document.addEventListener('DOMContentLoaded', function() {
    // Find all maintenance forms and add confirmation
    const maintenanceForms = document.querySelectorAll('.maintenance-form');

    maintenanceForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const actionName = this.getAttribute('data-action-name');
            const confirmMessage = this.getAttribute('data-confirm-message') ||
                                  `Tem certeza que deseja executar esta operação? Esta ação não pode ser desfeita.`;

            if (confirm(confirmMessage)) {
                this.submit();
            }
        });
    });
});
</script>

<div class="row">
    <!-- Tokens de Download -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Tokens de Download</h5>
                <?php if ($download_tokens_stats['success']): ?>
                <span class="badge bg-info"><?= $download_tokens_stats['total'] ?> tokens</span>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if ($download_tokens_stats['success']): ?>
                <div class="mb-3">
                    <h6>Estatísticas</h6>
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Total de tokens
                            <span class="badge bg-primary rounded-pill"><?= $download_tokens_stats['total'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Tokens expirados
                            <span class="badge bg-warning rounded-pill"><?= $download_tokens_stats['expired'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Tokens utilizados
                            <span class="badge bg-success rounded-pill"><?= $download_tokens_stats['used'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Tokens expirados e utilizados
                            <span class="badge bg-secondary rounded-pill"><?= $download_tokens_stats['expired_used'] ?></span>
                        </li>
                    </ul>
                </div>
                <?php else: ?>
                <div class="alert alert-danger">
                    <?= $download_tokens_stats['message'] ?>
                </div>
                <?php endif; ?>

                <h6>Ações</h6>
                <div class="d-grid gap-2">
                    <form method="post" action="admin.php?section=maintenance&action=cleanup_expired_download_tokens&<?= get_session_id_param() ?>"
                          class="maintenance-form"
                          data-action-name="Limpar Tokens Expirados"
                          data-confirm-message="Tem certeza que deseja limpar todos os tokens de download expirados? Esta ação não pode ser desfeita.">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        <input type="hidden" name="only_used" value="0">
                        <button type="submit" class="btn btn-warning w-100 mb-2">
                            <i class="bi bi-trash"></i> Limpar Tokens Expirados
                        </button>
                    </form>

                    <form method="post" action="admin.php?section=maintenance&action=cleanup_expired_download_tokens&<?= get_session_id_param() ?>"
                          class="maintenance-form"
                          data-action-name="Limpar Tokens Expirados e Utilizados"
                          data-confirm-message="Tem certeza que deseja limpar apenas os tokens de download expirados e já utilizados? Esta ação não pode ser desfeita.">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        <input type="hidden" name="only_used" value="1">
                        <button type="submit" class="btn btn-outline-warning w-100">
                            <i class="bi bi-trash"></i> Limpar Apenas Tokens Expirados e Utilizados
                        </button>
                    </form>
                    <form method="post" action="admin.php?section=maintenance&action=cleanup_all_active_download_tokens&<?= get_session_id_param() ?>"
                          class="maintenance-form"
                          data-action-name="Limpar TODOS os Tokens de Download ATIVOS (Não Expirados)"
                          data-confirm-message="Tem certeza que deseja limpar TODOS os tokens de download ATIVOS (NÃO EXPIRADOS)? Esta ação não pode ser desfeita e removerá o acesso a downloads válidos.">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        <button type="submit" class="btn btn-danger w-100 mt-2">
                            <i class="bi bi-exclamation-triangle-fill"></i> Limpar Tokens de Download ATIVOS (Não Expirados)
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Tokens de Acesso a Encomendas -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Tokens de Acesso a Encomendas</h5>
                <?php if ($order_access_tokens_stats['success']): ?>
                <span class="badge bg-info"><?= $order_access_tokens_stats['total'] ?> tokens</span>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if ($order_access_tokens_stats['success']): ?>
                <div class="mb-3">
                    <h6>Estatísticas</h6>
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Total de tokens
                            <span class="badge bg-primary rounded-pill"><?= $order_access_tokens_stats['total'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Tokens expirados
                            <span class="badge bg-warning rounded-pill"><?= $order_access_tokens_stats['expired'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Tokens em formato antigo
                            <span class="badge bg-danger rounded-pill"><?= $order_access_tokens_stats['old_format'] ?></span>
                        </li>
                    </ul>
                </div>
                <?php else: ?>
                <div class="alert alert-danger">
                    <?= $order_access_tokens_stats['message'] ?>
                </div>
                <?php endif; ?>

                <h6>Ações</h6>
                <div class="d-grid gap-2">
                    <form method="post" action="admin.php?section=maintenance&action=cleanup_expired_order_access_tokens&<?= get_session_id_param() ?>"
                          class="maintenance-form"
                          data-action-name="Limpar Tokens de Acesso Expirados"
                          data-confirm-message="Tem certeza que deseja limpar todos os tokens de acesso a encomendas expirados? Esta ação não pode ser desfeita.">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        <button type="submit" class="btn btn-warning w-100 mb-2">
                            <i class="bi bi-trash"></i> Limpar Tokens Expirados
                        </button>
                    </form>

                    <form method="post" action="admin.php?section=orders&action=migrate_tokens&<?= get_session_id_param() ?>"
                          class="maintenance-form"
                          data-action-name="Migrar Tokens de Acesso"
                          data-confirm-message="Tem certeza que deseja migrar os tokens de acesso para o novo formato? Esta operação é segura e pode ser executada várias vezes.">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-arrow-repeat"></i> Migrar Tokens de Acesso
                        </button>
                    </form>
                     <form method="post" action="admin.php?section=maintenance&action=cleanup_all_active_order_access_tokens&<?= get_session_id_param() ?>"
                          class="maintenance-form"
                          data-action-name="Limpar TODOS os Tokens de Acesso a Encomendas ATIVOS (Não Expirados)"
                          data-confirm-message="Tem certeza que deseja limpar TODOS os tokens de acesso a encomendas ATIVOS (NÃO EXPIRADOS)? Esta ação não pode ser desfeita e removerá o acesso a links de encomenda válidos.">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        <button type="submit" class="btn btn-danger w-100 mt-2">
                            <i class="bi bi-exclamation-triangle-fill"></i> Limpar Tokens de Acesso ATIVOS (Não Expirados)
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Otimização de Base de Dados -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">Otimização de Base de Dados</h5>
            </div>
            <div class="card-body">
                <p>Otimize a base de dados para melhorar o desempenho e reduzir o tamanho do ficheiro.</p>
                <div class="d-grid gap-2">
                    <form method="post" action="admin.php?section=maintenance&action=optimize_database&<?= get_session_id_param() ?>"
                          class="maintenance-form"
                          data-action-name="Otimizar Base de Dados"
                          data-confirm-message="Tem certeza que deseja otimizar a base de dados? Esta operação pode demorar algum tempo dependendo do tamanho da base de dados.">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        <button type="submit" class="btn btn-success w-100 mb-2">
                            <i class="bi bi-speedometer"></i> Otimizar Base de Dados
                        </button>
                    </form>

                    <form method="post" action="admin.php?section=maintenance&action=check_database_integrity&<?= get_session_id_param() ?>"
                          class="maintenance-form"
                          data-action-name="Verificar Integridade"
                          data-confirm-message="Tem certeza que deseja verificar a integridade da base de dados? Esta operação pode demorar algum tempo dependendo do tamanho da base de dados.">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        <button type="submit" class="btn btn-info w-100">
                            <i class="bi bi-check-circle"></i> Verificar Integridade
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup de Base de Dados -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">Backup de Base de Dados</h5>
            </div>
            <div class="card-body">
                <p>Crie um backup da base de dados para garantir a segurança dos seus dados.</p>
                <div class="d-grid gap-2">
                    <form method="post" action="admin.php?section=maintenance&action=backup_database&<?= get_session_id_param() ?>"
                          class="maintenance-form"
                          data-action-name="Criar Backup"
                          data-confirm-message="Tem certeza que deseja criar um backup da base de dados? Esta operação pode demorar algum tempo dependendo do tamanho da base de dados.">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-download"></i> Criar Backup
                        </button>
                    </form>
                </div>
                <div class="mt-3" id="backup-result"></div>
            </div>
        </div>
    </div>

    <!-- Visitas a Encomendas -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Visitas a Encomendas</h5>
                <?php if ($order_visits_stats['success']): ?>
                <span class="badge bg-info"><?= $order_visits_stats['total'] ?> visitas</span>
                <?php endif; ?>
            </div>
            <div class="card-body">
                <?php if ($order_visits_stats['success']): ?>
                <div class="mb-3">
                    <h6>Estatísticas</h6>
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Total de visitas
                            <span class="badge bg-primary rounded-pill"><?= $order_visits_stats['total'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Encomendas únicas visitadas
                            <span class="badge bg-success rounded-pill"><?= $order_visits_stats['unique_orders'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Sessões únicas
                            <span class="badge bg-info rounded-pill"><?= $order_visits_stats['unique_sessions'] ?></span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Visitas antigas (> 30 dias)
                            <span class="badge bg-warning rounded-pill"><?= $order_visits_stats['old_visits'] ?></span>
                        </li>
                    </ul>
                </div>
                <?php else: ?>
                <div class="alert alert-danger">
                    <?= $order_visits_stats['message'] ?>
                </div>
                <?php endif; ?>

                <h6>Ações</h6>
                <div class="d-grid gap-2">
                    <form method="post" action="admin.php?section=maintenance&action=cleanup_old_order_visits&<?= get_session_id_param() ?>"
                          class="maintenance-form"
                          data-action-name="Limpar Visitas Antigas"
                          data-confirm-message="Tem certeza que deseja limpar todas as visitas a encomendas com mais de 30 dias? Esta ação não pode ser desfeita.">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        <input type="hidden" name="days_to_keep" value="30">
                        <button type="submit" class="btn btn-warning w-100">
                            <i class="bi bi-trash"></i> Limpar Visitas Antigas (> 30 dias)
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Migrações de Base de Dados -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">Migrações de Base de Dados</h5>
            </div>
            <div class="card-body">
                <p>Execute migrações de base de dados para atualizar a estrutura da base de dados.</p>
                <div class="d-grid gap-2">
                    <form method="post" action="admin.php?section=maintenance&action=migrate_file_types_tables&<?= get_session_id_param() ?>"
                          class="maintenance-form"
                          data-action-name="Migrar Tabelas de Tipos de Arquivo"
                          data-confirm-message="Tem certeza que deseja migrar as tabelas de tipos de arquivo? Esta ação não pode ser desfeita. Isso irá mover dados da tabela digital_product_file_types para digital_files_file_types e remover a tabela digital_product_file_type_associations.">
                        <input type="hidden" name="csrf_token" value="<?= $csrf_token ?>">
                        <button type="submit" class="btn btn-danger w-100 mb-2">
                            <i class="bi bi-database-x"></i> Migrar Tabelas de Tipos de Arquivo
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
