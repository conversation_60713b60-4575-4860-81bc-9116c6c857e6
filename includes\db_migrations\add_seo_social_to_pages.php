<?php

function migrate_add_seo_social_to_pages($pdo) {
    $columns_to_add = [
        'seo_title' => 'TEXT',
        'seo_description' => 'TEXT',
        'seo_keywords' => 'TEXT',
        'og_title' => 'TEXT',
        'og_description' => 'TEXT',
        'og_image' => 'TEXT',
        'twitter_card' => 'TEXT',
        'twitter_title' => 'TEXT',
        'twitter_description' => 'TEXT',
        'twitter_image' => 'TEXT'
    ];

    $migration_name = 'add_seo_social_to_pages';
    $all_successful = true;

    foreach ($columns_to_add as $column_name => $column_type) {
        try {
            
            $stmt = $pdo->query("PRAGMA table_info(pages);");
            $existing_columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 1);

            if (!in_array($column_name, $existing_columns)) {
                $pdo->exec("ALTER TABLE pages ADD COLUMN {$column_name} {$column_type};");
            } else {
            }
        } catch (PDOException $e) {
            $all_successful = false;
        }
    }

    if ($all_successful) {
    } else {
    }
    
    return $all_successful; 
}
?>