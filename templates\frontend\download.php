<?php

require_once __DIR__ . '/../../includes/digital_product_functions.php';
require_once __DIR__ . '/../../includes/license_encryption_functions.php';

cleanup_expired_download_tokens();

$license_code = '';
$error_message = '';
$success_message = '';
$download_ready = false;
$download_info = null;
$license = null;
$requires_token = false;
$security_token = '';
$new_token_sent = false;
$token_invalidated = false;
$invalidated_license_id = 0;

if (isset($_GET['email_token'])) {
    $email_token = sanitize_input($_GET['email_token']);

    
    $token_verification = verify_email_download_token($email_token);

    if ($token_verification['valid']) {
        
        $license_code = $token_verification['license_code'];

        
        $validity = check_license_validity($license_code, false);

        if ($validity['valid']) {
            $license = $validity['license'];
            $success_message = 'Token de download verificado. Clique no botão abaixo para fazer o download.';

            
            $download_result = process_download_request($license_code, null, $email_token);

            if ($download_result['success']) {
                
                $_SESSION['download_file'] = [
                    'path' => $download_result['file_path'],
                    'name' => $download_result['file_name'],
                    'expires' => time() + 300 
                ];

                

                
                session_write_close();

                
                $token = bin2hex(random_bytes(16));
                $download_url = BASE_URL . '/index.php?view=download_file&token=' . $token;
                $download_url = add_session_param_to_url($download_url);

                
                header('Location: ' . $download_url);
                exit;
            } else {
                $error_message = $download_result['message'];
            }
        } else {
            $error_message = $validity['message'];
        }
    } else {
        $error_message = $token_verification['message'];
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['request_new_token']) && isset($_POST['license_id'])) {
    $license_id = (int)sanitize_input($_POST['license_id']);

    
    $token_result = request_new_security_token($license_id);

    if ($token_result['success']) {
        $success_message = $token_result['message'];
        $new_token_sent = true;

        
        $license = get_license_by_id($license_id);
        if ($license) {
            $license_code = $license['license_code'];
            $requires_token = true;
        }
    } else {
        $error_message = $token_result['message'];
    }
}

elseif ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['security_token']) && isset($_POST['license_code'])) {
    $license_code = sanitize_input($_POST['license_code']);
    $security_token = sanitize_input($_POST['security_token']);

    if (empty($license_code)) {
        $error_message = 'Por favor, insira um código de licença.';
    } elseif (empty($security_token)) {
        $error_message = 'Por favor, insira o código de segurança.';
    } else {
        
        $validity = check_license_validity($license_code, false);

        if ($validity['valid']) {
            $license = $validity['license'];

            
            $token_verification = verify_security_token($license['id'], $security_token);

            if ($token_verification['valid']) {
                $success_message = 'Código de segurança verificado. Clique no botão abaixo para fazer o download.';

                
                $download_result = process_download_request($license_code, $security_token);

                if ($download_result['success']) {
                    
                    $_SESSION['download_file'] = [
                        'path' => $download_result['file_path'],
                        'name' => $download_result['file_name'],
                        'expires' => time() + 300 
                    ];

                    

                    
                    session_write_close();

                    
                    $token = bin2hex(random_bytes(16));
                    $download_url = BASE_URL . '/index.php?view=download_file&token=' . $token;
                    $download_url = add_session_param_to_url($download_url);

                    
                    header('Location: ' . $download_url);
                    exit;
                } else {
                    $error_message = $download_result['message'];
                }
            } else {
                $error_message = $token_verification['message'];
                $requires_token = true;

                
                if (isset($token_verification['token_invalidated']) && $token_verification['token_invalidated']) {
                    $token_invalidated = true;
                    $invalidated_license_id = $token_verification['license_id'];
                }
            }
        } else {
            $error_message = $validity['message'];
        }
    }
}

elseif ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['license_code'])) {
    $license_code = sanitize_input($_POST['license_code']);

    if (empty($license_code)) {
        $error_message = 'Por favor, insira um código de licença.';
    } else {
        
        $validity = check_license_validity($license_code, false);

        if ($validity['valid']) {
            $license = $validity['license'];

            
            if (isset($validity['requires_token']) && $validity['requires_token']) {
                $requires_token = true;
                $success_message = $validity['message'];
            } else {
                $success_message = 'Licença válida. Clique no botão abaixo para fazer o download.';
            }
        } else {
            
            $license_data = get_license_by_code($license_code);
            if ($license_data) {
                $license = $license_data;
                $error_message = $validity['message'];
            } else {
                $error_message = $validity['message'];
            }
        }
    }
} elseif (isset($_GET['code'])) {
    
    $license_code = sanitize_input($_GET['code']);

    if (!empty($license_code)) {
        
        $validity = check_license_validity($license_code, false);

        if ($validity['valid']) {
            $license = $validity['license'];

            
            if (isset($validity['requires_token']) && $validity['requires_token']) {
                $requires_token = true;
                $success_message = $validity['message'];
            } else {
                $success_message = 'Licença válida. Clique no botão abaixo para fazer o download.';
            }
        } else {
            
            $license_data = get_license_by_code($license_code);
            if ($license_data) {
                $license = $license_data;
                $error_message = $validity['message'];
            } else {
                $error_message = $validity['message'];
            }
        }
    } else {
        $error_message = 'Código de licença não fornecido.';
    }
}

if (isset($_GET['download']) && $_GET['download'] === '1' && !empty($license_code)) {
    
    $download_result = process_download_request($license_code);

    if ($download_result['success']) {
        
        $_SESSION['download_file'] = [
            'path' => $download_result['file_path'],
            'name' => $download_result['file_name'],
            'expires' => time() + 300 
        ];

        

        
        session_write_close();

        
        $token = bin2hex(random_bytes(16));
        $download_url = BASE_URL . '/index.php?view=download_file&token=' . $token;
        $download_url = add_session_param_to_url($download_url);

        
        header('Location: ' . $download_url);
        exit;
    } else {
        $error_message = $download_result['message'];

        
        if (isset($download_result['requires_token']) && $download_result['requires_token']) {
            $requires_token = true;
            $license = $download_result['license'];
        }
    }
}

if (isset($_GET['error'])) {
    $error_code = sanitize_input($_GET['error']);

    switch ($error_code) {
        case 'invalid_session':
            $error_message = 'Sessão de download inválida. Por favor, tente novamente.';
            break;
        case 'expired':
            $error_message = 'O link de download expirou. Por favor, tente novamente.';
            break;
        case 'file_not_found':
            $error_message = 'Arquivo não encontrado. Por favor, entre em contato com o suporte.';
            break;
        default:
            $error_message = 'Ocorreu um erro durante o download. Por favor, tente novamente.';
    }
}

$digital_products = [];
$file_types = [];

if ($license) {
    
    $digital_products = get_license_digital_products($license['id']);

    
    foreach ($digital_products as $product) {
        $product_file_types = get_digital_product_file_types($product['id']);
        $file_types = array_merge($file_types, $product_file_types);
    }

    
    $unique_file_types = [];
    foreach ($file_types as $type) {
        $unique_file_types[$type['id']] = $type;
    }
    $file_types = array_values($unique_file_types);
}
?>

<div class="max-w-4xl mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-semibold">Download & Verificação de Licença de Produtos Digitais</h1>
        <a href="<?= add_session_param_to_url(BASE_URL . '/index.php') ?>" class="inline-block bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded text-sm">
            <i class="ri-arrow-left-line mr-1"></i> Voltar à Loja
        </a>
    </div>

    <?php if (!empty($error_message)): ?>
        <div class="bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded relative mb-6" role="alert">
            <strong class="font-bold">Erro!</strong>
            <span class="block sm:inline"><?= sanitize_input($error_message) ?></span>
        </div>
    <?php endif; ?>

    <?php if (!empty($success_message)): ?>
        <div class="bg-green-900 border border-green-700 text-green-100 px-4 py-3 rounded relative mb-6" role="alert">
            <strong class="font-bold">Sucesso!</strong>
            <span class="block sm:inline"><?= sanitize_input($success_message) ?></span>
        </div>
    <?php endif; ?>

    <?php if ($license): ?>
        <!-- License details for valid license -->
        <div class="bg-gray-900 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-medium mb-4">Detalhes da Licença</h2>

            <?php
            
            $status_class = 'bg-green-900 text-green-100';
            $status_text = 'Ativo';
            $is_expired = false;
            $is_revoked = false;
            $is_waiting_payment = false;
            $is_limit_reached = false;
            $is_inactive = false;

            if ($license['status'] === 'waiting_payment') {
                $status_class = 'bg-yellow-900 text-yellow-100';
                $status_text = 'Aguardando Pagamento';
                $is_waiting_payment = true;
            } elseif ($license['status'] === 'revoked' || $license['status'] === 'disabled' || $license['status'] === 'canceled') {
                $status_class = 'bg-red-900 text-red-100';
                $status_text = 'Revogada';
                if ($license['status'] === 'disabled') {
                    $status_text = 'Desativada';
                } elseif ($license['status'] === 'canceled') {
                    $status_text = 'Cancelada';
                }
                $is_revoked = true;
                $is_inactive = true;
            } elseif (strtotime($license['expiry_date']) < time()) {
                $status_class = 'bg-red-900 text-red-100';
                $status_text = 'Expirada';
                $is_expired = true;
            } elseif ($license['downloads_used'] >= $license['download_limit']) {
                $status_class = 'bg-orange-900 text-orange-100';
                $status_text = 'Limite Atingido';
                $is_limit_reached = true;
            }

            
            
            if ($is_inactive) {
                $censored_name = get_fully_censored_string($license['customer_name']);
                $censored_email = get_fully_censored_email($license['customer_email']);
            } else {
                $censored_name = get_censored_string($license['customer_name'], 2, 2);
                $censored_email = get_censored_email($license['customer_email']);
            }
            ?>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <p class="text-gray-400 text-sm mb-1">Código de Licença:</p>
                    <p class="font-medium"><?= sanitize_input($license['license_code']) ?></p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Status:</p>
                    <p class="font-medium">
                        <span class="inline-block px-2 py-1 text-xs rounded <?= $status_class ?>"><?= $status_text ?></span>
                    </p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Licenciado para:</p>
                    <p class="font-medium"><?= sanitize_input($censored_name) ?></p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Email:</p>
                    <p class="font-medium"><?= sanitize_input($censored_email) ?></p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Data de Expiração:</p>
                    <p class="font-medium">
                        <?= date('d/m/Y H:i', strtotime($license['expiry_date'])) ?>
                        <?php if (strtotime($license['expiry_date']) > time()): ?>
                            <span class="text-sm text-gray-400">
                                (<?= ceil((strtotime($license['expiry_date']) - time()) / (60 * 60 * 24)) ?> dias restantes)
                            </span>
                        <?php endif; ?>
                    </p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm mb-1">Downloads:</p>
                    <p class="font-medium"><?= $license['downloads_used'] ?> de <?= $license['download_limit'] ?> utilizados</p>
                </div>
            </div>

            <?php if (!empty($file_types)): ?>
                <div class="mb-6">
                    <h3 class="text-lg font-medium mb-2">Extensões incluídas neste download:</h3>
                    <div class="flex flex-wrap gap-2">
                        <?php foreach ($file_types as $type): ?>
                            <span class="inline-block px-3 py-1 text-sm rounded bg-gray-800 text-gray-300">
                                <?= sanitize_input($type['extension']) ?>
                            </span>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($requires_token): ?>
                <!-- Security Token Verification Form -->
                <div class="mt-6 bg-blue-900/30 border border-blue-800 rounded-lg p-6">
                    <h3 class="text-lg font-medium mb-4 text-blue-300">Verificação de Segurança</h3>

                    <?php if ($new_token_sent): ?>
                    <div class="bg-green-800/50 border border-green-700 text-green-100 px-4 py-3 rounded relative mb-4" role="alert">
                        <strong class="font-bold">Sucesso!</strong>
                        <span class="block sm:inline">Um novo código de segurança foi enviado para o seu email.</span>
                    </div>
                    <?php elseif ($token_invalidated): ?>
                    <div class="bg-blue-800/50 border border-blue-700 text-blue-100 px-4 py-3 rounded relative mb-4" role="alert">
                        <strong class="font-bold">Atenção!</strong>
                        <span class="block sm:inline">O código de segurança inserido é inválido. O código anterior foi invalidado por motivos de segurança.</span>

                        <form method="post" action="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download') ?>" class="mt-3">
                            <?php if (function_exists('csrf_token_field')): ?>
                                <?= csrf_token_field() ?>
                            <?php endif; ?>

                            <input type="hidden" name="license_id" value="<?= $invalidated_license_id ?>">
                            <input type="hidden" name="request_new_token" value="1">

                            <button type="submit" class="inline-block bg-blue-600 hover:bg-blue-500 text-white py-2 px-4 rounded text-sm">
                                <i class="ri-refresh-line mr-1"></i> Enviar novo código de segurança
                            </button>
                        </form>
                    </div>
                    <?php endif; ?>

                    <p class="mb-4 text-blue-200">
                        Para proteger seus arquivos digitais, enviamos um código de segurança para o seu email.
                        Por favor, verifique sua caixa de entrada e insira o código abaixo para continuar com o download.
                    </p>

                    <form method="post" action="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download') ?>">
                        <?php if (function_exists('csrf_token_field')): ?>
                            <?= csrf_token_field() ?>
                        <?php endif; ?>

                        <input type="hidden" name="license_code" value="<?= sanitize_input($license_code) ?>">

                        <div class="mb-4">
                            <label for="security_token" class="block text-sm font-medium text-blue-300 mb-1">Código de Segurança</label>
                            <input type="text" class="w-full px-4 py-2 bg-gray-800 border border-blue-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none"
                                   id="security_token" name="security_token" value="" required
                                   placeholder="Digite o código de 6 dígitos" maxlength="6" minlength="6">
                            <p class="text-sm text-blue-300 mt-1">
                                O código de segurança foi enviado para o email associado a esta licença.
                                Se não recebeu o código, verifique sua pasta de spam ou solicite um novo código.
                            </p>
                        </div>

                        <div class="mt-4 flex flex-wrap gap-3">
                            <button type="submit" class="inline-block bg-blue-700 hover:bg-blue-600 text-white py-3 px-6 rounded-button font-medium">
                                <i class="ri-shield-check-line mr-2"></i> Verificar Código
                            </button>

                            <?php if (!$new_token_sent && !$token_invalidated && $license): ?>
                            <button type="button" onclick="document.getElementById('request-new-token-form').submit();" class="inline-block bg-gray-700 hover:bg-gray-600 text-white py-3 px-6 rounded-button font-medium">
                                <i class="ri-refresh-line mr-2"></i> Solicitar Novo Código
                            </button>
                            <?php endif; ?>
                        </div>
                    </form>

                    <?php if (!$new_token_sent && !$token_invalidated && $license): ?>
                    <!-- Hidden form for requesting a new token -->
                    <form id="request-new-token-form" method="post" action="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download') ?>" class="hidden">
                        <?php if (function_exists('csrf_token_field')): ?>
                            <?= csrf_token_field() ?>
                        <?php endif; ?>

                        <input type="hidden" name="license_id" value="<?= $license['id'] ?>">
                        <input type="hidden" name="request_new_token" value="1">
                    </form>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <div class="mt-6">
                    <?php if ($license['status'] === 'active' &&
                              strtotime($license['expiry_date']) > time() &&
                              $license['downloads_used'] < $license['download_limit']): ?>
                        <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download&code=' . urlencode($license_code) . '&download=1') ?>"
                           class="inline-block bg-primary hover:bg-primary/90 text-white py-3 px-6 rounded-button font-medium">
                            <i class="ri-download-line mr-2"></i> Baixar Arquivos
                        </a>
                    <?php else: ?>
                        <button class="inline-block bg-gray-700 text-gray-300 py-3 px-6 rounded-button font-medium cursor-not-allowed" disabled>
                            <i class="ri-download-line mr-2"></i> Baixar Arquivos
                        </button>
                        <p class="text-sm text-gray-400 mt-2">
                            <?php if ($license['status'] === 'waiting_payment'): ?>
                                Aguardando confirmação de pagamento para autorizar o download.
                            <?php elseif ($license['status'] === 'revoked'): ?>
                                Esta licença foi revogada. Entre em contato com o suporte.
                            <?php elseif (strtotime($license['expiry_date']) < time()): ?>
                                O período de download expirou em <?= date('d/m/Y', strtotime($license['expiry_date'])) ?>.
                                <br>
                                <span class="text-gray-300 mt-2">
                                    Esta página mostra os detalhes da sua licença para fins de verificação, mesmo após a expiração do período de download.
                                </span>
                            <?php elseif ($license['downloads_used'] >= $license['download_limit']): ?>
                                Você atingiu o limite máximo de downloads para este produto.
                            <?php endif; ?>
                        </p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>

        <div class="bg-gray-900 rounded-lg p-6">
            <h2 class="text-xl font-medium mb-4">Termos de Licença</h2>
            <div class="prose prose-invert max-w-none">
                <?php
                
                $digital_files_placeholder = get_page_placeholder_by_slug('ficheiros-digitais');

                if ($digital_files_placeholder):
                    
                    $placeholder_pages = get_pages_by_placeholder_id($digital_files_placeholder['id']);

                    if (!empty($placeholder_pages)):
                ?>
                    <h3 class="text-lg font-medium text-gray-200 mb-3"><?= htmlspecialchars($digital_files_placeholder['name']) ?></h3>
                    <ul class="space-y-2">
                        <?php foreach ($placeholder_pages as $page): ?>
                            <li>
                                <a href="<?= get_page_url($page['slug']) ?>" class="text-gray-400 hover:text-white flex items-center" target="_blank" rel="noopener noreferrer">
                                    <i class="ri-file-text-line mr-2"></i>
                                    <?= htmlspecialchars($page['title']) ?>
                                </a>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php
                    else:
                        
                        echo nl2br(sanitize_input(get_setting('digital_license_text', 'Este ficheiro está licenciado apenas para uso pessoal e uso comercial limitado. A revenda ou distribuição não é permitida sem autorização expressa do autor, mesmo que este produto tenha sido obtido de forma gratuita.')));
                    endif;
                else:
                    
                    echo nl2br(sanitize_input(get_setting('digital_license_text', 'Este ficheiro está licenciado apenas para uso pessoal e uso comercial limitado. A revenda ou distribuição não é permitida sem autorização expressa do autor, mesmo que este produto tenha sido obtido de forma gratuita.')));
                endif;
                ?>
            </div>
        </div>
    <?php else: ?>
        <!-- License code form -->
        <div class="bg-gray-900 rounded-lg p-6 mb-6">
            <h2 class="text-xl font-medium mb-4">Acesse aos seus ficheiros digitais aqui</h2>
            <p class="mb-4">De modo a recuperar os seus ficheiros digitais, insira aqui o código de licença que recebeu por email após a sua compra. Recorde que este códido tem limite de utilizações para download assim como tempo de validade e que apenas será válido após confirmação de pagamento.</p>

            <form method="post" action="<?= add_session_param_to_url(BASE_URL . '/index.php?view=download') ?>">
                <?php if (function_exists('csrf_token_field')): ?>
                    <?= csrf_token_field() ?>
                <?php endif; ?>

                <div class="mb-4">
                    <label for="license_code" class="block text-sm font-medium text-gray-300 mb-1">Código de Licença</label>
                    <input type="text" class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none"
                           id="license_code" name="license_code" value="<?= sanitize_input($license_code) ?>" required
                           placeholder="Ex: XXXX-XXXX-XXXX-XXXX">
                    <p class="text-sm text-gray-400 mt-1">
                        O código de licença foi enviado para o email que você forneceu durante a compra. Este código é a prova da sua compra e é valido para download dos ficheiros de forma manual durante tempo limitado!
                    </p>
                </div>

                <div class="mt-6">
                    <button type="submit" class="inline-block bg-primary hover:bg-primary/90 text-white py-3 px-6 rounded-button font-medium">
                        <i class="ri-search-line mr-2"></i> Verificar Licença
                    </button>
                </div>
            </form>
        </div>

        <div class="bg-blue-900/30 border border-blue-800 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0 mt-0.5">
                    <i class="ri-information-line text-blue-400 text-lg"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-400">Não encontrou seu código de licença?</h3>
                    <div class="mt-2 text-sm text-blue-300">
                        <p>Se não recebeu o código de licença ou está com problemas em aceder seus downloads, entre em contato.</p>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
