<?php

require_once __DIR__ . '/../../includes/digital_product_functions.php';

$filter_product_id = isset($_GET['product_id']) ? (int)$_GET['product_id'] : 0;
$filter_customer_email = isset($_GET['customer_email']) ? sanitize_input($_GET['customer_email']) : '';
$filter_date_from = isset($_GET['date_from']) ? sanitize_input($_GET['date_from']) : '';
$filter_date_to = isset($_GET['date_to']) ? sanitize_input($_GET['date_to']) : '';

$conditions = [];
$params = [];

if ($filter_product_id > 0) {
    $conditions[] = "dp.product_id = :product_id";
    $params[':product_id'] = $filter_product_id;
}

if (!empty($filter_customer_email)) {
    
    $conditions[] = "l.customer_email = :customer_email";
    $params[':customer_email'] = $filter_customer_email;
}

if (!empty($filter_date_from)) {
    $conditions[] = "d.download_date >= :date_from";
    $params[':date_from'] = $filter_date_from . ' 00:00:00';
}

if (!empty($filter_date_to)) {
    $conditions[] = "d.download_date <= :date_to";
    $params[':date_to'] = $filter_date_to . ' 23:59:59';
}

$where_clause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";

$count_sql = "SELECT COUNT(*) as total FROM downloads d
              JOIN licenses l ON d.license_id = l.id
              JOIN license_files lf ON l.id = lf.license_id
              JOIN digital_products dp ON lf.digital_product_id = dp.id
              JOIN products p ON dp.product_id = p.id
              {$where_clause}";
$total_result = db_query($count_sql, $params, true);
$total_downloads = $total_result ? (int)$total_result['total'] : 0;

$page = isset($_GET['p']) ? max(1, (int)$_GET['p']) : 1;
$per_page = 20;
$total_pages = ceil($total_downloads / $per_page);
$offset = ($page - 1) * $per_page;

$sql = "SELECT d.*, l.license_code, l.customer_name, l.customer_email, l.is_encrypted, p.name_pt as product_name, p.id as product_id
        FROM downloads d
        JOIN licenses l ON d.license_id = l.id
        JOIN license_files lf ON l.id = lf.license_id
        JOIN digital_products dp ON lf.digital_product_id = dp.id
        JOIN products p ON dp.product_id = p.id
        {$where_clause}
        ORDER BY d.download_date DESC
        LIMIT {$per_page} OFFSET {$offset}";

$downloads = db_query($sql, $params, false, true);

$products_sql = "SELECT p.id, p.name_pt
                FROM products p
                JOIN digital_products dp ON p.id = dp.product_id
                WHERE p.product_type = 'digital'
                ORDER BY p.name_pt ASC";
$digital_products = db_query($products_sql, [], false, true);

$customer_emails_sql = "SELECT DISTINCT l.customer_email, l.customer_name, l.is_encrypted
                       FROM downloads d
                       JOIN licenses l ON d.license_id = l.id
                       ORDER BY l.customer_name ASC";
$customer_emails_raw = db_query($customer_emails_sql, [], false, true);

$customer_emails = [];
foreach ($customer_emails_raw as $email_data) {
    
    $license = [
        'customer_email' => $email_data['customer_email'],
        'customer_name' => $email_data['customer_name'],
        'is_encrypted' => $email_data['is_encrypted']
    ];

    
    $decrypted_name = get_decrypted_license_name($license);
    $decrypted_email = get_decrypted_license_email($license);

    $customer_emails[] = [
        'customer_email' => $email_data['customer_email'], 
        'customer_name' => $decrypted_name,
        'decrypted_email' => $decrypted_email 
    ];
}

$stats_sql = "SELECT
                COUNT(*) as total_downloads,
                COUNT(DISTINCT l.id) as unique_licenses,
                COUNT(DISTINCT l.customer_email) as unique_customers,
                COUNT(DISTINCT dp.product_id) as unique_products,
                MAX(d.download_date) as last_download
              FROM downloads d
              JOIN licenses l ON d.license_id = l.id
              JOIN license_files lf ON l.id = lf.license_id
              JOIN digital_products dp ON lf.digital_product_id = dp.id
              {$where_clause}";
$stats = db_query($stats_sql, $params, true);

$top_products_sql = "SELECT
                      p.id, p.name_pt, COUNT(*) as download_count
                    FROM downloads d
                    JOIN licenses l ON d.license_id = l.id
                    JOIN license_files lf ON l.id = lf.license_id
                    JOIN digital_products dp ON lf.digital_product_id = dp.id
                    JOIN products p ON dp.product_id = p.id
                    {$where_clause}
                    GROUP BY p.id
                    ORDER BY download_count DESC
                    LIMIT 5";
$top_products = db_query($top_products_sql, $params, false, true);

$top_customers_sql = "SELECT
                        l.customer_email, l.customer_name, l.is_encrypted, COUNT(*) as download_count
                      FROM downloads d
                      JOIN licenses l ON d.license_id = l.id
                      {$where_clause}
                      GROUP BY l.customer_email
                      ORDER BY download_count DESC
                      LIMIT 5";
$top_customers_raw = db_query($top_customers_sql, $params, false, true);

$top_customers = [];
foreach ($top_customers_raw as $customer) {
    
    $license = [
        'customer_email' => $customer['customer_email'],
        'customer_name' => $customer['customer_name'],
        'is_encrypted' => $customer['is_encrypted']
    ];

    
    $decrypted_name = get_decrypted_license_name($license);
    $decrypted_email = get_decrypted_license_email($license);

    $top_customers[] = [
        'customer_name' => $decrypted_name,
        'customer_email' => $decrypted_email,
        'download_count' => $customer['download_count']
    ];
}

display_flash_messages();
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Estatísticas de Downloads</h1>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-4">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="me-3">
                            <div class="text-white-75 small">Total de Downloads</div>
                            <div class="text-lg fw-bold"><?= number_format($stats['total_downloads'] ?? 0) ?></div>
                        </div>
                        <i class="bi bi-download text-white-50" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="me-3">
                            <div class="text-white-75 small">Licenças Únicas</div>
                            <div class="text-lg fw-bold"><?= number_format($stats['unique_licenses'] ?? 0) ?></div>
                        </div>
                        <i class="bi bi-key text-white-50" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="me-3">
                            <div class="text-white-75 small">Clientes Únicos</div>
                            <div class="text-lg fw-bold"><?= number_format($stats['unique_customers'] ?? 0) ?></div>
                        </div>
                        <i class="bi bi-people text-white-50" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-4">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="me-3">
                            <div class="text-white-75 small">Último Download</div>
                            <div class="text-lg fw-bold"><?= !empty($stats['last_download']) ? date('d/m/Y H:i', strtotime($stats['last_download'])) : 'N/A' ?></div>
                        </div>
                        <i class="bi bi-clock-history text-white-50" style="font-size: 2rem;"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Top Products -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <i class="bi bi-bar-chart-fill me-1"></i>
                    Top 5 Produtos Mais Baixados
                </div>
                <div class="card-body">
                    <?php if (!empty($top_products)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Produto</th>
                                        <th class="text-end">Downloads</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($top_products as $product): ?>
                                        <tr>
                                            <td>
                                                <a href="admin.php?section=products&action=edit&id=<?= $product['id'] ?>&<?= get_session_id_param() ?>">
                                                    <?= sanitize_input($product['name_pt']) ?>
                                                </a>
                                            </td>
                                            <td class="text-end"><?= number_format($product['download_count']) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info mb-0">
                            Nenhum download registrado.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Top Customers -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <i class="bi bi-people-fill me-1"></i>
                    Top 5 Clientes
                </div>
                <div class="card-body">
                    <?php if (!empty($top_customers)): ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Cliente</th>
                                        <th class="text-end">Downloads</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($top_customers as $customer): ?>
                                        <tr>
                                            <td>
                                                <?= sanitize_input($customer['customer_name']) ?>
                                                <div class="small text-muted"><?= sanitize_input($customer['customer_email']) ?></div>
                                            </td>
                                            <td class="text-end"><?= number_format($customer['download_count']) ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info mb-0">
                            Nenhum download registrado.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Form -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-funnel-fill me-1"></i>
            Filtrar Downloads
        </div>
        <div class="card-body">
            <form method="get" action="admin.php" class="row g-3">
                <input type="hidden" name="section" value="download_statistics">
                <input type="hidden" name="<?= session_name() ?>" value="<?= session_id() ?>">

                <div class="col-md-3">
                    <label for="product_id" class="form-label">Produto</label>
                    <select class="form-select" id="product_id" name="product_id">
                        <option value="0">Todos os Produtos</option>
                        <?php foreach ($digital_products as $product): ?>
                            <option value="<?= $product['id'] ?>" <?= $filter_product_id == $product['id'] ? 'selected' : '' ?>>
                                <?= sanitize_input($product['name_pt']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="customer_email" class="form-label">Email do Cliente</label>
                    <select class="form-select" id="customer_email" name="customer_email">
                        <option value="">Todos os Clientes</option>
                        <?php foreach ($customer_emails as $email): ?>
                            <option value="<?= $email['customer_email'] ?>" <?= $filter_customer_email === $email['customer_email'] ? 'selected' : '' ?>>
                                <?= sanitize_input($email['customer_name']) ?> (<?= sanitize_input($email['decrypted_email']) ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="col-md-2">
                    <label for="date_from" class="form-label">Data Inicial</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?= $filter_date_from ?>">
                </div>

                <div class="col-md-2">
                    <label for="date_to" class="form-label">Data Final</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?= $filter_date_to ?>">
                </div>

                <div class="col-md-2 d-flex align-items-end">
                    <div class="d-grid gap-2 w-100">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search me-1"></i> Filtrar
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Downloads Table -->
    <div class="card">
        <div class="card-header">
            <i class="bi bi-table me-1"></i>
            Histórico de Downloads
        </div>
        <div class="card-body">
            <?php if (!empty($downloads)): ?>
                <!-- Group downloads by client and decrypt data -->
                <?php
                $downloads_by_client = [];
                foreach ($downloads as $download) {
                    $client_key = $download['customer_email'];

                    
                    $license = [
                        'customer_email' => $download['customer_email'],
                        'customer_name' => $download['customer_name'],
                        'is_encrypted' => $download['is_encrypted']
                    ];

                    
                    $decrypted_name = get_decrypted_license_name($license);
                    $decrypted_email = get_decrypted_license_email($license);

                    if (!isset($downloads_by_client[$client_key])) {
                        $downloads_by_client[$client_key] = [
                            'customer_name' => $decrypted_name,
                            'customer_email' => $decrypted_email,
                            'downloads' => []
                        ];
                    }

                    
                    $download['decrypted_name'] = $decrypted_name;
                    $download['decrypted_email'] = $decrypted_email;
                    $downloads_by_client[$client_key]['downloads'][] = $download;
                }
                ?>

                <!-- Display downloads grouped by client -->
                <?php foreach ($downloads_by_client as $client_key => $client_data): ?>
                    <div class="card mb-4">
                        <div class="card-header" style="background-color: #f8f9fa;">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-person-fill me-2"></i>
                                    <strong style="color: #000000; font-weight: 600;"><?= sanitize_input($client_data['customer_name']) ?></strong>
                                    <span style="color: #000000; font-weight: 500;" class="ms-2">(<?= sanitize_input($client_data['customer_email']) ?>)</span>
                                </h5>
                                <div>
                                    <span class="badge bg-primary"><?= count($client_data['downloads']) ?> downloads</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover mb-0">
                                    <thead style="background-color: #343a40; color: white;">
                                        <tr>
                                            <th class="align-middle">Data</th>
                                            <th class="align-middle">Produto</th>
                                            <th class="align-middle">Licença</th>
                                            <th class="align-middle">IP</th>
                                            <th class="align-middle">Navegador</th>
                                            <th class="align-middle">Ações</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($client_data['downloads'] as $download): ?>
                                            <tr>
                                                <td><?= date('d/m/Y H:i', strtotime($download['download_date'])) ?></td>
                                                <td>
                                                    <a href="admin.php?section=products&action=edit&id=<?= $download['product_id'] ?>&<?= get_session_id_param() ?>">
                                                        <?= sanitize_input($download['product_name']) ?>
                                                    </a>
                                                </td>
                                                <td>
                                                    <a href="admin.php?section=licenses&action=edit&id=<?= $download['license_id'] ?>&<?= get_session_id_param() ?>">
                                                        <?= sanitize_input($download['license_code']) ?>
                                                    </a>
                                                </td>
                                                <td><?= sanitize_input($download['ip_address']) ?></td>
                                                <td class="small text-truncate" style="max-width: 200px;"><?= sanitize_input($download['user_agent']) ?></td>
                                                <td>
                                                    <a href="admin.php?section=download_statistics&action=delete&id=<?= $download['id'] ?>&product_id=<?= $filter_product_id ?>&customer_email=<?= urlencode($filter_customer_email) ?>&date_from=<?= $filter_date_from ?>&date_to=<?= $filter_date_to ?>&p=<?= $page ?>&csrf_token=<?= urlencode($_SESSION['csrf_token'] ?? '') ?>&<?= get_session_id_param() ?>"
                                                       class="btn btn-sm btn-danger"
                                                       onclick="return confirm('Tem certeza que deseja remover este registro de download?');">
                                                        <i class="bi bi-trash"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <nav aria-label="Page navigation" class="mt-3">
                        <ul class="pagination justify-content-center">
                            <!-- Previous Button -->
                            <li class="page-item <?= ($page <= 1) ? 'disabled' : '' ?>">
                                <a class="page-link" href="admin.php?section=download_statistics<?=
                                    ($filter_product_id > 0 ? '&product_id=' . $filter_product_id : '') .
                                    (!empty($filter_customer_email) ? '&customer_email=' . urlencode($filter_customer_email) : '') .
                                    (!empty($filter_date_from) ? '&date_from=' . $filter_date_from : '') .
                                    (!empty($filter_date_to) ? '&date_to=' . $filter_date_to : '') .
                                    '&p=' . ($page - 1) .
                                    '&' . get_session_id_param() ?>" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>

                            <?php
                            
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);

                            if ($start_page > 1) {
                                echo '<li class="page-item"><a class="page-link" href="admin.php?section=download_statistics' .
                                    ($filter_product_id > 0 ? '&product_id=' . $filter_product_id : '') .
                                    (!empty($filter_customer_email) ? '&customer_email=' . urlencode($filter_customer_email) : '') .
                                    (!empty($filter_date_from) ? '&date_from=' . $filter_date_from : '') .
                                    (!empty($filter_date_to) ? '&date_to=' . $filter_date_to : '') .
                                    '&p=1&' . get_session_id_param() . '">1</a></li>';
                                if ($start_page > 2) {
                                    echo '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
                                }
                            }

                            for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <li class="page-item <?= ($i == $page) ? 'active' : '' ?>">
                                    <a class="page-link" href="admin.php?section=download_statistics<?=
                                        ($filter_product_id > 0 ? '&product_id=' . $filter_product_id : '') .
                                        (!empty($filter_customer_email) ? '&customer_email=' . urlencode($filter_customer_email) : '') .
                                        (!empty($filter_date_from) ? '&date_from=' . $filter_date_from : '') .
                                        (!empty($filter_date_to) ? '&date_to=' . $filter_date_to : '') .
                                        '&p=' . $i .
                                        '&' . get_session_id_param() ?>"><?= $i ?></a>
                                </li>
                            <?php endfor;

                            if ($end_page < $total_pages) {
                                if ($end_page < $total_pages - 1) {
                                    echo '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
                                }
                                echo '<li class="page-item"><a class="page-link" href="admin.php?section=download_statistics' .
                                    ($filter_product_id > 0 ? '&product_id=' . $filter_product_id : '') .
                                    (!empty($filter_customer_email) ? '&customer_email=' . urlencode($filter_customer_email) : '') .
                                    (!empty($filter_date_from) ? '&date_from=' . $filter_date_from : '') .
                                    (!empty($filter_date_to) ? '&date_to=' . $filter_date_to : '') .
                                    '&p=' . $total_pages .
                                    '&' . get_session_id_param() . '">' . $total_pages . '</a></li>';
                            }
                            ?>

                            <!-- Next Button -->
                            <li class="page-item <?= ($page >= $total_pages) ? 'disabled' : '' ?>">
                                <a class="page-link" href="admin.php?section=download_statistics<?=
                                    ($filter_product_id > 0 ? '&product_id=' . $filter_product_id : '') .
                                    (!empty($filter_customer_email) ? '&customer_email=' . urlencode($filter_customer_email) : '') .
                                    (!empty($filter_date_from) ? '&date_from=' . $filter_date_from : '') .
                                    (!empty($filter_date_to) ? '&date_to=' . $filter_date_to : '') .
                                    '&p=' . ($page + 1) .
                                    '&' . get_session_id_param() ?>" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php else: ?>
                <div class="alert alert-info">
                    Nenhum download encontrado com os filtros selecionados.
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

