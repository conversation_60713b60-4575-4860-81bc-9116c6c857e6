<?php
header('Content-Type: application/json');
error_reporting(E_ALL); // Good for development
ini_set('display_errors', 1); // Good for development

// Assuming this script is in C:\xampp\htdocs\
// And your audio files are in C:\xampp\htdocs\public\assets\audio\
$audioDir = __DIR__ . '/public/assets/audio/'; // Adjust if script is elsewhere

// Check if the directory exists
if (!is_dir($audioDir)) {
    echo json_encode(['error' => 'Audio directory not found: ' . $audioDir]);
    exit;
}

$files = scandir($audioDir);
if ($files === false) {
    echo json_encode(['error' => 'Could not scan audio directory: ' . $audioDir]);
    exit;
}

$audioFiles = [];

foreach ($files as $file) {
    if ($file === '.' || $file === '..') {
        continue;
    }

    $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
    $filenameWithoutExt = pathinfo($file, PATHINFO_FILENAME);

    if ($extension === 'mp3' || $extension === 'm4a') {
        $txtFilePath = $audioDir . $filenameWithoutExt . '.txt';
        $descriptionText = 'No description available.'; // Default

        if (file_exists($txtFilePath)) {
            $content = file_get_contents($txtFilePath); // Read the file
            if ($content !== false) { // Check if read was successful
                // THIS IS THE FIX: Ensure $content is a string before strip_tags
                $descriptionText = strip_tags($content); // $content is now guaranteed to be a string (even if empty)
            } else {
                // File exists, but couldn't be read
                $descriptionText = 'Error reading description file.';
                // You might want to log this server-side:
                // error_log("Could not read content from: " . $txtFilePath);
            }
        }

        $audioFiles[] = [
            'audio' => 'public/assets/audio/' . $file, // Relative path for client
            // 'text' => 'public/assets/audio/' . $filenameWithoutExt . '.txt', // Path (if client fetches)
            'description' => trim($descriptionText), // Actual description content
            'displayName' => $filenameWithoutExt
        ];
    }
}

if (empty($audioFiles)) {
    // Send a specific message if no files are found, rather than just an empty array
    // (though an empty array is also valid JSON for the client to handle)
    // echo json_encode(['message' => 'No compatible audio files with descriptions found.']);
    // For consistency with client expecting an array of files:
    echo json_encode([]);
} else {
    echo json_encode($audioFiles);
}
?>