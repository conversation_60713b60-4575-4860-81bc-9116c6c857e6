<?php

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/db.php'; 

$pdo_for_session = null;

$current_user_fingerprint = null;

if (!defined('SESSION_UUID_HEADER')) {
    define('SESSION_UUID_HEADER', 'X-Session-UUID');
}
if (!defined('SESSION_ID_REGEX')) {
    define('SESSION_ID_REGEX', '/^[a-f0-9]{64}$/i');
}

function get_customer_ip(): string
{
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'UNKNOWN';
    }
    
    if (strpos($ip, ',') !== false) {
        $ip = trim(explode(',', $ip)[0]);
    }
    return filter_var($ip, FILTER_VALIDATE_IP) ?: 'UNKNOWN';
}

function get_user_fingerprint(): string
{
    $ipAddress = get_customer_ip();
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $acceptLanguage = $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '';
    $acceptEncoding = $_SERVER['HTTP_ACCEPT_ENCODING'] ?? ''; 
    
    $rawFingerprint = $ipAddress . '|' . $userAgent . '|' . $acceptLanguage . '|' . $acceptEncoding . '|' . SESSION_FINGERPRINT_SALT;
    return hash('sha256', $rawFingerprint);
}

function generate_session_id(): string
{
    try {
        return bin2hex(random_bytes(32));
    } catch (Exception $e) {
        
        
        return hash('sha256', uniqid(microtime() . SESSION_FINGERPRINT_SALT, true));
    }
}

function read_session(string $session_id): array|null|false
{
    global $current_user_fingerprint; 
    $pdo = get_db_connection();

    if (!$pdo) {
        
        return false; 
    }

    
    if (empty($current_user_fingerprint)) {
        
        
        $current_user_fingerprint = get_user_fingerprint();
        if (empty($current_user_fingerprint)) {
            
            return null; 
        }
    }

    try {
        $stmt = $pdo->prepare(
            "SELECT data, user_fingerprint
             FROM sessions
             WHERE session_id = :id AND expires_at > datetime('now', 'localtime')"
        );
        $stmt->bindParam(':id', $session_id, PDO::PARAM_STR);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result === false) {
            
            if ($stmt->errorCode() === '00000') {
                
                
                return null; 
            } else {
                
                
                return false; 
            }
        }

        
        $stored_fingerprint = $result['user_fingerprint'] ?? null;

        if ($stored_fingerprint !== $current_user_fingerprint) {
            
            
            
            return null; 
        }

        
        return [
            'data' => $result['data'] ?? '',
            'fingerprint' => $stored_fingerprint 
        ];

    } catch (PDOException $e) {
        
        return false; 
    }
}

function write_session(string $session_id, string $data): bool
{
    global $pdo_for_session, $current_user_fingerprint;

    if (!$pdo_for_session) {
        $pdo_for_session = get_db_connection();
        if (!$pdo_for_session) {
            return false;
        }
    }

    if (empty($current_user_fingerprint)) {
        
        $current_user_fingerprint = get_user_fingerprint();
        if (empty($current_user_fingerprint)) {
             return false; 
        }
    }

    try {
        $last_access = date('Y-m-d H:i:s');
        $expires_at = date('Y-m-d H:i:s', time() + SESSION_LIFETIME_SECONDS);

        
        $sql = "INSERT OR REPLACE INTO sessions (session_id, data, user_fingerprint, created_at, last_access, expires_at)
                VALUES (:id, :data, :fingerprint,
                        COALESCE((SELECT created_at FROM sessions WHERE session_id = :id), :created_at), -- Keep original created_at if exists
                        :last_access, :expires_at)";

        $stmt = $pdo_for_session->prepare($sql);
        $params = [
            ':id' => $session_id,
            ':data' => $data,
            ':fingerprint' => $current_user_fingerprint,
            ':created_at' => $last_access, 
            ':last_access' => $last_access,
            ':expires_at' => $expires_at
        ];

        return $stmt->execute($params);

    } catch (PDOException $e) {
        return false;
    }
}

function destroy_session(string $session_id): bool
{
    global $pdo_for_session;

    if (!$pdo_for_session) {
        $pdo_for_session = get_db_connection();
         if (!$pdo_for_session) {
              
              return false;
         }
    }

    try {
        $stmt = $pdo_for_session->prepare("DELETE FROM sessions WHERE session_id = :id");
        $stmt->bindParam(':id', $session_id, PDO::PARAM_STR);
        return $stmt->execute();
    } catch (PDOException $e) {
        
        return false;
    }
}

function gc_session(int $max_lifetime_ignored): int|false
{
    global $pdo_for_session;

    if (!$pdo_for_session) {
        $pdo_for_session = get_db_connection();
         if (!$pdo_for_session) {
              
              return false;
         }
    }

    try {
        $stmt = $pdo_for_session->prepare("DELETE FROM sessions WHERE expires_at <= datetime('now', 'localtime')");
        $stmt->execute();
        return $stmt->rowCount();
    } catch (PDOException $e) {
        
        return false;
    }
}

function open_session(string $savePath, string $sessionName): bool
{
    global $pdo_for_session;

     if (!$pdo_for_session) {
         $pdo_for_session = get_db_connection();
     }
     
     return ($pdo_for_session instanceof PDO);
}

function close_session(): bool
{
    global $pdo_for_session;
    
    
    
    
    
    return true;
}

function start_cookieless_session(): string
{
    global $pdo_for_session, $current_user_fingerprint;

    
    $pdo_for_session = get_db_connection();
    if (!$pdo_for_session) {
        
        
        die("Erro crítico: Não foi possível estabelecer ligação à base de dados para a sessão.");
    }

    
    
    if (session_status() === PHP_SESSION_NONE) {
        ini_set('session.use_cookies', '0');
        ini_set('session.use_only_cookies', '0');
        ini_set('session.use_trans_sid', '0'); 
        ini_set('session.cache_limiter', 'nocache');
        ini_set('session.gc_probability', '1'); 
        ini_set('session.gc_divisor', '100');
        ini_set('session.gc_maxlifetime', SESSION_LIFETIME_SECONDS);
    }

    
    if (session_status() === PHP_SESSION_NONE) {
        session_set_save_handler(
            'open_session',
            'close_session',
            function (string $id): string|false { 
                
                $session_info = read_session($id); 
                if ($session_info === false) {
                    
                    
                    
                    return "";
                }
                if ($session_info === null) {
                     
                     return ""; 
                }
                
                return $session_info['data'] ?? ''; 
            },
            'write_session', 
            'destroy_session',
            'gc_session'
        );
    }
    
    register_shutdown_function('session_write_close');

    

    
    
    $current_user_fingerprint = get_user_fingerprint();

    
    $session_id_to_use = null;
    $generate_new_session = true;
    $session_source = 'none'; 
    $log_reason = "";

    
    $uuid_header_key = 'HTTP_' . str_replace('-', '_', strtoupper(SESSION_UUID_HEADER));
    $uuid_from_header = $_SERVER[$uuid_header_key] ?? null;

    if ($uuid_from_header && preg_match(SESSION_ID_REGEX, $uuid_from_header)) {
        
        $session_info = read_session($uuid_from_header);

        if ($session_info === false) { 
            $log_reason = "DB error reading session via UUID header '$uuid_from_header'.";
        } elseif ($session_info === null) { 
            $log_reason = "Session ID from UUID header '$uuid_from_header' not found, expired, or fingerprint mismatch.";
        } else { 
            $session_id_to_use = $uuid_from_header;
            $generate_new_session = false;
            $session_source = 'uuid_header';
            $log_reason = "Session found via valid UUID header.";
        }
    } elseif ($uuid_from_header) {
        $log_reason = "Invalid UUID header format received: '$uuid_from_header'.";
    }

    
    if ($session_id_to_use === null) {
        $session_id_from_url = $_GET[SESSION_PARAM_NAME] ?? $_POST[SESSION_PARAM_NAME] ?? null;
        if ($session_id_from_url && preg_match(SESSION_ID_REGEX, $session_id_from_url)) {
            $session_info = read_session($session_id_from_url); 

            if ($session_info === false) { 
                $log_reason = "DB error reading session via URL param '$session_id_from_url'.";
            } elseif ($session_info === null) { 
                $log_reason = "Session ID from URL param '$session_id_from_url' not found, expired, or fingerprint mismatch.";
            } else { 
                $session_id_to_use = $session_id_from_url;
                $generate_new_session = false;
                $session_source = 'url_param';
                $log_reason = "Session found via URL parameter.";
            }
        } elseif ($session_id_from_url) {
            $log_reason = "Invalid Session ID format in URL parameter: '$session_id_from_url'.";
        }
    }

    
    
    if ($session_id_to_use === null) {
        if (empty($log_reason)) {
             $log_reason = "No valid session ID provided via header or URL.";
        }
        try {
            $stmt_find = $pdo_for_session->prepare(
                "SELECT session_id FROM sessions
                 WHERE user_fingerprint = :fingerprint AND expires_at > datetime('now', 'localtime')
                 ORDER BY last_access DESC LIMIT 1"
            );
            $stmt_find->execute([':fingerprint' => $current_user_fingerprint]);
            $existing_session_id = $stmt_find->fetchColumn();

            if ($existing_session_id) {
                $session_id_to_use = $existing_session_id;
                $generate_new_session = false;
                $session_source = 'fingerprint';
                $log_reason .= " Reusing session found via fingerprint match.";
            } else {
                $log_reason .= " No existing session found for current fingerprint.";
            }
        } catch (PDOException $e) {
            
            $log_reason .= " DB error during fingerprint lookup.";
            
        }
    }

    
    if ($generate_new_session) {
        $session_id_to_use = generate_session_id(); 
        $session_source = 'new';
        $log_reason .= " Generating new session ID.";
    }

    
    

    
    if (session_status() !== PHP_SESSION_ACTIVE) {
        session_id($session_id_to_use);
    } else {
        
    }

    
    
    $session_started = (session_status() === PHP_SESSION_ACTIVE) || session_start(['read_and_close' => false]);

    if (!$session_started) {
        
        
        
        $_SESSION = []; 
        $fallback_id = generate_session_id();
        session_id($fallback_id);
        
        die("Erro crítico: Falha fatal ao iniciar a sessão. Tente novamente mais tarde ou contacte o suporte.");
    }

    
    if (!isset($_SESSION['cart']) || !is_array($_SESSION['cart'])) {
        
        if (!$generate_new_session && $session_source !== 'new') {
        } else {
        }
        $_SESSION['cart'] = [];
    } else {
        
        $i = 0;
        foreach ($_SESSION['cart'] as $key => $item) {
            if ($i < 3) { 

                $i++;
            } else {
                break;
            }
        }
    }
    

    
    if (!is_array($_SESSION['cart'])) {
        $_SESSION['cart'] = []; 
    }

    
    
    return $session_id_to_use;
}

function get_all_sessions_data(): array
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return ['success' => false, 'message' => 'Database connection failed', 'sessions' => []];
    }

    try {
        $stmt = $pdo->query(
            "SELECT session_id, data, created_at, last_access, expires_at
             FROM sessions
             ORDER BY last_access DESC"
        );
        $sessions_raw = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $sessions_data = [];

        foreach ($sessions_raw as $session) {
            $cart_contents = [];
            if (!empty($session['data'])) {
                // More robust attempt to find and unserialize cart data.
                // Assumes standard PHP session serialization format: key|serialized_value;
                $temp_session_data = $session['data'];
                $cart_data_found = false;

                // Iterate over session variables
                $offset = 0;
                while ($offset < strlen($temp_session_data)) {
                    $key_end_pos = strpos($temp_session_data, '|', $offset);
                    if ($key_end_pos === false) break; // No more variables or malformed

                    $key = substr($temp_session_data, $offset, $key_end_pos - $offset);
                    $offset = $key_end_pos + 1;
                    
                    if ($key === 'cart') {
                        // Value starts after "|"
                        // We need to find the end of this specific serialized value.
                        $value_str = "";
                        $brace_count = 0;
                        $in_string = false;
                        $len = strlen($temp_session_data);
                        $value_start_offset = $offset;
                        $found_value_end = false;

                        for ($i = $offset; $i < $len; $i++) {
                            $char = $temp_session_data[$i];
                            
                            if ($char === '"') $in_string = !$in_string;
                            
                            if (!$in_string) {
                                if ($char === '{' || $char === '(') $brace_count++;
                                else if ($char === '}' || $char === ')') $brace_count--;
                            }
                            
                            if ($char === ';' && $brace_count <= 0 && !$in_string) { // Found end of serialized item
                                $value_str = substr($temp_session_data, $value_start_offset, $i - $value_start_offset);
                                $offset = $i + 1;
                                $found_value_end = true;
                                break;
                            }
                        }
                        if (!$found_value_end) { // If we reached end of data without a clear semicolon delimiter for the cart
                            $value_str = substr($temp_session_data, $value_start_offset);
                            $offset = $len;
                        }
                        
                        $cart_array = @unserialize($value_str);

                        if (is_array($cart_array)) {
                            foreach ($cart_array as $item_key => $cart_item) {
                                if (is_array($cart_item)) {
                                    $product_name = $cart_item['name'] ?? "Produto (Chave: $item_key)";
                                    $quantity = $cart_item['quantity'] ?? '?';
                                    $cart_contents[] = "$product_name (Qty: $quantity)";
                                } else {
                                    $cart_contents[] = "Item carrinho (Chave: $item_key, Formato Desconhecido)";
                                }
                            }
                            $cart_data_found = true;
                        }
                        break; // Found 'cart' key (or attempted to parse it), stop parsing for summary
                    } else {
                        // Skip other variables by finding the next semicolon that marks the end of this variable's value
                        $current_var_offset = $offset;
                        $semicolon_pos = -1;
                        $level = 0;
                        $in_str = false;
                        for ($k = $current_var_offset; $k < strlen($temp_session_data); $k++) {
                            $ch = $temp_session_data[$k];
                            if ($ch === '"') $in_str = !$in_str;
                            if (!$in_str) {
                                if ($ch === '{' || $ch === '(') $level++;
                                else if ($ch === '}' || $ch === ')') $level--;
                                else if ($ch === ';' && $level === 0) {
                                    $semicolon_pos = $k;
                                    break;
                                }
                            }
                        }

                        if ($semicolon_pos === false || $semicolon_pos === -1) break; // Malformed or end of data
                        $offset = $semicolon_pos + 1;
                    }
                }
            }

            $sessions_data[] = [
                'session_id' => $session['session_id'],
                'created_at' => $session['created_at'],
                'last_access' => $session['last_access'],
                'expires_at' => $session['expires_at'],
                'cart_items_list' => $cart_contents // Pass the array of item strings
            ];
        }

        return ['success' => true, 'sessions' => $sessions_data];
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'Error fetching sessions: ' . $e->getMessage(), 'sessions' => []];
    }
}

function delete_session_and_associated_tokens(string $session_id): array
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return ['success' => false, 'message' => 'Database connection failed'];
    }

    $log_del_path = __DIR__ . '/../logs/session_delete_debug.log';
    $log_del_msg = "Timestamp: " . date('Y-m-d H:i:s') . " - delete_session_and_associated_tokens for session_id: " . $session_id . "\n";
    file_put_contents($log_del_path, $log_del_msg, FILE_APPEND);


    try {
        $pdo->beginTransaction();

        // 1. Delete associated download tokens
        // Assuming download_tokens has a session_id column
        // If not, this part needs to be adjusted or removed if tokens are not directly linked to sessions
        $stmt_download_tokens = $pdo->prepare("DELETE FROM download_tokens WHERE session_id = :session_id");
        $stmt_download_tokens->bindParam(':session_id', $session_id, PDO::PARAM_STR);
        $stmt_download_tokens->execute();
        $deleted_download_tokens = (int)$stmt_download_tokens->rowCount();
        file_put_contents($log_del_path, "Deleted download tokens: " . $deleted_download_tokens . "\n", FILE_APPEND);

        // 2. Delete the session itself
        $stmt_session = $pdo->prepare("DELETE FROM sessions WHERE session_id = :session_id");
        $stmt_session->bindParam(':session_id', $session_id, PDO::PARAM_STR);
        $stmt_session->execute();
        $deleted_session = (int)$stmt_session->rowCount();
        file_put_contents($log_del_path, "Deleted sessions: " . $deleted_session . "\n", FILE_APPEND);

        if ($deleted_session === 0) {
            $pdo->rollBack(); // Rollback if session was not found/deleted
            file_put_contents($log_del_path, "Session not found or already deleted. Rolled back.\n---\n", FILE_APPEND);
            return ['success' => false, 'message' => 'Session not found or already deleted.'];
        }

        $pdo->commit();
        file_put_contents($log_del_path, "Committed successfully.\n", FILE_APPEND);


        $message = "Session '$session_id' deleted successfully.";
        if ($deleted_download_tokens > 0) {
            $message .= " $deleted_download_tokens download token(s) associated with this session were also deleted.";
        }

        return ['success' => true, 'message' => $message];
    } catch (PDOException $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
            file_put_contents($log_del_path, "PDOException, Rolled back: " . $e->getMessage() . "\n---\n", FILE_APPEND);
        }
        file_put_contents($log_del_path, "PDOException: " . $e->getMessage() . "\n---\n", FILE_APPEND);
        return ['success' => false, 'message' => 'Error deleting session: ' . $e->getMessage()];
    }
    file_put_contents($log_del_path, "---\n", FILE_APPEND); // End of function log
}
function get_session_id_param(bool $include_equals = true): string {
    $current_session_id = session_id(); 
    if (empty($current_session_id)) {
         
         return ''; 
    }
    
    $param_name = defined('SESSION_PARAM_NAME') ? SESSION_PARAM_NAME : 'sid';
    return $param_name . ($include_equals ? '=' . urlencode($current_session_id) : '');
}

?>
