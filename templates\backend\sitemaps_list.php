<?php

if (!defined('ADMIN_CONTEXT') || !ADMIN_CONTEXT) {
    die('Access Denied');
}

$sitemaps = get_sitemap_configs();

display_flash_messages();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Sitemaps e XMLs para Motores de Busca</h1>
    <div>
        <?php if (!empty($sitemaps)): ?>
            <a href="admin.php?section=sitemaps&action=generate_all&<?= get_session_id_param() ?>" class="btn btn-success me-2">
                <i class="bi bi-arrow-repeat"></i> Gerar Todos Ativos
            </a>
        <?php endif; ?>
        <a href="admin.php?section=sitemaps&action=new&<?= get_session_id_param() ?>" class="btn btn-primary" id="new-sitemap-btn">
            <i class="bi bi-plus-circle"></i> Novo Sitemap/XML
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <i class="bi bi-diagram-3"></i> Configurações de Sitemaps e XMLs
    </div>
    <div class="card-body">
        <?php if (empty($sitemaps)): ?>
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i> Não existem configurações de sitemaps ou XMLs. Clique no botão "Novo Sitemap/XML" para criar uma.
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Nome</th>
                            <th>Tipo</th>
                            <th>Caminho de Saída</th>
                            <th>Última Geração</th>
                            <th>Estado</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($sitemaps as $sitemap): ?>
                            <tr>
                                <td><?= sanitize_input($sitemap['name']) ?></td>
                                <td>
                                    <?php
                                    switch ($sitemap['type']) {
                                        case 'sitemap':
                                            echo '<span class="badge bg-primary">Sitemap</span>';
                                            break;
                                        case 'google_merchant':
                                            echo '<span class="badge bg-success">Google Merchant</span>';
                                            break;
                                        case 'custom':
                                            echo '<span class="badge bg-info">XML Personalizado</span>';
                                            break;
                                        default:
                                            echo '<span class="badge bg-secondary">Desconhecido</span>';
                                    }
                                    ?>
                                </td>
                                <td><?= sanitize_input($sitemap['output_path']) ?></td>
                                <td>
                                    <?php if (!empty($sitemap['last_generated'])): ?>
                                        <?= date('d/m/Y H:i:s', strtotime($sitemap['last_generated'])) ?>
                                    <?php else: ?>
                                        <span class="text-muted">Nunca</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($sitemap['is_active']): ?>
                                        <span class="badge bg-success">Ativo</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Inativo</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="admin.php?section=sitemaps&action=edit&id=<?= $sitemap['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-outline-primary" title="Editar">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a href="admin.php?section=sitemaps&action=generate&id=<?= $sitemap['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-outline-success" title="Gerar">
                                            <i class="bi bi-arrow-repeat"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-info copy-sitemap-url-btn" data-path="<?= htmlspecialchars($sitemap['output_path']) ?>" title="Copiar URL">
                                            <i class="bi bi-clipboard"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger delete-sitemap-btn" data-id="<?= $sitemap['id'] ?>" title="Eliminar">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header">
        <i class="bi bi-info-circle"></i> Informação
    </div>
    <div class="card-body">
        <h5>Tipos de Sitemaps/XMLs</h5>
        <ul>
            <li><strong>Sitemap:</strong> Gera um ficheiro sitemap.xml padrão para motores de busca como Google, Bing, etc.</li>
            <li><strong>Google Merchant:</strong> Gera um feed XML para o Google Merchant Center.</li>
            <li><strong>XML Personalizado:</strong> Permite configurar um formato XML personalizado.</li>
        </ul>

        <h5>Caminhos de Saída</h5>
        <p>O caminho de saída pode ser absoluto (ex: <code>C:\xampp\htdocs\sitemap.xml</code>) ou relativo ao diretório raiz do projeto (ex: <code>sitemap.xml</code>).</p>

        <h5>Conteúdo</h5>
        <p>Pode escolher quais os tipos de conteúdo a incluir em cada sitemap/XML:</p>
        <ul>
            <li><strong>Produtos:</strong> Inclui todos os produtos ativos.</li>
            <li><strong>Blog:</strong> Inclui posts e categorias do blog.</li>
            <li><strong>Páginas:</strong> Inclui páginas estáticas.</li>
        </ul>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize delete buttons
    const deleteButtons = document.querySelectorAll('.delete-sitemap-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const sitemapId = this.getAttribute('data-id');
            if (confirm('Tem a certeza que deseja eliminar esta configuração de sitemap/XML?')) {
                window.location.href = `admin.php?section=sitemaps&action=delete&id=${sitemapId}&<?= get_session_id_param() ?>`;
            }
        });
    });

    // Initialize copy URL buttons
    const copyButtons = document.querySelectorAll('.copy-sitemap-url-btn');
    copyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const outputPath = this.getAttribute('data-path');
            const sitemapUrl = getSitemapUrl(outputPath);

            // Copy to clipboard
            navigator.clipboard.writeText(sitemapUrl)
                .then(() => {
                    // Show success feedback
                    const originalTitle = this.getAttribute('title');
                    const originalIcon = this.innerHTML;

                    this.setAttribute('title', 'URL Copiado!');
                    this.innerHTML = '<i class="bi bi-check-lg"></i>';
                    this.classList.remove('btn-outline-info');
                    this.classList.add('btn-success');

                    // Reset after 2 seconds
                    setTimeout(() => {
                        this.setAttribute('title', originalTitle);
                        this.innerHTML = originalIcon;
                        this.classList.remove('btn-success');
                        this.classList.add('btn-outline-info');
                    }, 2000);
                })
                .catch(err => {
                    console.error('Erro ao copiar URL: ', err);
                    alert('Não foi possível copiar o URL. Erro: ' + err);
                });
        });
    });

    // Function to get the sitemap URL based on its output path
    function getSitemapUrl(outputPath) {
        // Get the site URL (protocol + domain + base path)
        const protocol = window.location.protocol;
        const host = window.location.host;
        const basePath = window.location.pathname.split('/admin.php')[0];
        const siteUrl = `${protocol}//${host}${basePath}/`;

        // If the output path is absolute, extract just the filename
        if (outputPath.match(/^[A-Z]:\\\\|^\//)) {
            const filename = outputPath.split(/[\/\\]/).pop();
            return siteUrl + filename;
        } else {
            // If the path is relative, just append to site URL
            return siteUrl + outputPath.replace(/^[\/\\]/, '');
        }
    }

    // Debug session and CSRF token

    // Add event listener to the "Novo Sitemap/XML" button to ensure proper navigation
    const newSitemapBtn = document.getElementById('new-sitemap-btn');
    if (newSitemapBtn) {
        newSitemapBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const url = this.getAttribute('href');
            // Ensure the session ID is in the URL
            let finalUrl = url;
            if (!finalUrl.includes('sid=')) {
                const sessionId = '<?= session_id() ?>';
                if (sessionId) {
                    finalUrl = finalUrl + (finalUrl.includes('?') ? '&' : '?') + 'sid=' + sessionId;
                }
            }
            window.location.href = finalUrl;
        });
    }
});
</script>
