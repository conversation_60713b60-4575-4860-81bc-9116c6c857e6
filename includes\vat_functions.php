<?php
function ensure_vat_rates_table_exists() {
    $pdo = get_db_connection();
    $table_exists = false;

    try {
        $result = $pdo->query("SELECT 1 FROM vat_rates LIMIT 1");
        $table_exists = true;
    } catch (PDOException $e) {
        $table_exists = false;
    }

    if (!$table_exists) {
        $pdo->exec("CREATE TABLE vat_rates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            rate REAL NOT NULL,
            description TEXT NOT NULL,
            is_default INTEGER NOT NULL DEFAULT 0,
            is_active INTEGER NOT NULL DEFAULT 1,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime'))
        );");

        $pdo->exec("CREATE INDEX idx_vat_rates_default ON vat_rates (is_default);");
        $pdo->exec("CREATE INDEX idx_vat_rates_is_active ON vat_rates (is_active);");

        $pdo->exec("INSERT INTO vat_rates (rate, description, is_default) VALUES
            (23.0, 'IVA Normal (23%)', 1);");
    }
}

function get_vat_rates($active_only = true) {
    
    
    $sql = "SELECT * FROM vat_rates";

    
    
    if ($active_only) {
        $sql .= " WHERE is_active = 1";
    }

    $sql .= " ORDER BY is_default DESC, rate ASC";

    return db_query($sql, [], false, true);
}

function get_vat_rate($id) {
    if (!$id || $id <= 0) {
        return false;
    }

    $sql = "SELECT * FROM vat_rates WHERE id = :id";
    return db_query($sql, [':id' => $id], true);
}

function get_default_vat_rate() {
    
    $sql = "SELECT * FROM vat_rates WHERE is_default = 1 LIMIT 1";
    $default_rate = db_query($sql, [], true);

    if (!$default_rate) {
        
        $sql = "SELECT * FROM vat_rates ORDER BY id ASC LIMIT 1";
        $default_rate = db_query($sql, [], true);
    }

    return $default_rate;
}

function create_vat_rate($rate, $description = null, $is_default = false) {
    $pdo = get_db_connection();
    if (!$pdo) {
        return ['success' => false, 'message' => 'Database connection error.'];
    }

    $rate_value = 0; 
    $is_default_flag = false; 

    
    if (is_array($rate)) {
        $data = $rate;
        $rate_value = filter_var($data['rate'] ?? 0, FILTER_VALIDATE_FLOAT);
        $description = trim($data['description'] ?? '');
        $is_default_flag = isset($data['is_default']) ? filter_var($data['is_default'], FILTER_VALIDATE_BOOLEAN) : false;
    } else {
        $rate_value = filter_var($rate, FILTER_VALIDATE_FLOAT);
        $description = trim($description ?? '');
        $is_default_flag = filter_var($is_default, FILTER_VALIDATE_BOOLEAN);
    }

    
    if ($rate_value === false || $rate_value < 0 || $rate_value > 100) {
        return ['success' => false, 'message' => 'Invalid VAT rate value. Must be between 0 and 100.'];
    }
    if (empty($description)) {
        return ['success' => false, 'message' => 'Description cannot be empty.'];
    }
     
     $stmt_check = $pdo->prepare("SELECT id FROM vat_rates WHERE (rate = :rate OR description = :description)");
     $stmt_check->execute([':rate' => $rate_value, ':description' => $description]);
     if ($stmt_check->fetch()) {
         return ['success' => false, 'message' => 'A VAT rate with this rate or description already exists.'];
     }

    try {
        $pdo->beginTransaction();

        
        if ($is_default_flag) {
            $stmt_unset = $pdo->prepare("UPDATE vat_rates SET is_default = 0 WHERE is_default = 1");
            $stmt_unset->execute();
        }

        $stmt_insert = $pdo->prepare("INSERT INTO vat_rates (rate, description, is_default, created_at, updated_at)
                               VALUES (:rate, :description, :is_default, datetime('now', 'localtime'), datetime('now', 'localtime'))");
        $stmt_insert->execute([
            ':rate' => $rate_value,
            ':description' => $description,
            ':is_default' => $is_default_flag ? 1 : 0
        ]);

        $vat_rate_id = $pdo->lastInsertId();
        $pdo->commit(); 

        if ($vat_rate_id) {
             
             $new_rate = get_vat_rate($vat_rate_id); 
            return ['success' => true, 'message' => 'VAT rate created successfully.', 'vat_rate_id' => $vat_rate_id, 'vat_rate' => $new_rate];
        } else {
            
            return ['success' => false, 'message' => 'Failed to create VAT rate (no ID returned).'];
        }

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return ['success' => false, 'message' => 'Database error during creation.', 'error' => $e->getMessage()];
    }
}

function update_vat_rate($id, $data) {
     if (!$id || !is_numeric($id) || $id <= 0) {
        return ['success' => false, 'message' => 'Invalid VAT Rate ID.'];
    }
     if (!is_array($data)) {
         return ['success' => false, 'message' => 'Invalid data format for update.'];
     }

    $pdo = get_db_connection();
    if (!$pdo) {
         return ['success' => false, 'message' => 'Database connection error.'];
    }

    
    $current_rate_data = get_vat_rate($id);
    if (!$current_rate_data) {
        return ['success' => false, 'message' => 'VAT rate not found.'];
    }
     
     
     if (!$current_rate_data['is_active']) {
          
          
     }

    
    $rate = isset($data['rate']) ? filter_var($data['rate'], FILTER_VALIDATE_FLOAT) : $current_rate_data['rate'];
    $description = isset($data['description']) ? trim($data['description']) : $current_rate_data['description'];
    $is_default = isset($data['is_default']) ? filter_var($data['is_default'], FILTER_VALIDATE_BOOLEAN) : (bool)$current_rate_data['is_default'];

    if ($rate === false || $rate < 0 || $rate > 100) {
        return ['success' => false, 'message' => 'Invalid VAT rate value. Must be between 0 and 100.'];
    }
    if (empty($description)) {
         return ['success' => false, 'message' => 'Description cannot be empty.'];
    }

    
    $stmt_check = $pdo->prepare("SELECT id FROM vat_rates WHERE (rate = :rate OR description = :description) AND id != :id");
    $stmt_check->execute([':rate' => $rate, ':description' => $description, ':id' => $id]);
    if ($stmt_check->fetch()) {
        return ['success' => false, 'message' => 'Another VAT rate with this rate or description already exists.'];
    }

    try {
        $pdo->beginTransaction();

        
        if ($is_default && !$current_rate_data['is_default']) {
            
            $stmt_unset = $pdo->prepare("UPDATE vat_rates SET is_default = 0 WHERE is_default = 1 AND id != :id");
            $stmt_unset->execute([':id' => $id]);
        } elseif (!$is_default && $current_rate_data['is_default']) {
            
             $stmt_check_count = $pdo->prepare("SELECT COUNT(*) FROM vat_rates WHERE id != :id");
             $stmt_check_count->execute([':id' => $id]);
             if ($stmt_check_count->fetchColumn() == 0) {
                 $pdo->rollBack();
                 return ['success' => false, 'message' => 'Cannot unset the only default VAT rate. Add another rate and set it as default first.'];
             }
             
        }

        $stmt_update = $pdo->prepare("UPDATE vat_rates
                              SET rate = :rate,
                                  description = :description,
                                  is_default = :is_default,
                                  updated_at = datetime('now', 'localtime')
                              WHERE id = :id");
        $success = $stmt_update->execute([
            ':rate' => $rate,
            ':description' => $description,
            ':is_default' => $is_default ? 1 : 0,
            ':id' => $id
        ]);

        $pdo->commit(); 

        if ($success) {
             
             $updated_rate = get_vat_rate($id);
             if ($stmt_update->rowCount() > 0) {
                 return ['success' => true, 'message' => 'VAT rate updated successfully.', 'vat_rate' => $updated_rate];
             } else {
                  
                  return ['success' => true, 'message' => 'No changes detected.', 'vat_rate' => $updated_rate];
             }
        } else {
             
            return ['success' => false, 'message' => 'Failed to execute VAT rate update.'];
        }

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return ['success' => false, 'message' => 'Database error during update.', 'error' => $e->getMessage()];
    }
}

function delete_vat_rate($id) {
    if (!$id || !is_numeric($id) || $id <= 0) {
        return ['success' => false, 'message' => 'Invalid VAT Rate ID.'];
    }

    $pdo = get_db_connection();
    if (!$pdo) {
        return ['success' => false, 'message' => 'Database connection error.'];
    }

    try {
        
        $stmt_check_exists = $pdo->prepare("SELECT is_default FROM vat_rates WHERE id = :id");
        $stmt_check_exists->execute([':id' => $id]);
        $rate_info = $stmt_check_exists->fetch(PDO::FETCH_ASSOC);

        if (!$rate_info) {
             return ['success' => false, 'message' => 'VAT rate not found.'];
        }

        
        
        $stmt_check_usage = $pdo->prepare("SELECT COUNT(*) FROM products WHERE vat_rate_id = :id");
        $stmt_check_usage->execute([':id' => $id]);
        $usage_count = $stmt_check_usage->fetchColumn();

        if ($usage_count > 0) {
            return ['success' => false, 'message' => 'Cannot delete VAT rate: It is currently assigned to ' . $usage_count . ' product(s). Please reassign products first.'];
        }

        
        if ($rate_info['is_default']) {
            
            $stmt_check_count = $pdo->prepare("SELECT COUNT(*) FROM vat_rates");
            $stmt_check_count->execute();
            $total_count = $stmt_check_count->fetchColumn();

            if ($total_count <= 1) {
                return ['success' => false, 'message' => 'Cannot delete the only VAT rate. Add another rate and set it as default first.'];
            }
            
            return ['success' => false, 'message' => 'Cannot delete the default VAT rate. Please set another rate as default first.'];
        }

        
        $stmt_delete = $pdo->prepare("DELETE FROM vat_rates WHERE id = :id");
        $success = $stmt_delete->execute([':id' => $id]);

        if ($success && $stmt_delete->rowCount() > 0) {
            return ['success' => true, 'message' => 'VAT rate deleted successfully.'];
        } elseif ($success) {
             
             return ['success' => false, 'message' => 'VAT rate not found.'];
        } else {
             return ['success' => false, 'message' => 'Failed to delete VAT rate.'];
        }

    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'Database error during deletion.', 'error' => $e->getMessage()];
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'An unexpected error occurred during deletion.', 'error' => $e->getMessage()];
    }
}

function set_default_vat_rate($id) {
     if (!$id || !is_numeric($id) || $id <= 0) {
        return ['success' => false, 'message' => 'Invalid VAT Rate ID.'];
    }

    $pdo = get_db_connection();
    if (!$pdo) {
        return ['success' => false, 'message' => 'Database connection error.'];
    }

    try {
        $pdo->beginTransaction();

        
        $stmt_check = $pdo->prepare("SELECT id, is_default FROM vat_rates WHERE id = :id");
        $stmt_check->execute([':id' => $id]);
        $rate_info = $stmt_check->fetch(PDO::FETCH_ASSOC);

        if (!$rate_info) {
            $pdo->rollBack();
            return ['success' => false, 'message' => 'VAT rate not found.'];
        }

        
        if ($rate_info['is_default']) {
             $pdo->commit(); 
             return ['success' => true, 'message' => 'This rate is already the default.'];
        }

        
        $stmt_unset = $pdo->prepare("UPDATE vat_rates SET is_default = 0 WHERE is_default = 1");
        $stmt_unset->execute(); 

        
        $stmt_set = $pdo->prepare("UPDATE vat_rates SET is_default = 1, updated_at = datetime('now', 'localtime') WHERE id = :id");
        $success = $stmt_set->execute([':id' => $id]);

        $pdo->commit();

        if ($success && $stmt_set->rowCount() > 0) {
             return ['success' => true, 'message' => 'Default VAT rate updated successfully.'];
        } elseif ($success) {
             
             return ['success' => true, 'message' => 'Default VAT rate set (no change detected).'];
        } else {
             
             return ['success' => false, 'message' => 'Failed to set default VAT rate.'];
        }

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return ['success' => false, 'message' => 'Database error while setting default rate.', 'error' => $e->getMessage()];
    }
}

function get_product_vat_rate($product_id) {
    if (!$product_id || $product_id <= 0) {
        return false;
    }

    $sql = "SELECT vat_rate_id FROM products WHERE id = :product_id";
    $product = db_query($sql, [':product_id' => $product_id], true);

    if (!$product || !$product['vat_rate_id']) {
        return get_default_vat_rate();
    }

    return get_vat_rate($product['vat_rate_id']);
}

function vat_get_all_rates($active_only = true) {
    return get_vat_rates($active_only);
}

function vat_get_rate($id) {
    return get_vat_rate($id);
}

function vat_get_default_rate() {
    return get_default_vat_rate();
}

function vat_create_rate($rate, $description = null, $is_default = false) {
    
    if (is_array($rate)) {
        return create_vat_rate($rate);
    } else {
        return create_vat_rate($rate, $description, $is_default);
    }
}

function vat_update_rate($id, $rate_or_data, $description = null, $is_default = false) {
    if (is_array($rate_or_data)) {
        
        return update_vat_rate($id, $rate_or_data);
    } else {
        
        $data = [
            'rate' => $rate_or_data,
            'description' => $description,
            'is_default' => $is_default
        ];
        return update_vat_rate($id, $data);
    }
}

function vat_set_default_rate($id) {
    return set_default_vat_rate($id);
}

function vat_delete_rate($id) {
    return delete_vat_rate($id);
}

function vat_get_product_rate($product_id) {
    return get_product_vat_rate($product_id);
}
