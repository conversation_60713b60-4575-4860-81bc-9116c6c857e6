<?php
// This is a dedicated endpoint for getting file type associations
// It avoids any potential output buffering or other issues that might be occurring in admin.php

// Ensure no output has been sent
if (ob_get_level()) ob_end_clean();

// Set proper content type
header('Content-Type: application/json');

// Include necessary files
require_once __DIR__ . '/includes/init.php';
require_once __DIR__ . '/includes/digital_files_functions.php';

// Check if user is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Não autorizado']);
    exit;
}

// Validate CSRF token
if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
    echo json_encode(['success' => false, 'message' => 'Token CSRF inválido']);
    exit;
}

// Get file ID from request
$file_id = isset($_POST['file_id']) ? (int)$_POST['file_id'] : 0;
if ($file_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'ID de arquivo inválido']);
    exit;
}

try {
    // Get file type IDs directly from the digital_file_type_associations table
    $file_type_ids = get_digital_file_file_type_ids($file_id);

    // Ensure $file_type_ids is an array
    if (!is_array($file_type_ids)) {
        error_log("Direct endpoint - Warning: file_type_ids is not an array for file $file_id, converting to empty array");
        $file_type_ids = [];
    }

    // Log the result for debugging
    error_log("Direct endpoint - File type IDs for file $file_id: " . json_encode($file_type_ids));
    error_log("Direct endpoint - Number of file type associations: " . count($file_type_ids));

    // Send JSON response
    echo json_encode([
        'success' => true,
        'message' => 'Tipos de arquivo obtidos com sucesso',
        'file_type_ids' => $file_type_ids
    ]);
} catch (Exception $e) {
    error_log("Direct endpoint - Error getting file type associations: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao obter tipos de arquivo: ' . $e->getMessage(),
        'file_type_ids' => [] // Always include an empty array even on error
    ]);
}
