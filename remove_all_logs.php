<?php
// <PERSON>ript to remove all error_log and console.log statements from specified files

// Files to process
$files = [
    'includes/functions.php',
    'includes/db.php',
    'includes/ajax_handler.php',
    'includes/admin_ajax_handler.php'
];

// Process each file
foreach ($files as $file) {
    echo "Processing file: $file\n";
    
    // Read the file
    $content = file_get_contents($file);
    if ($content === false) {
        echo "Error: Could not read file $file\n";
        continue;
    }
    
    // Count original error_log statements
    preg_match_all('/error_log\(.*?\);/', $content, $error_matches);
    $original_error_count = count($error_matches[0]);
    
    // Count original console.log statements
    preg_match_all('/console\.log\(.*?\);/', $content, $console_matches);
    $original_console_count = count($console_matches[0]);
    
    // Replace error_log statements
    $content = preg_replace('/\s*error_log\(.*?\);/', '', $content);
    
    // Replace console.log statements
    $content = preg_replace('/\s*console\.log\(.*?\);/', '', $content);
    
    // Write the file back
    if (file_put_contents($file, $content) === false) {
        echo "Error: Could not write to file $file\n";
        continue;
    }
    
    // Count remaining error_log statements
    preg_match_all('/error_log\(.*?\);/', $content, $error_matches);
    $remaining_error_count = count($error_matches[0]);
    
    // Count remaining console.log statements
    preg_match_all('/console\.log\(.*?\);/', $content, $console_matches);
    $remaining_console_count = count($console_matches[0]);
    
    echo "Removed " . ($original_error_count - $remaining_error_count) . " error_log statements from $file\n";
    echo "Removed " . ($original_console_count - $remaining_console_count) . " console.log statements from $file\n";
}

echo "All files processed.\n";
