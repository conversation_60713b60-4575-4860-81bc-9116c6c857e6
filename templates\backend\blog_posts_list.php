<?php

$title_filter = isset($_GET['title']) ? trim($_GET['title']) : '';
$category_filter = isset($_GET['category_id']) ? $_GET['category_id'] : '';
$type_filter = isset($_GET['post_type']) ? $_GET['post_type'] : '';
$status_filter = isset($_GET['is_published']) ? $_GET['is_published'] : '';

$current_page = isset($_GET['p']) ? max(1, (int)$_GET['p']) : 1;
$per_page = 20; 

$filters = [
    'page' => $current_page,
    'per_page' => $per_page,
    'order_by' => 'published_at',
    'order_dir' => 'DESC'
];

if (!empty($title_filter)) {
    $filters['search_term'] = $title_filter;
}
if (!empty($category_filter)) {
    $filters['category_id'] = $category_filter;
}
if (!empty($type_filter)) {
    $filters['post_type'] = $type_filter;
}
if ($status_filter !== '') {
    $filters['is_published'] = (int)$status_filter;
}

$result = get_blog_posts($filters);
$posts = $result['posts'] ?? [];
$total_posts = $result['total_count'] ?? 0;
$total_pages = ceil($total_posts / $per_page);

$categories = get_blog_categories();

?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Posts do Blog</h3>
                    <div class="d-flex">
                        <button class="btn btn-outline-primary btn-sm me-2" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
                            <i class="bi bi-funnel"></i> Filtros
                        </button>
                        <a href="admin.php?section=blog_posts&action=new&<?php echo get_session_id_param(); ?>" class="btn btn-success btn-sm">
                            <i class="bi bi-plus-circle"></i> Adicionar Novo Post
                        </a>
                    </div>
                </div>

                <div class="collapse mb-3 px-3 pt-3" id="filterCollapse">
                    <div class="card card-body">
                        <form method="get" action="admin.php" class="row g-3">
                            <input type="hidden" name="section" value="blog_posts">
                            <?php
                            
                            $session_param = get_session_id_param();
                            if (!empty($session_param)) {
                                list($name, $value) = explode('=', $session_param);
                                echo '<input type="hidden" name="' . $name . '" value="' . $value . '">';
                            }
                            ?>

                            <div class="col-md-3">
                                <label for="title" class="form-label">Título</label>
                                <input type="text" class="form-control" id="title" name="title" value="<?= htmlspecialchars($title_filter) ?>">
                            </div>

                            <div class="col-md-3">
                                <label for="category_id" class="form-label">Categoria</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="" <?= $category_filter === '' ? 'selected' : '' ?>>Todas</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?= $category['id'] ?>" <?= $category_filter == $category['id'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($category['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="col-md-2">
                                <label for="post_type" class="form-label">Tipo</label>
                                <select class="form-select" id="post_type" name="post_type">
                                    <option value="" <?= $type_filter === '' ? 'selected' : '' ?>>Todos</option>
                                    <option value="article" <?= $type_filter === 'article' ? 'selected' : '' ?>>Artigo</option>
                                    <option value="link" <?= $type_filter === 'link' ? 'selected' : '' ?>>Link Externo</option>
                                </select>
                            </div>

                            <div class="col-md-2">
                                <label for="is_published" class="form-label">Estado</label>
                                <select class="form-select" id="is_published" name="is_published">
                                    <option value="" <?= $status_filter === '' ? 'selected' : '' ?>>Todos</option>
                                    <option value="1" <?= $status_filter === '1' ? 'selected' : '' ?>>Publicado</option>
                                    <option value="0" <?= $status_filter === '0' ? 'selected' : '' ?>>Rascunho</option>
                                </select>
                            </div>

                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">Filtrar</button>
                                <a href="admin.php?section=blog_posts&<?= get_session_id_param() ?>" class="btn btn-outline-secondary">Limpar</a>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card-body">
                    <?php display_flash_messages(); ?>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0">Lista de Posts</h5>
                        <?php if (!empty($title_filter) || $category_filter !== '' || $type_filter !== '' || $status_filter !== ''): ?>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-info me-2">Filtros ativos</span>
                                <a href="admin.php?section=blog_posts&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-secondary">
                                    <i class="bi bi-x-circle"></i> Limpar filtros
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>

                    <?php if (!empty($title_filter) || $category_filter !== '' || $type_filter !== '' || $status_filter !== ''): ?>
                    <script>
                        // Auto-expand filter panel when filters are active
                        document.addEventListener('DOMContentLoaded', function() {
                            const filterCollapse = document.getElementById('filterCollapse');
                            if (filterCollapse) {
                                const bsCollapse = new bootstrap.Collapse(filterCollapse, {
                                    toggle: true
                                });
                            }
                        });
                    </script>
                    <?php endif; ?>

                    <?php if (empty($posts)): ?>
                        <div class="alert alert-info">Nenhum post encontrado.</div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Título</th>
                                        <th>Tipo</th>
                                        <th>Categorias</th>
                                        <th>Imagem</th>
                                        <th>Publicado</th>
                                        <th>Data Publicação</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($posts as $post): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($post['id']); ?></td>
                                            <td><?php echo htmlspecialchars($post['title']); ?></td>
                                            <td><?php echo ($post['post_type'] === 'link') ? 'Link Externo' : 'Artigo'; ?></td>
                                            <td>
                                                <?php
                                                $category_names = array_map(function($cat) {
                                                    return htmlspecialchars($cat['name']);
                                                }, $post['categories'] ?? []);
                                                echo !empty($category_names) ? implode(', ', $category_names) : 'N/A';
                                                ?>
                                            </td>
                                            <td>
                                                <?php if (!empty($post['image_path']) && file_exists(__DIR__ . '/../../' . $post['image_path'])): ?>
                                                    <img src="<?php echo htmlspecialchars(BASE_URL . "/" . $post['image_path']); ?>" alt="Thumbnail" style="max-height: 50px; max-width: 100px;">
                                                <?php else: ?>
                                                    <small>Sem Imagem</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($post['is_published']): ?>
                                                    <span class="badge bg-success">Sim</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">Não (Rascunho)</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo !empty($post['published_at']) ? format_datetime($post['published_at']) : 'N/A'; ?></td>
                                            <td>
                                                <a href="admin.php?section=blog_posts&action=edit&id=<?php echo $post['id']; ?>&<?php echo get_session_id_param(); ?>" class="btn btn-primary btn-sm" title="Editar">
                                                    <i class="bi bi-pencil-square"></i> Editar
                                                </a>
                                                <a href="admin.php?section=blog_posts&action=delete&id=<?php echo $post['id']; ?>&<?php echo get_session_id_param(); ?>"
                                                   class="btn btn-danger btn-sm delete-confirm"
                                                   data-confirm-message="Tem a certeza que deseja remover o post '<?php echo htmlspecialchars($post['title']); ?>'?"
                                                   title="Remover">
                                                    <i class="bi bi-trash"></i> Remover
                                                </a>
                                                <!-- Optional: Add View on Site button if published -->
                                                <?php if ($post['is_published']): ?>
                                                    <a href="<?php echo add_session_param_to_url(BASE_URL . '/index.php?view=blog_post&slug=' . $post['slug']); ?>" class="btn btn-info btn-sm" title="Ver no Site" target="_blank">
                                                        <i class="bi bi-eye"></i> Ver
                                                    </a>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                            <nav aria-label="Page navigation" class="mt-3">
                                <ul class="pagination justify-content-center">
                                    <!-- Previous Button -->
                                    <li class="page-item <?= ($current_page <= 1) ? 'disabled' : '' ?>">
                                        <a class="page-link" href="admin.php?section=blog_posts<?=
                                            (!empty($title_filter) ? '&title=' . urlencode($title_filter) : '') .
                                            ($category_filter !== '' ? '&category_id=' . $category_filter : '') .
                                            ($type_filter !== '' ? '&post_type=' . $type_filter : '') .
                                            ($status_filter !== '' ? '&is_published=' . $status_filter : '') .
                                            '&p=' . ($current_page - 1) .
                                            '&' . $session_param ?>" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>

                                    <?php
                                    
                                    $start_page = max(1, $current_page - 2);
                                    $end_page = min($total_pages, $current_page + 2);

                                    if ($start_page > 1) {
                                        echo '<li class="page-item"><a class="page-link" href="admin.php?section=blog_posts' .
                                            (!empty($title_filter) ? '&title=' . urlencode($title_filter) : '') .
                                            ($category_filter !== '' ? '&category_id=' . $category_filter : '') .
                                            ($type_filter !== '' ? '&post_type=' . $type_filter : '') .
                                            ($status_filter !== '' ? '&is_published=' . $status_filter : '') .
                                            '&p=1&' . $session_param . '">1</a></li>';
                                        if ($start_page > 2) {
                                            echo '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
                                        }
                                    }

                                    for ($i = $start_page; $i <= $end_page; $i++): ?>
                                        <li class="page-item <?= ($i == $current_page) ? 'active' : '' ?>">
                                            <a class="page-link" href="admin.php?section=blog_posts<?=
                                                (!empty($title_filter) ? '&title=' . urlencode($title_filter) : '') .
                                                ($category_filter !== '' ? '&category_id=' . $category_filter : '') .
                                                ($type_filter !== '' ? '&post_type=' . $type_filter : '') .
                                                ($status_filter !== '' ? '&is_published=' . $status_filter : '') .
                                                '&p=' . $i .
                                                '&' . $session_param ?>"><?= $i ?></a>
                                        </li>
                                    <?php endfor;

                                    if ($end_page < $total_pages) {
                                        if ($end_page < $total_pages - 1) {
                                            echo '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
                                        }
                                        echo '<li class="page-item"><a class="page-link" href="admin.php?section=blog_posts' .
                                            (!empty($title_filter) ? '&title=' . urlencode($title_filter) : '') .
                                            ($category_filter !== '' ? '&category_id=' . $category_filter : '') .
                                            ($type_filter !== '' ? '&post_type=' . $type_filter : '') .
                                            ($status_filter !== '' ? '&is_published=' . $status_filter : '') .
                                            '&p=' . $total_pages . '&' . $session_param . '">' . $total_pages . '</a></li>';
                                    }
                                    ?>

                                    <!-- Next Button -->
                                    <li class="page-item <?= ($current_page >= $total_pages) ? 'disabled' : '' ?>">
                                        <a class="page-link" href="admin.php?section=blog_posts<?=
                                            (!empty($title_filter) ? '&title=' . urlencode($title_filter) : '') .
                                            ($category_filter !== '' ? '&category_id=' . $category_filter : '') .
                                            ($type_filter !== '' ? '&post_type=' . $type_filter : '') .
                                            ($status_filter !== '' ? '&is_published=' . $status_filter : '') .
                                            '&p=' . ($current_page + 1) .
                                            '&' . $session_param ?>" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        <?php endif; ?>

                    <?php endif; ?>
                </div>
                <!-- /.card-body -->
            </div>
            <!-- /.card -->
        </div>
    </div>
</div>

<!-- Add Confirmation Modal specific JS if needed, or rely on a global admin.js -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const deleteButtons = document.querySelectorAll('.delete-confirm');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(event) {
            event.preventDefault(); // Prevent default link behavior
            const message = this.getAttribute('data-confirm-message') || 'Tem a certeza?';
            if (confirm(message)) {
                window.location.href = this.href; // Proceed with deletion
            }
        });
    });
});
</script>