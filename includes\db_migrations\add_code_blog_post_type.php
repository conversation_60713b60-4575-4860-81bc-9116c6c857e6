<?php

function migrate_add_code_blog_post_type(PDO $pdo) {
    try {
        $pdo->beginTransaction();

        // Step 1: Add the new column if it doesn't exist
        $columns = $pdo->query("PRAGMA table_info(blog_posts);")->fetchAll(PDO::FETCH_COLUMN, 1);
        if (!in_array('code_content', $columns)) {
            $pdo->exec("ALTER TABLE blog_posts ADD COLUMN code_content TEXT;");
            echo "Column 'code_content' added to 'blog_posts'.\n";
        } else {
            echo "Column 'code_content' already exists in 'blog_posts'.\n";
        }

        // Step 2: Recreate table to update CHECK constraint
        // (Assuming 'twitter_titleTEXT' was a typo for 'twitter_title TEXT')
        $pdo->exec("
            CREATE TABLE blog_posts_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                slug TEXT UNIQUE NOT NULL,
                post_type TEXT NOT NULL CHECK(post_type IN ('article', 'link', 'CODE')),
                content TEXT,
                link_url TEXT,
                link_description TEXT,
                image_path TEXT NOT NULL,
                is_published INTEGER NOT NULL DEFAULT 0,
                published_at TEXT,
                seo_title TEXT,
                seo_description TEXT,
                seo_keywords TEXT,
                og_title TEXT,
                og_description TEXT,
                og_image TEXT,
                twitter_card TEXT,
                twitter_title TEXT, -- Corrected from twitter_titleTEXT
                twitter_description TEXT,
                twitter_image TEXT,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                image_description TEXT,
                code_content TEXT
            );
        ");
        echo "Table 'blog_posts_new' created with updated CHECK constraint.\n";

        // Step 3: Copy data from old table to new table
        // Explicitly list columns to handle potential schema differences or future additions
        // and ensure 'code_content' from the altered old table is mapped.
        $pdo->exec("
            INSERT INTO blog_posts_new (
                id, title, slug, post_type, content, link_url, link_description,
                image_path, is_published, published_at, seo_title, seo_description,
                seo_keywords, og_title, og_description, og_image, twitter_card,
                twitter_title, twitter_description, twitter_image, created_at,
                updated_at, image_description, code_content
            )
            SELECT
                id, title, slug, post_type, content, link_url, link_description,
                image_path, is_published, published_at, seo_title, seo_description,
                seo_keywords, og_title, og_description, og_image, twitter_card,
                twitter_title, -- Corrected from twitter_titleTEXT
                twitter_description, twitter_image, created_at,
                updated_at, image_description, code_content
            FROM blog_posts;
        ");
        echo "Data copied from 'blog_posts' to 'blog_posts_new'.\n";

        // Step 4: Drop the old table
        $pdo->exec("DROP TABLE blog_posts;");
        echo "Old table 'blog_posts' dropped.\n";

        // Step 5: Rename the new table
        $pdo->exec("ALTER TABLE blog_posts_new RENAME TO blog_posts;");
        echo "Table 'blog_posts_new' renamed to 'blog_posts'.\n";

        // Step 6: Recreate indexes
        $pdo->exec("CREATE INDEX idx_blog_posts_slug ON blog_posts (slug);");
        $pdo->exec("CREATE INDEX idx_blog_posts_published ON blog_posts (is_published, published_at);");
        $pdo->exec("CREATE INDEX idx_blog_posts_type ON blog_posts (post_type);");
        echo "Indexes recreated for 'blog_posts'.\n";

        $pdo->commit();
        echo "Migration 'add_code_blog_post_type' completed successfully.\n";
        return true;

    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("Migration 'add_code_blog_post_type' failed: " . $e->getMessage());
        echo "Migration 'add_code_blog_post_type' failed: " . $e->getMessage() . "\n";
        return false;
    }
}

// Optional: Add a function to check if this migration has already run,
// for instance, by checking the schema.
function has_code_blog_post_type_migrated(PDO $pdo) {
    try {
        $stmt = $pdo->query("PRAGMA table_info(blog_posts);");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $has_code_content = false;
        $post_type_check_is_updated = false;

        foreach ($columns as $column) {
            if ($column['name'] === 'code_content') {
                $has_code_content = true;
            }
        }

        // Check the CHECK constraint. This is a bit tricky as PRAGMA table_info doesn't directly show it.
        // We can infer by trying to insert a 'CODE' type. A more robust way is to check the sqlite_master table.
        $stmt = $pdo->query("SELECT sql FROM sqlite_master WHERE type='table' AND name='blog_posts';");
        $table_sql = $stmt->fetchColumn();
        if ($table_sql && strpos($table_sql, "CHECK(post_type IN ('article', 'link', 'CODE'))") !== false) {
            $post_type_check_is_updated = true;
        }

        return $has_code_content && $post_type_check_is_updated;
    } catch (PDOException $e) {
        error_log("Error checking migration status for 'add_code_blog_post_type': " . $e->getMessage());
        return false; // Assume not migrated if there's an error
    }
}

?>