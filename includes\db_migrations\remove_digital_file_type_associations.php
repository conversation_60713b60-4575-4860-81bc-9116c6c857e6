<?php

/**
 * Migration to remove the redundant digital_file_type_associations table
 * We'll use only the digital_product_file_type_associations table going forward
 */
function migrate_remove_digital_file_type_associations()
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        // Check if the table exists
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='digital_file_type_associations';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {
            // Table doesn't exist, migration already applied
            error_log("Migration already applied: digital_file_type_associations table doesn't exist");
            return true;
        }

        // Start transaction
        $pdo->beginTransaction();

        // Drop the table
        $pdo->exec("DROP TABLE IF EXISTS digital_file_type_associations;");

        // Drop related indexes
        $pdo->exec("DROP INDEX IF EXISTS idx_digital_file_type_associations_file_id;");
        $pdo->exec("DROP INDEX IF EXISTS idx_digital_file_type_associations_type_id;");

        // Record the migration
        $stmt = $pdo->prepare("INSERT INTO migrations (name, executed_at) VALUES (:name, datetime('now', 'localtime'))");
        $stmt->execute([':name' => 'remove_digital_file_type_associations']);

        // Commit transaction
        $pdo->commit();
        
        error_log("Successfully removed digital_file_type_associations table");
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Error removing digital_file_type_associations table: " . $e->getMessage());
        return false;
    }
}
