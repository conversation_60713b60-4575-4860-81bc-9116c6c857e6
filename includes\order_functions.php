<?php

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/db.php';
require_once __DIR__ . '/functions.php';
require_once __DIR__ . '/security.php';
require_once __DIR__ . '/payment_methods.php';

function send_order_confirmation_email($order_id) {
    
    $order = db_query("SELECT * FROM orders WHERE id = :id", [':id' => $order_id], true);
    if (!$order) {
        return false;
    }

    
    $order_items = db_query("SELECT * FROM order_items WHERE order_id = :order_id", [':order_id' => $order_id], false, true);

    
    $customer_info = json_decode($order['customer_info_json'], true);

    
    $store_name = get_setting('store_name', 'A Nossa Loja');
    $currency_symbol = get_setting('currency_symbol', '€');

    
    $customer_email = $customer_info['customer_email'] ?? '';
    $customer_name = $customer_info['customer_name'] ?? '';

    if (empty($customer_email)) {
        return false;
    }

    
    $access_token = get_order_access_token($order_id);
    if (!$access_token) {
        $access_token = create_order_access_token($order_id);
    }

    
    $payment_method_id = $order['payment_method'];
    $payment_instructions = null;
    $payment_method_title = '';

    
    if (is_numeric($payment_method_id)) {
        $payment_method = get_payment_method($payment_method_id);
        if ($payment_method) {
            $payment_instructions = $payment_method['instructions'];
            $payment_method_title = $payment_method['title'];
        }
    } else {
        
        if ($payment_method_id === 'bank_transfer') {
            $payment_method = db_query("SELECT * FROM payment_methods WHERE title LIKE '%Transferência%' AND is_active = 1 LIMIT 1", [], true);
            if ($payment_method) {
                $payment_instructions = $payment_method['instructions'];
                $payment_method_title = $payment_method['title'];
            } else {
                $payment_method_title = 'Transferência Bancária';
            }
        }
    }

    
    $email_subject = "Confirmação de Encomenda #{$order['order_ref']} - $store_name";

    
    $email_body_html = "<div style='font-family: Arial, sans-serif; max-width: 960px; margin: 0 auto; padding: 20px; border: 1px solid #eee; box-shadow: 0 0 10px rgba(0,0,0,0.1);'>";
    $email_body_html .= "<div style='text-align: center; margin-bottom: 20px;'><img src='" . BASE_URL . "/public/assets/images/logo/store_logo.png' alt='" . sanitize_input($store_name) . " Logo' style='max-width: 200px; height: auto;'></div>";
    $email_body_html .= "<h1 style='color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px; text-align: center;'>Confirmação de Encomenda</h1>";
    $email_body_html .= "<p>Olá " . sanitize_input($customer_name) . ",</p>";
    $email_body_html .= "<p>Obrigado pela sua encomenda. Abaixo estão os detalhes do seu pedido:</p>";

    
    $email_body_html .= "<div style='background-color: #f9f9f9; border: 1px solid #ddd; padding: 15px; margin: 15px 0;'>";
    $email_body_html .= "<h2 style='margin-top: 0; color: #333;'>Detalhes da Encomenda</h2>";
    $email_body_html .= "<p><strong>Número da Encomenda:</strong> " . sanitize_input($order['order_ref']) . "</p>";
    $email_body_html .= "<p><strong>Data:</strong> " . date('d/m/Y H:i', strtotime($order['created_at'])) . "</p>";
    
    require_once __DIR__ . '/order_statuses.php';
    $status_name_pt = get_status_name_pt($order['status']);
    $email_body_html .= "<p><strong>Estado:</strong> " . sanitize_input($status_name_pt) . "</p>";
    $email_body_html .= "<p><strong>Método de Pagamento:</strong> " . sanitize_input($payment_method_title ?: $order['payment_method']) . "</p>";
    $email_body_html .= "<p><strong>Notas para este pedido:</strong> " . nl2br(sanitize_input($customer_info['order_notes'])) . "</p>";

    
    $order_details_url = BASE_URL . '/index.php?view=order_success&token=' . $access_token;
    $email_body_html .= "<p><strong>Detalhes da Encomenda:</strong> <a href='" . $order_details_url . "'>Ver Detalhes da Encomenda</a></p>";

    $email_body_html .= "</div>";

    
    $email_body_html .= "<h2 style='color: #333;'>Itens do Pedido</h2>";
    $email_body_html .= "<table style='width: 100%; border-collapse: collapse;'>";
    $email_body_html .= "<tr style='background-color: #f2f2f2;'>";
    $email_body_html .= "<th style='padding: 8px; text-align: left; border: 1px solid #ddd;'>Produto</th>";
    $email_body_html .= "<th style='padding: 8px; text-align: center; border: 1px solid #ddd;'>Quantidade</th>";
    $email_body_html .= "<th style='padding: 8px; text-align: right; border: 1px solid #ddd;'>Preço</th>";
    $email_body_html .= "<th style='padding: 8px; text-align: right; border: 1px solid #ddd;'>Total</th>";
    $email_body_html .= "</tr>";

    $has_digital_products = false;
    $digital_products = [];

    foreach ($order_items as $item) {
        $item_details = json_decode($item['product_details_json'], true);
        $item_name = $item_details['name'] ?? 'Produto';
        $item_attributes = $item_details['attributes_display'] ?? '';
        $item_custom_fields = $item_details['custom_fields'] ?? [];

        
        $product = db_query(
            "SELECT product_type FROM products WHERE id = :id",
            [':id' => $item['product_id']],
            true
        );

        $is_digital = ($product && $product['product_type'] === 'digital');
        if ($is_digital) {
            $has_digital_products = true;
            $digital_products[] = [
                'name' => $item_name,
                'product_id' => $item['product_id']
            ];
        }

        $email_body_html .= "<tr>";
        $email_body_html .= "<td style='padding: 8px; border: 1px solid #ddd;'>" . sanitize_input($item_name);

        
        if ($is_digital) {
            $email_body_html .= " <span style='background-color: #e3f2fd; color: #0d47a1; padding: 2px 5px; border-radius: 3px; font-size: 0.8em;'>Produto Digital</span>";
        }

        if (!empty($item_attributes)) {
            $email_body_html .= "<br><small style='color: #666;'>" . sanitize_input($item_attributes) . "</small>";
        }

        
        if (!empty($item_custom_fields)) {
            require_once __DIR__ . '/custom_field_functions.php';
            foreach ($item_custom_fields as $custom_field) {
                $email_body_html .= "<br><small style='color: #666;'>" . sanitize_input(get_cart_custom_field_display($custom_field)) . "</small>";
            }
        }

        $email_body_html .= "</td>";
        $email_body_html .= "<td style='padding: 8px; text-align: center; border: 1px solid #ddd;'>" . (int)$item['quantity'] . "</td>";
        $email_body_html .= "<td style='padding: 8px; text-align: right; border: 1px solid #ddd;'>" . format_price($item['price_at_purchase'], $currency_symbol) . "</td>";
        $email_body_html .= "<td style='padding: 8px; text-align: right; border: 1px solid #ddd;'>" . format_price($item['price_at_purchase'] * $item['quantity'], $currency_symbol) . "</td>";
        $email_body_html .= "</tr>";
    }

    $email_body_html .= "</table>";

    
    if ($has_digital_products) {
        
        require_once __DIR__ . '/digital_order_functions.php';

        
        $licenses = db_query(
            "SELECT * FROM licenses WHERE order_id = :order_id",
            [':order_id' => $order_id],
            false, true
        );

        $email_body_html .= "<div style='margin: 20px 0; padding: 15px; border: 1px solid #ddd; background-color: #f9f9f9;'>";
        $email_body_html .= "<h3 style='color: #333; margin-top: 0;'>Informação sobre Produtos Digitais</h3>";
        $email_body_html .= "<p>A sua encomenda contém produtos digitais. Após a confirmação do pagamento, receberá um email com instruções para download.</p>";

        if (!empty($licenses)) {
            $email_body_html .= "<h4 style='color: #333;'>Códigos de Licença:</h4>";
            $email_body_html .= "<ul>";

            foreach ($licenses as $license) {
                $email_body_html .= "<li><strong>Código:</strong> " . $license['license_code'] . " <span style='color: #777;'>(Inativo - será ativado após confirmação de pagamento)</span></li>";
            }

            $email_body_html .= "</ul>";
            $email_body_html .= "<p>Guarde estes códigos para referência futura. Poderá utilizá-los para fazer download dos seus produtos digitais após a ativação.</p>";
        }

        $email_body_html .= "</div>";
    }

    
    $email_body_html .= "<div style='margin-top: 20px; text-align: right;'>";

    
    $tax_amount = isset($order['tax_amount']) ? (float)$order['tax_amount'] : 0.0;
    $shipping_cost = isset($order['shipping_cost']) ? (float)$order['shipping_cost'] : 0.0;
    $discount_amount = isset($order['discount_amount']) ? (float)$order['discount_amount'] : 0.0;
    $total_amount = isset($order['total_amount']) ? (float)$order['total_amount'] : 0.0;

    $subtotal = $total_amount - $tax_amount - $shipping_cost + $discount_amount;

    $email_body_html .= "<p><strong>Subtotal:</strong> " . format_price($subtotal, $currency_symbol) . "</p>";

    if ($discount_amount > 0) {
        $email_body_html .= "<p><strong>Desconto:</strong> -" . format_price($discount_amount, $currency_symbol) . "</p>";
    }

    
    $tax_details = json_decode($order['tax_details_json'] ?? '{}', true);

    if (!empty($tax_details)) {
        $email_body_html .= "<p><strong>IVA:</strong> " . format_price($tax_amount, $currency_symbol) . "</p>";

        
        $email_body_html .= "<div style='margin-left: 20px; font-size: 12px;'>";
        foreach ($tax_details as $rate => $details) {
            $email_body_html .= "<p>" . number_format($details['rate'], 1, ',', '.') . "% - " .
                                sanitize_input($details['description']) . ": " . "</p>";
        }
        $email_body_html .= "</div>";
    } else {
        
        require_once __DIR__ . '/vat_functions.php';
        $default_vat = get_default_vat_rate();
        $vat_description = $default_vat && !empty($default_vat['description']) ? ' (' . sanitize_input($default_vat['description']) . ')' : '';

        $email_body_html .= "<p><strong>IVA" . $vat_description . ":</strong> " . format_price($tax_amount, $currency_symbol) . "</p>";
    }
    $email_body_html .= "<p><strong>Envio:</strong> " . format_price($shipping_cost, $currency_symbol) . "</p>";
    $email_body_html .= "<p style='font-size: 18px;'><strong>Total:</strong> " . format_price($total_amount, $currency_symbol) . "</p>";
    $email_body_html .= "</div>";

    
    if ($payment_instructions) {
        $email_body_html .= "<div style='background-color: #f9f9f9; border: 1px solid #ddd; padding: 15px; margin: 15px 0;'>";
        $email_body_html .= "<h2 style='margin-top: 0; color: #333;'>Instruções de Pagamento</h2>";
        $email_body_html .= "<pre style='white-space: pre-wrap; font-family: Arial, sans-serif;'>" . sanitize_input($payment_instructions) . "</pre>";
        $email_body_html .= "</div>";
    }

    
    $email_body_html .= "<div style='margin-top: 20px;'>";
    $email_body_html .= "<h2 style='color: #333;'>Informação do Cliente</h2>";
    $email_body_html .= "<p><strong>Nome:</strong> " . sanitize_input($customer_info['customer_name'] ?? '') . "</p>";

    
    if (!empty($customer_info['customer_vat_id'])) {
        
        $customer_vat_description = '';
        if (!empty($customer_info['customer_vat_description'])) {
            $customer_vat_description = ' (' . sanitize_input($customer_info['customer_vat_description']) . ')';
        } else {
            
            if (!empty($customer_info['customer_vat_rate_id'])) {
                require_once __DIR__ . '/vat_functions.php';
                $vat_rate = get_vat_rate($customer_info['customer_vat_rate_id']);
                if ($vat_rate && !empty($vat_rate['description'])) {
                    $customer_vat_description = ' (' . sanitize_input($vat_rate['description']) . ')';
                }
            }
        }
        $email_body_html .= "<p><strong>NIF:</strong> " . sanitize_input($customer_info['customer_vat_id']) . $customer_vat_description . "</p>";
    }

    $email_body_html .= "<h3 style='color: #333; margin-top: 15px;'>Morada de Envio</h3>";
    $email_body_html .= "<p>" . sanitize_input($customer_info['shipping_address'] ?? '') . "</p>";
    $email_body_html .= "<p>" . sanitize_input($customer_info['shipping_zip'] ?? '') . " " . sanitize_input($customer_info['shipping_city'] ?? '') . ", " . sanitize_input($customer_info['shipping_country'] ?? '') . "</p>";
    $email_body_html .= "</div>";

    
    $email_body_html .= "<div style='margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; color: #666; font-size: 12px;'>";
    $email_body_html .= "<p>Se notar algum erro ou tiver alguma dúvida sobre a sua encomenda, por favor contacte-me.</p>";
    $email_body_html .= "<p>Com os melhores cumprimentos,<br>" . $store_name . "</p>";
    $email_body_html .= "</div>";

    $email_body_html .= "</div>";

    
    return send_email(
        $customer_email,
        $customer_name,
        $email_subject,
        $email_body_html
    );
}

function get_orders_by_status($status) {
    $sql = "SELECT * FROM orders WHERE status = :status ORDER BY created_at DESC";
    return db_query($sql, [':status' => $status], false, true);
}

function get_order_by_id(int $id): array|false
{
    if ($id <= 0) return false;
    $sql = "SELECT * FROM orders WHERE id = :id";
    $order = db_query($sql, [':id' => $id], true); 
    return is_array($order) ? $order : false;
}

function get_order_items(int $order_id): array
{
    if ($order_id <= 0) return [];
    $sql = "SELECT * FROM order_items WHERE order_id = :order_id ORDER BY id ASC";
    $items = db_query($sql, [':order_id' => $order_id], false, true); 
    return is_array($items) ? $items : [];
}

function update_order_status($order_id, $status, $notify_customer = false, $tracking_number = null, $tracking_url = null) {
    $pdo = get_db_connection();

    try {
        
        $order = db_query("SELECT * FROM orders WHERE id = :id", [':id' => $order_id], true);
        if (!$order) {
            return false;
        }

        $status_changed = ($order['status'] != $status);
        $tracking_updated = ($tracking_number !== null && $order['tracking_number'] != $tracking_number) ||
                           ($tracking_url !== null && $order['tracking_url'] != $tracking_url);

        
        $sql = "UPDATE orders SET status = :status, updated_at = datetime('now', 'localtime')";
        $params = [
            ':id' => $order_id,
            ':status' => $status
        ];

        
        if ($tracking_number !== null) {
            $sql .= ", tracking_number = :tracking_number";
            $params[':tracking_number'] = $tracking_number;
        }

        
        if ($tracking_url !== null) {
            $sql .= ", tracking_url = :tracking_url";
            $params[':tracking_url'] = $tracking_url;
        }

        $sql .= " WHERE id = :id";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        $updated = $stmt->rowCount() > 0;

        
        if ($status_changed) {
            require_once __DIR__ . '/digital_order_functions.php';

            
            
            $has_digital_items_check = db_query(
                "SELECT 1 FROM order_items oi JOIN products p ON oi.product_id = p.id WHERE oi.order_id = :order_id AND p.product_type = 'digital' LIMIT 1",
                [':order_id' => $order_id],
                true
            );

            if ($has_digital_items_check) {
                switch ($status) {
                    case 'shipped':
                    case 'completed':
                        activate_licenses_for_order($order_id);
                        break;
                    case 'cancelled':
                        cancel_licenses_for_order($order_id);
                        break;
                    case 'refunded':
                        deactivate_licenses_for_order($order_id);
                        break;
                }
            }

            
            
        }

        
        if ($notify_customer && $updated) {
            
            require_once __DIR__ . '/order_statuses.php';

            
            if ($status_changed && get_setting('order_email_status_change_enabled', '1') == '1') {
                $notification_sent = send_order_status_notification($order_id, $status);
            }

            
            if ($tracking_updated && get_setting('order_email_tracking_update_enabled', '1') == '1') {
                $tracking_notification_sent = send_order_tracking_notification($order_id, $tracking_number);
            }
        }

        return $updated;
    } catch (PDOException $e) {
        return false;
    }
}

function send_order_status_notification($order_id, $status) {
    
    $order = db_query("SELECT * FROM orders WHERE id = :id", [':id' => $order_id], true);
    if (!$order) {
        return false;
    }

    
    $customer_info = json_decode($order['customer_info_json'], true);

    
    if (isset($customer_info['anonymized']) && $customer_info['anonymized']) {
        return false;
    }

    
    
    $last_email_sent_key = "last_email_sent_status_" . $order_id . "_" . $status;
    $current_time = time();
    $last_email_sent = isset($_SESSION[$last_email_sent_key]) ? $_SESSION[$last_email_sent_key] : 0;

    
    if ($current_time - $last_email_sent < 5) {
        return true; 
    }

    
    $_SESSION[$last_email_sent_key] = $current_time;

    
    $customer_email = $customer_info['customer_email'] ?? '';
    $customer_name = $customer_info['customer_name'] ?? '';
    $customer_first_name = explode(' ', $customer_name)[0] ?? $customer_name;
    $customer_last_name = count(explode(' ', $customer_name)) > 1 ?
                          implode(' ', array_slice(explode(' ', $customer_name), 1)) : '';

    if (empty($customer_email)) {
        return false;
    }

    
    $store_name = get_setting('store_name', 'A Nossa Loja');

    
    require_once __DIR__ . '/order_statuses.php';
    $status_name = get_status_name_pt($status);

    
    $access_token = get_order_access_token($order_id);
    if (!$access_token) {
        $access_token = create_order_access_token($order_id);
    }

    
    $order_details_url = BASE_URL . '/index.php?view=order_success&token=' . $access_token;

    
    $email_template = get_setting('order_email_template_status_change',
        '<p>Olá {customer_first_name},</p>
        <p>O estado da sua encomenda #{order_number} foi alterado para <strong>{order_status}</strong>.</p>
        <p>Pode consultar os detalhes da sua encomenda a qualquer momento <a href="{order_details_url}">clicando aqui</a>.</p>
        {digital_license_info}
        <p>Cumprimentos,<br>{store_name}</p>');

    
    $has_digital_products = false;
    $digital_license_info = '';

    $order_items = db_query(
        "SELECT oi.*, p.product_type, p.name_pt FROM order_items oi
        JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = :order_id AND p.product_type = 'digital'",
        [':order_id' => $order_id],
        false, true
    );

    if (!empty($order_items)) {
        $has_digital_products = true;

        
        require_once __DIR__ . '/digital_order_functions.php';

        
        switch ($status) {
            case 'processing':
                $digital_license_info = '<p><strong>Informação sobre produtos digitais:</strong> Os seus produtos digitais estão a ser processados. As licenças serão ativadas quando o pagamento for confirmado.</p>';
                break;

            case 'shipped':
            case 'completed':
                
                $licenses = db_query(
                    "SELECT * FROM licenses WHERE order_id = :order_id",
                    [':order_id' => $order_id],
                    false, true
                );

                if (!empty($licenses)) {
                    $digital_license_info = '<p><strong>Informação sobre produtos digitais:</strong> As suas licenças de produtos digitais foram ativadas. Verifique o seu email para instruções de download ou aceda à página de downloads com o seu código de licença.</p>';

                    $digital_license_info .= '<div style="margin: 15px 0; padding: 10px; border: 1px solid #ddd; background-color: #f9f9f9;">';
                    $digital_license_info .= '<h3>Códigos de Licença:</h3>';
                    $digital_license_info .= '<ul>';

                    foreach ($licenses as $license) {
                        $digital_license_info .= '<li><strong>Código:</strong> ' . $license['license_code'] . '</li>';
                    }

                    $digital_license_info .= '</ul>';
                    $digital_license_info .= '<p>Um email com instruções detalhadas de download foi enviado para o seu endereço de email.</p>';
                    $digital_license_info .= '</div>';
                }
                break;

            case 'cancelled':
                $digital_license_info = '<p><strong>Informação sobre produtos digitais:</strong> As licenças dos seus produtos digitais foram canceladas devido ao cancelamento da encomenda.</p>';
                break;

            case 'refunded':
                $digital_license_info = '<p><strong>Informação sobre produtos digitais:</strong> As licenças dos seus produtos digitais foram desativadas devido ao reembolso da encomenda.</p>';
                break;
        }
    }

    
    $replacements = [
        '{customer_first_name}' => $customer_first_name,
        '{customer_last_name}' => $customer_last_name,
        '{order_number}' => $order['order_ref'],
        '{order_date}' => date('d/m/Y', strtotime($order['created_at'])),
        '{order_status}' => $status_name,
        '{order_details_url}' => $order_details_url,
        '{store_name}' => $store_name,
        '{digital_license_info}' => $digital_license_info
    ];

    $email_body_html = str_replace(array_keys($replacements), array_values($replacements), $email_template);

    
    $email_subject = "Atualização da Encomenda #{$order['order_ref']} - $store_name";

    
    return send_email(
        $customer_email,
        $customer_name,
        $email_subject,
        $email_body_html
    );
}

function send_order_tracking_notification($order_id, $tracking_number) {
    
    $order = db_query("SELECT * FROM orders WHERE id = :id", [':id' => $order_id], true);
    if (!$order) {
        return false;
    }

    
    $customer_info = json_decode($order['customer_info_json'], true);

    
    if (isset($customer_info['anonymized']) && $customer_info['anonymized']) {
        return false;
    }

    
    
    $last_email_sent_key = "last_email_sent_tracking_" . $order_id . "_" . md5($tracking_number);
    $current_time = time();
    $last_email_sent = isset($_SESSION[$last_email_sent_key]) ? $_SESSION[$last_email_sent_key] : 0;

    
    if ($current_time - $last_email_sent < 5) {
        return true; 
    }

    
    $_SESSION[$last_email_sent_key] = $current_time;

    
    $customer_email = $customer_info['customer_email'] ?? '';
    $customer_name = $customer_info['customer_name'] ?? '';
    $customer_first_name = explode(' ', $customer_name)[0] ?? $customer_name;
    $customer_last_name = count(explode(' ', $customer_name)) > 1 ?
                          implode(' ', array_slice(explode(' ', $customer_name), 1)) : '';

    if (empty($customer_email)) {
        return false;
    }

    
    $store_name = get_setting('store_name', 'A Nossa Loja');

    
    require_once __DIR__ . '/order_statuses.php';
    $status_name = get_status_name_pt($order['status']);

    
    $tracking_url = $order['tracking_url'] ?? '';

    
    $tracking_link_html = '';
    if (!empty($tracking_url) && !empty($tracking_number)) {
        $tracking_link_html = '<p>Pode acompanhar a sua encomenda através do seguinte link: <a href="' . $tracking_url . '" target="_blank">Acompanhar Encomenda</a></p>';
    }

    
    $access_token = get_order_access_token($order_id);
    if (!$access_token) {
        $access_token = create_order_access_token($order_id);
    }

    
    $order_details_url = BASE_URL . '/index.php?view=order_success&token=' . $access_token;

    
    $email_template = get_setting('order_email_template_tracking_update',
        '<p>Olá {customer_first_name},</p>
        <p>A sua encomenda #{order_number} foi enviada!</p>
        <p>Pode acompanhar a sua encomenda com o número de tracking: <strong>{tracking_number}</strong></p>
        {tracking_link}
        <p>Pode consultar os detalhes completos da sua encomenda <a href="{order_details_url}">clicando aqui</a>.</p>
        <p>Cumprimentos,<br>{store_name}</p>');

    
    $replacements = [
        '{customer_first_name}' => $customer_first_name,
        '{customer_last_name}' => $customer_last_name,
        '{order_number}' => $order['order_ref'],
        '{order_date}' => date('d/m/Y', strtotime($order['created_at'])),
        '{order_status}' => $status_name,
        '{tracking_number}' => $tracking_number,
        '{tracking_url}' => $tracking_url,
        '{tracking_link}' => $tracking_link_html,
        '{order_details_url}' => $order_details_url,
        '{store_name}' => $store_name
    ];

    $email_body_html = str_replace(array_keys($replacements), array_values($replacements), $email_template);

    
    $email_subject = "Informação de Envio da Encomenda #{$order['order_ref']} - $store_name";

    
    return send_email(
        $customer_email,
        $customer_name,
        $email_subject,
        $email_body_html
    );
}

function update_order_customer_info($order_id, array $customer_data) {
    $pdo = get_db_connection();

    try {
        
        $order = db_query("SELECT * FROM orders WHERE id = :id", [':id' => $order_id], true);
        if (!$order) {
            return false;
        }

        
        $customer_info = json_decode($order['customer_info_json'], true);
        if (isset($customer_info['anonymized']) && $customer_info['anonymized']) {
            return false;
        }

        
        $updated_customer_info = array_merge($customer_info, $customer_data);

        
        $sql = "UPDATE orders SET
                customer_info_json = :customer_info_json,
                updated_at = datetime('now', 'localtime')
                WHERE id = :id";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':id' => $order_id,
            ':customer_info_json' => json_encode($updated_customer_info)
        ]);

        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

function update_order_payment_method($order_id, $payment_method_id) {
    
    ensure_payment_methods_table_exists();

    $pdo = get_db_connection();

    
    if (empty($order_id)) {
        return false;
    }

    
    if (empty($payment_method_id)) {
        return false;
    }

    try {
        
        $order = db_query("SELECT * FROM orders WHERE id = :id", [':id' => $order_id], true);
        if (!$order) {
            return false;
        }

        
        $customer_info = json_decode($order['customer_info_json'], true);
        if (isset($customer_info['anonymized']) && $customer_info['anonymized']) {
            return false;
        }

        
        if (is_numeric($payment_method_id)) {
            $payment_method = get_payment_method($payment_method_id);
            if (!$payment_method) {
                
            } else {
            }
        } else {
            
            $all_methods = get_payment_methods();
            $method_found = false;
            foreach ($all_methods as $method) {
                if (strtolower($method['title']) === strtolower($payment_method_id)) {
                    $payment_method_id = $method['id']; 
                    $method_found = true;
                    break;
                }
            }

            if (!$method_found) {
            }
        }

        
        $sql = "UPDATE orders SET
                payment_method = :payment_method,
                updated_at = datetime('now', 'localtime')
                WHERE id = :id";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':id' => $order_id,
            ':payment_method' => $payment_method_id
        ]);

        $rows_affected = $stmt->rowCount();

        return $rows_affected > 0;
    } catch (PDOException $e) {
        return false;
    }
}

function resend_order_details($order_id) {
    
    $order = db_query("SELECT * FROM orders WHERE id = :id", [':id' => $order_id], true);
    if (!$order) {
        return false;
    }

    
    $customer_info = json_decode($order['customer_info_json'], true);

    
    if (isset($customer_info['anonymized']) && $customer_info['anonymized']) {
        return false;
    }

    
    
    $last_email_sent_key = "last_email_sent_order_" . $order_id;
    $current_time = time();
    $last_email_sent = isset($_SESSION[$last_email_sent_key]) ? $_SESSION[$last_email_sent_key] : 0;

    
    if ($current_time - $last_email_sent < 5) {
        return true; 
    }

    
    $_SESSION[$last_email_sent_key] = $current_time;

    
    if (!empty($customer_info['customer_vat_id']) && empty($customer_info['customer_vat_description'])) {
        if (!empty($customer_info['customer_vat_rate_id'])) {
            require_once __DIR__ . '/vat_functions.php';
            $vat_rate = get_vat_rate($customer_info['customer_vat_rate_id']);
            if ($vat_rate && !empty($vat_rate['description'])) {
                $customer_info['customer_vat_description'] = $vat_rate['description'];
                
                $pdo = get_db_connection();
                if ($pdo) {
                    try {
                        $sql = "UPDATE orders SET
                                customer_info_json = :customer_info_json
                                WHERE id = :id";
                        $stmt = $pdo->prepare($sql);
                        $stmt->execute([
                            ':id' => $order_id,
                            ':customer_info_json' => json_encode($customer_info)
                        ]);
                    } catch (Exception $e) {
                        
                    }
                }
            }
        }
    }

    
    return send_order_confirmation_email($order_id);
}

function anonymize_order($order_id, $notify_customer = false) {
    $pdo = get_db_connection();

    try {
        
        $order = db_query("SELECT * FROM orders WHERE id = :id", [':id' => $order_id], true);
        if (!$order) {
            return false;
        }

        $customer_info = json_decode($order['customer_info_json'], true);

        
        $original_customer_email = $customer_info['customer_email'] ?? '';
        $original_customer_name = $customer_info['customer_name'] ?? '';
        $original_customer_first_name = explode(' ', $original_customer_name)[0] ?? $original_customer_name;
        $original_customer_last_name = count(explode(' ', $original_customer_name)) > 1 ?
                                      implode(' ', array_slice(explode(' ', $original_customer_name), 1)) : '';

        
        if (isset($customer_info['anonymized']) && $customer_info['anonymized']) {
            return true; 
        }

        
        if ($notify_customer && !empty($original_customer_email) && get_setting('order_email_anonymized_enabled', '1') == '1') {
            
            
            $last_email_sent_key = "last_email_sent_anonymize_" . $order_id;
            $current_time = time();
            $last_email_sent = isset($_SESSION[$last_email_sent_key]) ? $_SESSION[$last_email_sent_key] : 0;

            
            if ($current_time - $last_email_sent < 5) {
                
            } else {
                
                $_SESSION[$last_email_sent_key] = $current_time;

                
                $store_name = get_setting('store_name', 'A Nossa Loja');

                
                $customer_first_name = explode(' ', $original_customer_name)[0] ?? $original_customer_name;

                
                $order_date = date('d/m/Y', strtotime($order['created_at']));

                
                $email_template = get_setting('order_email_template_anonymized', '');

                if (!empty($email_template)) {
                    
                    $replacements = [
                        '{customer_first_name}' => $customer_first_name,
                        '{order_number}' => $order['order_ref'],
                        '{order_date}' => $order_date,
                        '{store_name}' => $store_name
                    ];

                    $email_body_html = str_replace(array_keys($replacements), array_values($replacements), $email_template);
                } else {
                    
                    ob_start();
                    include __DIR__ . '/../templates/emails/order_anonymized.php';
                    $email_body_html = ob_get_clean();
                }

                
                $email_subject = "Informação Importante Sobre a Sua Encomenda - {$store_name}";

                
                $email_sent = send_email(
                    $original_customer_email,
                    $original_customer_name,
                    $email_subject,
                    $email_body_html
                );

                if (!$email_sent) {
                    
                }
            }
        }

        
        $anonymized_info = [
            'customer_name' => 'Anonimizado',
            'customer_email' => '<EMAIL>',
            'customer_phone' => 'XXXXXXXXXX',
            'customer_vat_id' => 'XXXXXXXXX', 
            'shipping_address' => 'Endereço anonimizado',
            'shipping_city' => 'Cidade anonimizada',
            'shipping_zip' => 'XXXX-XXX',
            'shipping_country' => $customer_info['shipping_country'] ?? 'PT', 
            'anonymized' => true,
            'anonymized_at' => date('Y-m-d H:i:s')
        ];

        
        $sql = "UPDATE orders SET
                customer_info_json = :customer_info_json,
                updated_at = datetime('now', 'localtime')
                WHERE id = :id";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':id' => $order_id,
            ':customer_info_json' => json_encode($anonymized_info)
        ]);

        return $stmt->rowCount() > 0;
    } catch (PDOException $e) {
        return false;
    }
}

function get_order_custom_field_file_paths(int $order_id): array {
    $file_paths = [];

    try {
        
        $order_items = get_order_items($order_id);

        if (empty($order_items)) {
            return [];
        }

        $pdo = get_db_connection();

        
        $order_item_ids = array_column($order_items, 'id');

        if (empty($order_item_ids)) {
            return [];
        }

        
        $order_item_ids_str = implode(',', $order_item_ids);

        
        $sql = "SELECT file_path FROM order_item_custom_fields
                WHERE order_item_id IN ($order_item_ids_str)
                AND file_path IS NOT NULL AND file_path != ''";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();

        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            if (!empty($row['file_path'])) {
                $file_paths[] = $row['file_path'];
            }
        }

    } catch (Exception $e) {
    }

    return $file_paths;
}

function delete_custom_field_files(array $file_paths): array {
    $result = [
        'deleted' => 0,
        'failed' => 0,
        'not_found' => 0
    ];

    if (empty($file_paths)) {
        return $result;
    }

    foreach ($file_paths as $file_path) {
        
        $absolute_path = PROJECT_ROOT . $file_path;

        if (file_exists($absolute_path)) {
            if (@unlink($absolute_path)) {
                $result['deleted']++;
            } else {
                $result['failed']++;
            }
        } else {
            $result['not_found']++;
        }
    }

    return $result;
}

function generate_order_access_token(int $order_id): string {
    try {
        
        $random_part = bin2hex(random_bytes(8));

        
        $timestamp = dechex(time());

        
        $encoded_id = str_pad(dechex($order_id), 8, '0', STR_PAD_LEFT);

        
        $combined = $random_part . $timestamp . $encoded_id;

        
        $checksum = substr(sha1($combined . 'order_token_salt'), -8);

        
        return $random_part . $timestamp . $encoded_id . $checksum;
    } catch (Exception $e) {
        
        $random_part = md5(uniqid(microtime(), true));
        $encoded_id = str_pad(dechex($order_id), 8, '0', STR_PAD_LEFT);
        $timestamp = dechex(time());
        $combined = $random_part . $timestamp . $encoded_id;
        $checksum = substr(sha1($combined . 'order_token_salt'), -8);
        return substr($random_part, 0, 16) . $timestamp . $encoded_id . $checksum;
    }
}

function extract_order_id_from_token(string $token): int|false {
    
    if (strlen($token) < 40) {
        return false;
    }

    
    $random_part = substr($token, 0, 16);
    $timestamp = substr($token, 16, 8);
    $encoded_id = substr($token, 24, 8);
    $checksum = substr($token, 32);

    
    $combined = $random_part . $timestamp . $encoded_id;
    $expected_checksum = substr(sha1($combined . 'order_token_salt'), -8);

    if ($checksum !== $expected_checksum) {
        return false;
    }

    
    try {
        $order_id = hexdec($encoded_id);
        return $order_id > 0 ? $order_id : false;
    } catch (Exception $e) {
        return false;
    }
}

function create_order_access_token(int $order_id): string|false {
    if ($order_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        
        $table_check = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_access_tokens'");
        if (!$table_check->fetch()) {
            
            $create_table_sql = "CREATE TABLE order_access_tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                access_token TEXT NOT NULL,
                created_at TEXT NOT NULL,
                expires_at TEXT NOT NULL,
                UNIQUE(order_id, access_token)
            )";
            $pdo->exec($create_table_sql);

            
            $pdo->exec("CREATE INDEX idx_order_access_tokens_order_id ON order_access_tokens(order_id)");
            $pdo->exec("CREATE INDEX idx_order_access_tokens_access_token ON order_access_tokens(access_token)");
        }

        
        $access_token = generate_order_access_token($order_id);

        
        $expires_at = date('Y-m-d H:i:s', strtotime('+30 days'));

        
        $sql = "DELETE FROM order_access_tokens WHERE order_id = :order_id";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([':order_id' => $order_id]);

        
        $sql = "INSERT INTO order_access_tokens (
                    order_id, access_token, created_at, expires_at
                ) VALUES (
                    :order_id, :access_token, datetime('now', 'localtime'), :expires_at
                )";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':order_id' => $order_id,
            ':access_token' => $access_token,
            ':expires_at' => $expires_at
        ]);

        return $access_token;
    } catch (Exception $e) {
        return false;
    }
}

function validate_order_access_token(string $access_token, ?int $order_id = null): int|false {
    if (empty($access_token)) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        
        $table_check = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_access_tokens'");
        if (!$table_check->fetch()) {
            return false;
        }

        
        $extracted_order_id = extract_order_id_from_token($access_token);

        if ($extracted_order_id) {
            
            $sql = "SELECT order_id FROM order_access_tokens
                    WHERE order_id = :order_id
                    AND access_token = :access_token
                    AND expires_at > datetime('now', 'localtime')";

            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                ':order_id' => $extracted_order_id,
                ':access_token' => $access_token
            ]);

            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result) {
                return (int)$result['order_id'];
            }
        }

        
        if ($order_id) {
            $sql = "SELECT order_id FROM order_access_tokens
                    WHERE order_id = :order_id
                    AND access_token = :access_token
                    AND expires_at > datetime('now', 'localtime')";

            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                ':order_id' => $order_id,
                ':access_token' => $access_token
            ]);

            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result) {
                return (int)$result['order_id'];
            }
        }

        
        
        $sql = "SELECT order_id FROM order_access_tokens
                WHERE access_token = :access_token
                AND expires_at > datetime('now', 'localtime')";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([':access_token' => $access_token]);

        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result ? (int)$result['order_id'] : false;
    } catch (Exception $e) {
        return false;
    }
}

function get_order_access_token(int $order_id): string|false {
    if ($order_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {
        
        $table_check = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_access_tokens'");
        if (!$table_check->fetch()) {
            return false;
        }

        
        $sql = "SELECT access_token, id FROM order_access_tokens
                WHERE order_id = :order_id
                AND expires_at > datetime('now', 'localtime')
                ORDER BY created_at DESC LIMIT 1";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([':order_id' => $order_id]);

        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$result) {
            return false;
        }

        $token = $result['access_token'];

        
        if (!extract_order_id_from_token($token)) {
            
            $new_token = generate_order_access_token($order_id);

            
            $update_sql = "UPDATE order_access_tokens SET access_token = :new_token
                          WHERE id = :id";
            $update_stmt = $pdo->prepare($update_sql);
            $update_stmt->execute([
                ':new_token' => $new_token,
                ':id' => $result['id']
            ]);

            return $new_token;
        }

        return $token;
    } catch (Exception $e) {
        return false;
    }
}

function migrate_order_tokens_to_new_format(): array {
    $pdo = get_db_connection();
    if (!$pdo) return ['success' => false, 'message' => 'Database connection failed'];

    try {
        
        $table_check = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_access_tokens'");
        if (!$table_check->fetch()) {
            return ['success' => false, 'message' => 'Tokens table does not exist'];
        }

        
        $sql = "SELECT id, order_id, access_token FROM order_access_tokens";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();

        $tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $migrated = 0;
        $already_migrated = 0;
        $errors = 0;

        foreach ($tokens as $token) {
            
            if (!extract_order_id_from_token($token['access_token'])) {
                
                $new_token = generate_order_access_token($token['order_id']);

                
                $update_sql = "UPDATE order_access_tokens SET access_token = :new_token
                              WHERE id = :id";
                $update_stmt = $pdo->prepare($update_sql);
                $result = $update_stmt->execute([
                    ':new_token' => $new_token,
                    ':id' => $token['id']
                ]);

                if ($result) {
                    $migrated++;
                } else {
                    $errors++;
                }
            } else {
                $already_migrated++;
            }
        }

        return [
            'success' => true,
            'total' => count($tokens),
            'migrated' => $migrated,
            'already_migrated' => $already_migrated,
            'errors' => $errors
        ];
    } catch (Exception $e) {
        return ['success' => false, 'message' => $e->getMessage()];
    }
}

function delete_order($order_id, $run_cleanup = true) {

    if (empty($order_id) || !is_numeric($order_id)) {
        return false;
    }

    $pdo = get_db_connection();

    
    $file_paths = get_order_custom_field_file_paths($order_id);

    try {
        
        $pdo->beginTransaction();

        
        $order = db_query("SELECT * FROM orders WHERE id = :id", [':id' => $order_id], true);
        if (!$order) {
            return false;
        }

        
        
        $delete_items_sql = "DELETE FROM order_items WHERE order_id = :order_id";
        $stmt_items = $pdo->prepare($delete_items_sql);
        $stmt_items->execute([':order_id' => $order_id]);
        $items_deleted = $stmt_items->rowCount();

        
        
        if ($pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_item_custom_fields'")->fetch()) {
            $delete_custom_fields_sql = "DELETE FROM order_item_custom_fields
                                        WHERE order_item_id IN (
                                            SELECT id FROM order_items WHERE order_id = :order_id
                                        )";
            $stmt_custom = $pdo->prepare($delete_custom_fields_sql);
            $stmt_custom->execute([':order_id' => $order_id]);
            $custom_fields_deleted = $stmt_custom->rowCount();
        }

        
        if ($pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_access_tokens'")->fetch()) {
            $delete_tokens_sql = "DELETE FROM order_access_tokens WHERE order_id = :order_id";
            $stmt_tokens = $pdo->prepare($delete_tokens_sql);
            $stmt_tokens->execute([':order_id' => $order_id]);
        }

        
        $delete_order_sql = "DELETE FROM orders WHERE id = :id";
        $stmt_order = $pdo->prepare($delete_order_sql);
        $stmt_order->execute([':id' => $order_id]);
        $order_deleted = $stmt_order->rowCount();

        if ($order_deleted > 0) {
            
            $pdo->commit();

            
            if (!empty($file_paths)) {
                $delete_results = delete_custom_field_files($file_paths);

            }

            
            if ($run_cleanup) {
                require_once __DIR__ . '/db_maintenance.php';
                $cleanup_results = cleanup_database();
            }

            return true;
        } else {
            
            $pdo->rollBack();
            return false;
        }
    } catch (PDOException $e) {
        
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function record_order_visit(int $order_id, string $access_token): bool {
    
    $session_id = session_id();
    if (empty($session_id)) {
        return false;
    }

    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        
        $stmt = $pdo->prepare(
            "SELECT COUNT(*) FROM order_visits WHERE order_id = :order_id"
        );
        $stmt->execute([':order_id' => $order_id]);
        $visit_count = (int)$stmt->fetchColumn();
        $is_first_visit = ($visit_count === 0) ? 1 : 0;

        
        $stmt = $pdo->prepare(
            "SELECT id FROM order_visits
             WHERE order_id = :order_id AND session_id = :session_id
             LIMIT 1"
        );
        $stmt->execute([
            ':order_id' => $order_id,
            ':session_id' => $session_id
        ]);
        $existing_visit = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existing_visit) {
            
            
            $stmt = $pdo->prepare(
                "UPDATE order_visits
                 SET visit_time = datetime('now', 'localtime'),
                     access_token = :access_token,
                     is_first_visit = 0
                 WHERE id = :id"
            );

            return $stmt->execute([
                ':id' => $existing_visit['id'],
                ':access_token' => $access_token
            ]);
        } else {
            
            $stmt = $pdo->prepare(
                "INSERT INTO order_visits (order_id, access_token, session_id, is_first_visit)
                 VALUES (:order_id, :access_token, :session_id, :is_first_visit)"
            );

            return $stmt->execute([
                ':order_id' => $order_id,
                ':access_token' => $access_token,
                ':session_id' => $session_id,
                ':is_first_visit' => $is_first_visit
            ]);
        }
    } catch (PDOException $e) {
        return false;
    }
}

function is_order_revisit(int $order_id, ?array $order = null): bool {
    
    if ($order === null) {
        $order = get_order_by_id($order_id);
        if (!$order) {
            return false;
        }
    }

    
    $session_id = session_id();
    if (empty($session_id)) {
        
        return true;
    }

    $pdo = get_db_connection();
    if (!$pdo) {
        
        return true;
    }

    try {
        
        $stmt = $pdo->prepare(
            "SELECT COUNT(*) FROM order_visits WHERE order_id = :order_id"
        );
        $stmt->execute([':order_id' => $order_id]);
        $total_visits = (int)$stmt->fetchColumn();

        if ($total_visits === 0) {
            
            return false;
        }

        
        $stmt = $pdo->prepare(
            "SELECT COUNT(*) FROM order_visits
             WHERE order_id = :order_id AND session_id = :session_id"
        );
        $stmt->execute([
            ':order_id' => $order_id,
            ':session_id' => $session_id
        ]);
        $session_visits = (int)$stmt->fetchColumn();

        if ($session_visits === 0) {
            
            return true;
        }

        
        $stmt = $pdo->prepare(
            "SELECT is_first_visit FROM order_visits
             WHERE order_id = :order_id AND session_id = :session_id
             ORDER BY visit_time DESC LIMIT 1"
        );
        $stmt->execute([
            ':order_id' => $order_id,
            ':session_id' => $session_id
        ]);
        $is_first_visit = (int)$stmt->fetchColumn();

        
        
        if ($is_first_visit === 1) {
            return false;
        }

        
        return true;
    } catch (PDOException $e) {
        
        return true;
    }
}

function generate_order_verification_code(int $order_id): string {
    
    $order = get_order_by_id($order_id);
    if (!$order) {
        return '';
    }

    
    $customer_info = json_decode($order['customer_info_json'], true);

    
    $customer_email = $customer_info['customer_email'] ?? '';
    if (empty($customer_email)) {
        return '';
    }

    
    $secret = defined('CSRF_SECRET') ? CSRF_SECRET : 'order_verification_secret';
    $code_base = $order_id . '|' . $customer_email . '|' . $secret;

    
    $hash = md5($code_base);
    $code = substr(preg_replace('/[^0-9]/', '', $hash), 0, 6);

    return $code;
}

function verify_order_verification_code(int $order_id, string $code): bool {
    if (empty($code)) {
        return false;
    }

    
    $expected_code = generate_order_verification_code($order_id);

    
    return $code === $expected_code;
}

function get_censored_customer_data(array $customer_info): array {
    
    $censored_info = $customer_info;

    
    if (!empty($censored_info['customer_name'])) {
        $name_parts = explode(' ', $censored_info['customer_name']);
        $first_name = $name_parts[0];
        $last_name = count($name_parts) > 1 ? end($name_parts) : '';

        
        if (strlen($first_name) > 2) {
            $censored_first_name = $first_name[0] . str_repeat('*', strlen($first_name) - 2) . $first_name[strlen($first_name) - 1];
        } else {
            $censored_first_name = $first_name[0] . '*';
        }

        
        if (!empty($last_name) && strlen($last_name) > 2) {
            $censored_last_name = $last_name[0] . str_repeat('*', strlen($last_name) - 2) . $last_name[strlen($last_name) - 1];
        } elseif (!empty($last_name)) {
            $censored_last_name = $last_name[0] . '*';
        } else {
            $censored_last_name = '';
        }

        
        if (!empty($censored_last_name)) {
            $censored_info['customer_name'] = $censored_first_name . ' ' . $censored_last_name;
        } else {
            $censored_info['customer_name'] = $censored_first_name;
        }
    }

    
    if (!empty($censored_info['customer_email'])) {
        $email_parts = explode('@', $censored_info['customer_email']);
        $username = $email_parts[0];
        $domain = isset($email_parts[1]) ? $email_parts[1] : '';

        
        if (strlen($username) > 3) {
            $censored_username = substr($username, 0, 2) . str_repeat('*', strlen($username) - 3) . $username[strlen($username) - 1];
        } else {
            $censored_username = $username[0] . str_repeat('*', strlen($username) - 1);
        }

        
        if (!empty($domain)) {
            $domain_parts = explode('.', $domain);
            $domain_name = $domain_parts[0];
            $tld = count($domain_parts) > 1 ? '.' . implode('.', array_slice($domain_parts, 1)) : '';

            if (strlen($domain_name) > 1) {
                $censored_domain = $domain_name[0] . str_repeat('*', strlen($domain_name) - 1) . $tld;
            } else {
                $censored_domain = $domain_name[0] . $tld;
            }

            $censored_info['customer_email'] = $censored_username . '@' . $censored_domain;
        } else {
            $censored_info['customer_email'] = $censored_username;
        }
    }

    
    if (!empty($censored_info['customer_phone'])) {
        $phone = $censored_info['customer_phone'];

        
        if (strlen($phone) > 5) {
            $censored_info['customer_phone'] = substr($phone, 0, 3) . str_repeat('*', strlen($phone) - 5) . substr($phone, -2);
        } else {
            $censored_info['customer_phone'] = substr($phone, 0, 1) . str_repeat('*', strlen($phone) - 1);
        }
    }

    
    if (!empty($censored_info['shipping_address'])) {
        $address = $censored_info['shipping_address'];
        $address_parts = explode(' ', $address);

        
        if (count($address_parts) > 2) {
            $censored_address = $address_parts[0] . ' ' . $address_parts[1] . ' ' . str_repeat('*', strlen(implode(' ', array_slice($address_parts, 2))));
        } else {
            $censored_address = $address_parts[0] . ' ' . str_repeat('*', strlen($address) - strlen($address_parts[0]) - 1);
        }

        $censored_info['shipping_address'] = $censored_address;
    }

    
    if (!empty($censored_info['shipping_city'])) {
        $city = $censored_info['shipping_city'];

        
        if (strlen($city) > 2) {
            $censored_info['shipping_city'] = substr($city, 0, 2) . str_repeat('*', strlen($city) - 2);
        } else {
            $censored_info['shipping_city'] = $city[0] . '*';
        }
    }

    
    if (!empty($censored_info['shipping_zip'])) {
        $zip = $censored_info['shipping_zip'];

        
        if (strlen($zip) > 2) {
            $censored_info['shipping_zip'] = substr($zip, 0, 2) . str_repeat('*', strlen($zip) - 2);
        } else {
            $censored_info['shipping_zip'] = $zip[0] . '*';
        }
    }

    return $censored_info;
}

function send_order_verification_code(int $order_id, string $email): bool {
    
    $order = get_order_by_id($order_id);
    if (!$order) {
        return false;
    }

    
    $customer_info = json_decode($order['customer_info_json'], true);

    
    $customer_email = $customer_info['customer_email'] ?? '';

    
    if (strtolower(trim($email)) !== strtolower(trim($customer_email))) {
        return false;
    }

    
    $verification_code = generate_order_verification_code($order_id);

    
    $store_name = get_setting('store_name', 'A Nossa Loja');

    
    $email_subject = "Código de Verificação para Encomenda #{$order['order_ref']} - $store_name";

    
    $email_body_html = "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>";
    $email_body_html .= "<h1 style='color: #333; border-bottom: 1px solid #ddd; padding-bottom: 10px;'>Código de Verificação</h1>";
    $email_body_html .= "<p>Olá,</p>";
    $email_body_html .= "<p>Foi solicitado um código de verificação para aceder aos detalhes da encomenda #{$order['order_ref']}.</p>";
    $email_body_html .= "<br><p>O seu código de verificação é:</p>";
    $email_body_html .= "<div style='background-color: #f5f5f5; padding: 15px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0;'>";
    $email_body_html .= $verification_code;
    $email_body_html .= "</div>";
    $email_body_html .= "<p>Este código é válido por 30 minutos.</p><br>";
    $email_body_html .= "<p>Se não solicitou este código, pode ignorar este email.</p>";
    $email_body_html .= "<br><p>Com os melhores cumprimentos,<br>" . $store_name . "</p>";
    $email_body_html .= "</div>";

    
    return send_email(
        $email,
        $customer_info['customer_name'] ?? 'Cliente',
        $email_subject,
        $email_body_html
    );
}

function resend_order_details_to_email(int $order_id, string $email): bool {
    
    $order = get_order_by_id($order_id);
    if (!$order) {
        return false;
    }

    
    $customer_info = json_decode($order['customer_info_json'], true);

    
    $customer_email = $customer_info['customer_email'] ?? '';

    
    if (strtolower(trim($email)) !== strtolower(trim($customer_email))) {
        return false;
    }

    
    
    $last_email_sent_key = "last_email_sent_order_" . $order_id;
    $current_time = time();
    $last_email_sent = isset($_SESSION[$last_email_sent_key]) ? $_SESSION[$last_email_sent_key] : 0;

    
    if ($current_time - $last_email_sent < 5) {
        return true; 
    }

    
    $_SESSION[$last_email_sent_key] = $current_time;

    
    return send_order_confirmation_email($order_id);
}

function cleanup_old_order_visits(int $days_to_keep = 30): array {
    $pdo = get_db_connection();
    if (!$pdo) {
        return [
            'success' => false,
            'message' => 'Database connection failed'
        ];
    }

    try {
        
        $table_check = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='order_visits'");
        if (!$table_check->fetch()) {
            return [
                'success' => false,
                'message' => 'Order visits table does not exist'
            ];
        }

        
        $sql = "DELETE FROM order_visits WHERE visit_time < datetime('now', 'localtime', '-{$days_to_keep} days')";
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $deleted_count = $stmt->rowCount();

        return [
            'success' => true,
            'deleted_count' => $deleted_count,
            'message' => "Deleted {$deleted_count} old order visits"
        ];
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Error cleaning up old order visits: ' . $e->getMessage()
        ];
    }
}