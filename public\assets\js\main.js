

const eShopUtils = {


    showNotification: function(type, title, message) {
        if (typeof showToast === 'function') {
            const iconClass = type === 'success' ? 'ri-check-line' : 'ri-error-warning-line';
            const borderClass = type === 'success' ? 'border-green-500' : 'border-red-500';
            showToast(title, message, iconClass, borderClass);
        } else {
            alert(`${title}: ${message}`);
        }
    },



    formatPrice: function(amount, symbol) {
        const numericAmount = Number(amount);
        const validAmount = isNaN(numericAmount) ? 0 : numericAmount;

        try {
            return new Intl.NumberFormat(navigator.language || 'pt-PT', {
                style: 'currency',
                currency: symbol === '€' ? 'EUR' : 'USD'
            }).format(validAmount);
        } catch (e) {

            return symbol + validAmount.toFixed(2).replace('.', ',');
        }
    },



    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => { clearTimeout(timeout); func.apply(this, args); };
            clearTimeout(timeout); timeout = setTimeout(later, wait);
        };
    },



    addSessionParamToUrl: function(url) {
        const sessionId = window.eshopSessionId || '';
        const sessionParamName = window.eshopSessionParam || 'sid';
        if (!sessionId) return url;

        try {
            const urlObject = new URL(url, window.location.origin);
            if (!urlObject.searchParams.has(sessionParamName)) {
                urlObject.searchParams.set(sessionParamName, sessionId);
            }

            if (url.startsWith('/')) {
                 return urlObject.pathname + urlObject.search;
            } else if (!url.startsWith('http')) {

                 const pathAndQuery = url.split('?')[0];
                 return pathAndQuery + urlObject.search;
            }
            return urlObject.toString();
        } catch (e) {

            console.warn("Could not parse URL for adding session param:", url, e);
            const sessionParam = `${sessionParamName}=${sessionId}`;
            if (url.includes('?')) {
                return url.includes(sessionParamName + '=') ? url : url + '&' + sessionParam;
            } else {
                return url + '?' + sessionParam;
            }
        }
    },



     getCartCountFromStorage: function() {
         const storedCount = localStorage.getItem('eshop_cart_count');
         return storedCount ? parseInt(storedCount, 10) : 0;
     },



      setButtonLoadingState: function(button, isLoading, originalContent = null) {
          if (!button) return;
          const loadingSpinner = '<div class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-current"></div>';

          if (isLoading) {

              if (!button.dataset.originalContent) {
                  button.dataset.originalContent = button.innerHTML;
              }
              button.disabled = true;



              button.innerHTML = loadingSpinner;
          } else {
              button.disabled = false;

              button.innerHTML = originalContent || button.dataset.originalContent || 'Error';

              delete button.dataset.originalContent;


          }
      },



       handleAjaxError: function(error, contextMessage, button = null) {
           console.error(`${contextMessage} error:`, error);
           const displayMessage = error?.message && error.message.startsWith('HTTP error!')
               ? `Erro de comunicação (${error.message.split('status: ')[1] || 'desconhecido'}).`
               : error?.message || 'Erro de comunicação. Tente novamente.';
           this.showNotification('error', 'Erro', displayMessage);
           if (button) {
               this.setButtonLoadingState(button, false);
           }
       }
};

async function ajaxRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-Session-UUID': window.eshopSessionId || ''
        },
        credentials: 'same-origin'
    };

    const mergedOptions = { ...defaultOptions, ...options };
    if (options.headers) {
        mergedOptions.headers = { ...defaultOptions.headers, ...options.headers };
    }

    let fullUrl = (window.eshopBaseUrl || '') + '/index.php' + url;
    if (window.eshopSessionId && !url.includes(window.eshopSessionParam || 'sid')) {
        fullUrl += (url.includes('?') ? '&' : '?') + `${window.eshopSessionParam || 'sid'}=${window.eshopSessionId}`;
    }

    try {
        const response = await fetch(fullUrl, mergedOptions);
        if (!response.ok) {
            let errorData;
            let errorText;

            try {
                errorText = await response.text();
                console.error('Error response text:', errorText);

                try {
                    errorData = JSON.parse(errorText);
                } catch (jsonError) {
                    console.error('Failed to parse error response as JSON:', jsonError);
                }
            } catch (e) {
                console.error('Failed to read error response text:', e);
            }

            const errorMessage = errorData?.message || errorData?.error || `HTTP error! status: ${response.status}`;
            console.error('AJAX Error:', errorMessage, 'URL:', fullUrl, 'Options:', mergedOptions);
            throw new Error(errorMessage);
        }

        const contentType = response.headers.get("content-type");

        if (contentType && contentType.indexOf("application/json") !== -1) {

            const rawText = await response.text();

            try {

                return JSON.parse(rawText);
            } catch (jsonError) {
                console.error('JSON parse error:', jsonError);
                console.error('Raw response that caused the error:', rawText);
                throw new Error('Failed to parse JSON response: ' + jsonError.message);
            }
        } else {
            console.warn('AJAX response was not JSON. Content-Type:', contentType);
            const text = await response.text();
            return text;
        }
    } catch (error) {
        console.error('Fetch API Error:', error);
        throw error;
    }
}

document.addEventListener('DOMContentLoaded', () => {


    function updateCartBadge(count) {
        const badge = document.getElementById('cart-item-count');
        if (badge) {
            badge.textContent = count;
            badge.style.display = count > 0 ? 'flex' : 'none';
        }


        const cartPreviewCount = document.getElementById('cart-preview-count');
        if (cartPreviewCount) {
            cartPreviewCount.textContent = count;
        }


        if (typeof count !== 'undefined') {
            localStorage.setItem('eshop_cart_count', count);
        }
    }

    async function fetchCartCount() {
        try {
            const response = await ajaxRequest('?action=get_cart_count');
            if (response && typeof response.item_count !== 'undefined') {
                updateCartBadge(response.item_count);
            } else {
                console.warn("Could not get cart count from server.");

                const storedCount = localStorage.getItem('eshop_cart_count');
                updateCartBadge(storedCount ? parseInt(storedCount) : 0);
            }
        } catch (error) {
            console.error('Error fetching cart count:', error);

            const storedCount = localStorage.getItem('eshop_cart_count');
            updateCartBadge(storedCount ? parseInt(storedCount) : 0);
        }
    }
    fetchCartCount();


    setInterval(fetchCartCount, 30000);


    window.fetchCartCount = fetchCartCount;
    window.fetchCartPreview = fetchCartPreview;



    async function fetchCartPreview() {
        const previewItemsContainer = document.getElementById('cart-preview-items');
        const previewCount = document.getElementById('cart-preview-count');
        const previewSubtotal = document.getElementById('cart-preview-subtotal');

        if (!previewItemsContainer) {
            console.warn('Cart preview container not found in the DOM');
            return;
        }


        previewItemsContainer.innerHTML = '<p class="text-sm text-gray-400 text-center py-4">Carregando...</p>';

        try {

            const fullUrl = (window.eshopBaseUrl || '') + '/index.php?action=get_cart_preview';
            const sessionParam = window.eshopSessionParam || 'sid';
            const sessionId = window.eshopSessionId || '';


            const urlWithSession = sessionId ?
                fullUrl + (fullUrl.includes('?') ? '&' : '?') + `${sessionParam}=${sessionId}` :
                fullUrl;

            const fetchOptions = {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-Session-UUID': sessionId
                },
                credentials: 'same-origin'
            };

            const response = await fetch(urlWithSession, fetchOptions);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }


            const contentType = response.headers.get('Content-Type');
            let data;

            if (contentType && contentType.includes('application/json')) {

                const rawText = await response.text();

                try {

                    data = JSON.parse(rawText);
                } catch (jsonError) {
                    console.error('JSON parse error:', jsonError);
                    console.error('Raw response that caused the error:', rawText);
                    throw new Error('Failed to parse JSON response');
                }
            } else {
                console.warn('Response is not JSON. Content-Type:', contentType);
                const text = await response.text();

                data = {
                    success: false,
                    error: 'Unexpected response format',
                    html: '<p class="text-sm text-gray-400 text-center py-4">Erro ao carregar carrinho.</p>',
                    item_count: 0,
                    subtotal_formatted: '€0,00'
                };
            }

            if (data && data.success && data.html) {

                previewItemsContainer.innerHTML = data.html;


                if (previewCount) {
                    previewCount.textContent = data.item_count;
                }


                if (previewSubtotal) {
                    previewSubtotal.textContent = data.subtotal_formatted;
                }


                updateCartBadge(data.item_count);


                const removeButtons = document.querySelectorAll('#cart-preview-items .remove-preview-item');

                removeButtons.forEach(button => {

                    if (!button.dataset.itemKey && button.dataset.productId && button.dataset.variationId) {
                        button.dataset.itemKey = button.dataset.productId + '-' + button.dataset.variationId;
                    }

                    button.addEventListener('click', async function(e) {
                        e.preventDefault();


                        const cartKey = this.dataset.cartKey || this.dataset.itemKey;

                        if (!cartKey) {

                            const productId = this.dataset.productId;
                            const variationId = this.dataset.variationId;

                            if (productId) {


                                const constructedKey = variationId ? `${productId}-${variationId}` : `${productId}`;

                                try {

                                    const removeResponse = await ajaxRequest('?action=update_cart_item', {
                                        method: 'POST',
                                        body: JSON.stringify({ cart_key: constructedKey, quantity: 0 })
                                    });

                                    if (removeResponse.success) {

                                        fetchCartPreview();


                                        if (typeof showToast === 'function') {
                                            showToast('Sucesso', 'Item removido do carrinho!', 'ri-check-line', 'border-green-500');
                                        }
                                    } else {
                                        if (typeof showToast === 'function') {
                                            showToast('Erro', removeResponse.error || 'Erro ao remover item.', 'ri-error-warning-line', 'border-red-500');
                                        }
                                    }
                                } catch (error) {
                                    console.error('Error removing item from cart preview:', error);
                                    if (typeof showToast === 'function') {
                                        showToast('Erro', 'Erro de comunicação ao remover item.', 'ri-error-warning-line', 'border-red-500');
                                    }
                                }
                                return;
                            } else {
                                console.error('No cart key or product ID found for remove button');
                                return;
                            }
                        }

                        try {

                            const removeResponse = await ajaxRequest('?action=update_cart_item', {
                                method: 'POST',
                                body: JSON.stringify({ cart_key: cartKey, quantity: 0 })
                            });

                            if (removeResponse.success) {

                                fetchCartPreview();


                                if (typeof showToast === 'function') {
                                    showToast('Sucesso', 'Item removido do carrinho!', 'ri-check-line', 'border-green-500');
                                }
                            } else {
                                if (typeof showToast === 'function') {
                                    showToast('Erro', removeResponse.error || 'Erro ao remover item.', 'ri-error-warning-line', 'border-red-500');
                                }
                            }
                        } catch (error) {
                            console.error('Error removing item from cart preview:', error);
                            if (typeof showToast === 'function') {
                                showToast('Erro', 'Erro de comunicação ao remover item.', 'ri-error-warning-line', 'border-red-500');
                            }
                        }
                    });
                });
            } else {

                previewItemsContainer.innerHTML = '<p class="text-sm text-gray-400 text-center py-4">Erro ao carregar carrinho.</p>';
                console.error('Invalid cart preview response:', data);


                if (data && typeof data.item_count !== 'undefined') {
                    if (previewCount) previewCount.textContent = data.item_count;
                    updateCartBadge(data.item_count);
                }


                if (data && data.subtotal_formatted && previewSubtotal) {
                    previewSubtotal.textContent = data.subtotal_formatted;
                }
            }
        } catch (error) {

            previewItemsContainer.innerHTML = '<p class="text-sm text-gray-400 text-center py-4">Erro ao carregar carrinho.</p>';
            console.error('Error fetching cart preview:', error);


            try {
                const countResponse = await fetch((window.eshopBaseUrl || '') + '/index.php?action=get_cart_count&' +
                    (window.eshopSessionParam || 'sid') + '=' + (window.eshopSessionId || ''));
                const countData = await countResponse.json();

                if (countData && typeof countData.item_count !== 'undefined') {
                    if (previewCount) previewCount.textContent = countData.item_count;
                    updateCartBadge(countData.item_count);
                }
            } catch (countError) {
                console.error('Failed to get cart count as fallback:', countError);
            }

            if (typeof eShopUtils !== 'undefined' && typeof eShopUtils.handleAjaxError === 'function') {
                eShopUtils.handleAjaxError(error, 'Erro ao carregar pré-visualização do carrinho');
            }
        }
    }


    document.body.addEventListener('click', async (event) => {

        const button = event.target.closest('.add-to-cart-btn');
        if (!button) return;

        event.preventDefault();
        const productId = button.dataset.productId;
        const variationId = button.dataset.variationId;
        const productType = button.dataset.productType;

        // Check if this is a digital product
        if (productType === 'digital') {
            try {
                // Get current cart count via AJAX
                const cartResponse = await ajaxRequest('?action=get_cart_count', {
                    method: 'GET'
                });

                if (cartResponse.success && cartResponse.digital_products_count >= 10) {
                    if (typeof showToast === 'function') {
                        showToast('Aviso', 'Não é possível adicionar mais de 10 produtos digitais ao carrinho. Por favor, finalize a compra atual antes de adicionar mais produtos digitais.', 'ri-error-warning-line', 'border-yellow-500');
                    } else {
                        alert('Não é possível adicionar mais de 10 produtos digitais ao carrinho. Por favor, finalize a compra atual antes de adicionar mais produtos digitais.');
                    }
                    return;
                }
            } catch (error) {
                console.error('Error checking digital product count:', error);
            }
        }


        let quantity = 1;


        const quantityInput = document.getElementById('detail-quantity');
        if (quantityInput && !isNaN(parseInt(quantityInput.value))) {
            quantity = parseInt(quantityInput.value);
        }


        const originalContent = button.innerHTML;


        button.disabled = true;
        button.innerHTML = '<div class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>';

        try {


            const requestData = {
                product_id: productId,
                quantity: quantity
            };


            if (variationId) {
                requestData.variation_id = variationId;
            }

            const response = await ajaxRequest('?action=add_to_cart', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestData)
            });

            if (response.success) {
                updateCartBadge(response.item_count);
                if (typeof showToast === 'function') {
                    showToast('Sucesso', 'Produto adicionado ao carrinho!', 'ri-check-line', 'border-green-500');
                } else {
                    alert('Produto adicionado ao carrinho!');
                }

                if (typeof fetchCartPreview === 'function') {
                    fetchCartPreview();
                }
            } else {
                if (typeof showToast === 'function') {
                    showToast('Erro', response.error || 'Erro ao adicionar ao carrinho.', 'ri-error-warning-line', 'border-red-500');
                } else {
                    alert('Erro ao adicionar ao carrinho: ' + (response.error || 'Erro desconhecido'));
                }
            }
        } catch (error) {
            console.error('Add to cart error:', error);
            if (typeof showToast === 'function') {
                showToast('Erro', 'Erro de comunicação ao adicionar ao carrinho.', 'ri-error-warning-line', 'border-red-500');
            } else {
                alert('Erro de comunicação ao adicionar ao carrinho.');
            }
        } finally {

            button.disabled = false;
            button.innerHTML = originalContent;
        }
    });


    const variationSelects = document.querySelectorAll('.variation-select');
    const priceElement = document.getElementById('product-price');
    const skuElement = document.getElementById('product-sku');
    const stockElement = document.getElementById('product-stock');
    const addToCartBtn = document.getElementById('add-to-cart-btn');
    const variationErrorElement = document.getElementById('variation-error');
    const selectedVariationInput = document.getElementById('selected-variation-id');


    function optionsMatch(selectedOptions, variationOptions) {
        const selectedKeys = Object.keys(selectedOptions);
        const variationKeys = Object.keys(variationOptions);
        if (selectedKeys.length !== variationKeys.length) return false;
        for (const attrId of selectedKeys) {
            if (!variationOptions.hasOwnProperty(attrId) || String(variationOptions[attrId]) !== String(selectedOptions[attrId])) {
                return false;
            }
        }
        return true;
    }


    function updateVariationDetails() {

        if (!priceElement || !skuElement || !stockElement || !addToCartBtn || !variationErrorElement || !selectedVariationInput || typeof window.productVariations === 'undefined' || typeof window.attributeValueModifiers === 'undefined' || typeof window.productBasePrice === 'undefined') {



             if (selectedVariationInput && window.productVariations) {

                 const variationIds = Object.keys(window.productVariations);
                 if (variationIds.length === 1) {

                     const singleVariationId = variationIds[0];
                     const singleVariation = window.productVariations[singleVariationId];


                     selectedVariationInput.value = singleVariationId;

                     let calculatedModifier = 0;
                     if (singleVariation.options && window.attributeValueModifiers) {
                         Object.values(singleVariation.options).forEach(valueId => {
                             calculatedModifier += window.attributeValueModifiers[valueId] || 0;
                         });
                     }
                     const finalPrice = window.productBasePrice + calculatedModifier;


                     const stock = parseInt(singleVariation.stock);

                     priceElement.textContent = eShopUtils.formatPrice(finalPrice, window.currencySymbol);
                     skuElement.textContent = singleVariation.sku || 'N/D';
                     if (stock > 0) {
                         stockElement.textContent = `Em Stock (${stock} disponíveis)`;
                         stockElement.className = 'badge bg-success';
                         addToCartBtn.disabled = false;
                         addToCartBtn.dataset.variationId = singleVariationId;
                     } else {
                         stockElement.textContent = 'Esgotado';
                         stockElement.className = 'badge bg-danger';
                         addToCartBtn.disabled = true;
                         addToCartBtn.dataset.variationId = '';
                     }
                     variationErrorElement.style.display = 'none';
                 }
             }
            return;
        }


        const selectedValueIds = {};
        let currentTotalModifier = 0;
        let allSelected = true;

        variationSelects.forEach(select => {
            const attributeId = select.dataset.attributeId;
            const selectedValueId = select.value;

            if (selectedValueId) {
                selectedValueIds[attributeId] = selectedValueId;

                currentTotalModifier += window.attributeValueModifiers[selectedValueId] || 0;
            } else {
                allSelected = false;
            }
        });


        const finalPrice = window.productBasePrice + currentTotalModifier;
        priceElement.textContent = eShopUtils.formatPrice(finalPrice, window.currencySymbol);


        let foundVariation = null;
        let matchedVariationId = null;

        if (allSelected) {
            for (const variationId in window.productVariations) {
                const variation = window.productVariations[variationId];

                if (optionsMatch(selectedValueIds, variation.options)) {
                    foundVariation = variation;
                    matchedVariationId = variationId;
                    break;
                }
            }
        }


        if (foundVariation) {
            skuElement.textContent = foundVariation.sku || 'N/D';
            if (foundVariation.stock > 0) {
                stockElement.textContent = 'Em Stock';
                stockElement.className = 'badge bg-success';
                addToCartBtn.disabled = false;
            } else {
                stockElement.textContent = 'Esgotado';
                stockElement.className = 'badge bg-danger';
                addToCartBtn.disabled = true;
            }
            addToCartBtn.dataset.variationId = matchedVariationId;
            selectedVariationInput.value = matchedVariationId;
            variationErrorElement.style.display = 'none';



        } else {

            skuElement.textContent = 'N/D';
            stockElement.textContent = allSelected ? 'Indisponível' : 'Selecione as opções';
            stockElement.className = allSelected ? 'badge bg-danger' : 'badge bg-secondary';
            addToCartBtn.disabled = true;
            addToCartBtn.dataset.variationId = '';
            selectedVariationInput.value = '';
            if (variationErrorElement && allSelected) {
                variationErrorElement.style.display = 'block';
            } else {
                 variationErrorElement.style.display = 'none';
            }
        }
    }


    if (variationSelects.length > 0) {
        variationSelects.forEach(select => {
            select.addEventListener('change', updateVariationDetails);
        });

        updateVariationDetails();
    } else {

         updateVariationDetails();
    }


    const cartItemsContainer = document.querySelector('.col-lg-8 .card-body');


    function updateCartSummaryDisplay(subtotalFormatted, totalFormatted) {
        const subtotalEl = document.querySelector('#cart-subtotal');
        const summarySubtotalEl = document.querySelector('#summary-subtotal');
        const summaryTotalEl = document.querySelector('#summary-grand-total');


        if (subtotalEl) subtotalEl.textContent = subtotalFormatted;
        if (summarySubtotalEl) summarySubtotalEl.textContent = subtotalFormatted;
        if (summaryTotalEl) summaryTotalEl.textContent = totalFormatted;
    }


    async function handleQuantityChange(event) {
        const input = event.target;
        const itemKey = input.dataset.itemKey;
        const newQuantity = parseInt(input.value, 10);
        const maxStock = parseInt(input.max, 10);
        const originalQuantity = input.dataset.originalValue || input.value;

        if (!itemKey || isNaN(newQuantity) || newQuantity < 1) {
            input.value = 1;
            return;
        }
        if (newQuantity > maxStock) {
             alert(`Stock máximo é ${maxStock}.`);
             input.value = maxStock;

             return;
        }

        input.disabled = true;

        try {
            const response = await ajaxRequest('?action=update_cart_item', {
                method: 'POST',
                body: JSON.stringify({ item_key: itemKey, quantity: newQuantity })
            });

            if (response.success) {
                const cartItemRow = input.closest('.cart-item');
                const itemPrice = parseFloat(cartItemRow.querySelector('.item-price').textContent.replace(window.currencySymbol, '').replace(',', '.')) || 0;
                const itemTotalPriceElement = cartItemRow.querySelector('.item-total-price');
                if (itemTotalPriceElement) {
                    itemTotalPriceElement.textContent = eShopUtils.formatPrice(itemPrice * newQuantity, window.currencySymbol);
                }
                updateCartBadge(response.item_count);

                updateCartSummaryDisplay(response.subtotal_formatted, response.subtotal_formatted);
            } else {
                alert('Erro ao atualizar quantidade: ' + (response.message || 'Erro desconhecido'));
                input.value = originalQuantity;
            }
        } catch (error) {
            alert('Erro de comunicação ao atualizar carrinho.');
            console.error('Update cart error:', error);
            input.value = originalQuantity;
        } finally {
            input.disabled = false;
        }
    }


    async function handleRemoveItem(event) {
        const button = event.target.closest('.remove-from-cart-btn');
        if (!button) return;
        const itemKey = button.dataset.itemKey;
        if (!itemKey || !confirm('Tem a certeza que quer remover este item do carrinho?')) return;

        button.disabled = true;
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';

        try {
             const response = await ajaxRequest('?action=update_cart_item', {
                 method: 'POST',
                 body: JSON.stringify({ item_key: itemKey, quantity: 0 })
             });

             if (response.success && response.removed) {
                 const cartItemRow = button.closest('.cart-item');
                 if (cartItemRow) cartItemRow.remove();
                 updateCartBadge(response.item_count);
                 updateCartSummaryDisplay(response.subtotal_formatted, response.subtotal_formatted);

                 if (response.item_count === 0 && cartItemsContainer) {

                     const cartRowContainer = document.querySelector('.row');
                     if (cartRowContainer) {
                         cartRowContainer.innerHTML = '<div class="col-12"><div class="alert alert-info" role="alert">O seu carrinho está vazio. <a href="' + eShopUtils.addSessionParamToUrl(window.eshopBaseUrl + '/index.php') + '" class="alert-link">Continue a comprar</a>.</div></div>';
                     }
                 }
             } else {
                 alert('Erro ao remover item: ' + (response.message || 'Erro desconhecido'));
                 button.disabled = false;
                 button.innerHTML = '<i class="bi bi-trash"></i>';
             }
        } catch (error) {
             alert('Erro de comunicação ao remover item.');
             console.error('Remove cart item error:', error);
             button.disabled = false;
             button.innerHTML = '<i class="bi bi-trash"></i>';
        }

    }


    if (cartItemsContainer) {
        const debouncedQuantityHandler = eShopUtils.debounce(handleQuantityChange, 500);

        cartItemsContainer.addEventListener('change', (event) => {
            if (event.target.matches('.cart-item-qty')) {
                debouncedQuantityHandler(event);
            }
        });

        cartItemsContainer.addEventListener('click', (event) => {
             if (event.target.closest('.remove-from-cart-btn')) {
                 handleRemoveItem(event);
             }
         });


         cartItemsContainer.addEventListener('focusin', (event) => {
             if (event.target.matches('.cart-item-qty')) {
                 event.target.dataset.originalValue = event.target.value;
             }
         });
    }


    const mainImage = document.getElementById('main-product-image');

    const mainImageLightboxLink = mainImage ? mainImage.closest('a[data-lightbox="product-gallery"]') : null;
    const thumbnailsContainer = document.querySelector('.product-thumbnails');

    if (mainImage && mainImageLightboxLink && thumbnailsContainer) {

        thumbnailsContainer.addEventListener('click', (event) => {

            const thumbnailLink = event.target.closest('a.product-thumbnail-link');


            if (thumbnailLink && thumbnailsContainer.contains(thumbnailLink)) {


                event.preventDefault();


                const newImageUrl = thumbnailLink.dataset.previewUrl || thumbnailLink.href;
                const newImageTitle = thumbnailLink.dataset.title || '';


                mainImage.src = newImageUrl;
                mainImage.alt = newImageTitle;



                mainImageLightboxLink.href = newImageUrl;
                mainImageLightboxLink.dataset.title = newImageTitle;
            }


        });
    } else {
    }

});

function updatePaginationControls(paginationData, categoryId, sortOrder) {
    const paginationContainer = document.getElementById('pagination-container');
    if (!paginationContainer) return;

    const { currentPage, totalPages } = paginationData;

    if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }

    let paginationHtml = '<nav class="inline-flex rounded-md shadow-sm bg-gray-800" aria-label="Pagination">';


    const queryParams = new URLSearchParams();
    queryParams.set('view', 'home');
    if (categoryId && categoryId !== '0') {
        queryParams.set('category_id', categoryId);
    }
    if (sortOrder && sortOrder !== 'recent') {
        queryParams.set('sort', sortOrder);
    }

    if (window.eshopSessionId) {
        queryParams.set(window.eshopSessionParam || 'sid', window.eshopSessionId);
    }
    const baseUrl = `index.php?${queryParams.toString()}&page=`;


    const prevPage = currentPage - 1;
    let prevLinkClass = 'px-3 py-2 text-sm font-medium rounded-l-button ';
    let prevTag = 'a';
    let prevHref = baseUrl + prevPage;
    if (currentPage <= 1) {
        prevLinkClass += 'text-gray-600 bg-gray-700 cursor-not-allowed opacity-50';
        prevTag = 'span';
        prevHref = '#';
    } else {
        prevLinkClass += 'text-gray-300 hover:bg-gray-700';
    }
    paginationHtml += `<${prevTag} href="${prevHref}" class="${prevLinkClass}" ${prevTag === 'a' ? 'aria-label="Previous"' : ''}><i class="ri-arrow-left-s-line"></i></${prevTag}>`;



    for (let i = 1; i <= totalPages; i++) {
        let pageLinkClass = 'px-4 py-2 text-sm font-medium ';
        let pageTag = 'a';
        let pageHref = baseUrl + i;
        let ariaCurrent = '';
        if (i === currentPage) {
            pageLinkClass += 'text-white bg-primary z-10 cursor-default';
            pageTag = 'span';
            ariaCurrent = 'aria-current="page"';
            pageHref = '#';
        } else {
            pageLinkClass += 'text-gray-300 hover:bg-gray-700';
        }
        paginationHtml += `<${pageTag} href="${pageHref}" class="${pageLinkClass}" ${ariaCurrent}>${i}</${pageTag}>`;
    }


    const nextPage = currentPage + 1;
    let nextLinkClass = 'px-3 py-2 text-sm font-medium rounded-r-button ';
    let nextTag = 'a';
    let nextHref = baseUrl + nextPage;
    if (currentPage >= totalPages) {
        nextLinkClass += 'text-gray-600 bg-gray-700 cursor-not-allowed opacity-50';
        nextTag = 'span';
        nextHref = '#';
    } else {
        nextLinkClass += 'text-gray-300 hover:bg-gray-700';
    }
    paginationHtml += `<${nextTag} href="${nextHref}" class="${nextLinkClass}" ${nextTag === 'a' ? 'aria-label="Next"' : ''}><i class="ri-arrow-right-s-line"></i></${nextTag}>`;

    paginationHtml += '</nav>';
    paginationContainer.innerHTML = paginationHtml;
}

document.addEventListener('DOMContentLoaded', () => {
    const sortSelect = document.getElementById('product-sort-select');
    const categoryButtonsContainer = document.querySelector('.flex.items-center.space-x-2.overflow-x-auto');
    const productGrid = document.getElementById('products-grid');

    let currentCategoryId = '0';
    let currentSortOrder = 'recent';


    async function fetchAndUpdateProducts(categoryId, sortOrder) {
        if (!productGrid) return;

        currentCategoryId = categoryId;
        currentSortOrder = sortOrder;


        productGrid.innerHTML = `<div class="col-span-full text-center py-12">
            <div class="w-12 h-12 mx-auto mb-4 border-4 border-gray-700 border-t-primary rounded-full animate-spin"></div>
            <p class="text-lg text-gray-400">Carregando produtos...</p>
        </div>`;

        try {

            const url = `?action=get_products&category_id=${categoryId}&sort=${sortOrder}`;
             const response = await ajaxRequest(url);

             if (response && response.success && Array.isArray(response.products)) {
                  updateProductGrid(response.products, response.config || {});

                  if (response.pagination) {
                      updatePaginationControls(response.pagination, currentCategoryId, currentSortOrder);
                  } else {

                      const paginationContainer = document.getElementById('pagination-container');
                      if (paginationContainer) paginationContainer.innerHTML = '';
                  }
             } else {
                  console.error('Failed to fetch products:', response?.message || 'Unknown error');


                  const logoUrl = window.storeLogo || '';
                  const logoHtml = logoUrl
                      ? `<img src="${logoUrl}" alt="Store Logo" class="w-12 h-12 sm:w-16 sm:h-16 object-contain">`
                      : `<i class="ri-store-2-line text-3xl sm:text-4xl text-gray-400"></i>`;

                  productGrid.innerHTML = `<div class="col-span-full text-center py-12">
                      <div class="w-20 h-20 sm:w-24 sm:h-24 mx-auto mb-4 sm:mb-6 flex items-center justify-center bg-gray-800 rounded-full overflow-hidden">
                          ${logoHtml}
                      </div>
                      <p class="text-lg sm:text-xl text-red-400">Erro ao carregar produtos.</p>
                      <p class="text-sm sm:text-base text-gray-500">Por favor, tente novamente mais tarde.</p>
                  </div>`;


                  const paginationContainer = document.getElementById('pagination-container');
                  if (paginationContainer) paginationContainer.innerHTML = '';
            }
        } catch (error) {
            console.error('Error during AJAX product fetch:', error);


            const logoUrl = window.storeLogo || '';
            const logoHtml = logoUrl
                ? `<img src="${logoUrl}" alt="Store Logo" class="w-12 h-12 sm:w-16 sm:h-16 object-contain">`
                : `<i class="ri-store-2-line text-3xl sm:text-4xl text-gray-400"></i>`;

            productGrid.innerHTML = `<div class="col-span-full text-center py-12">
                <div class="w-20 h-20 sm:w-24 sm:h-24 mx-auto mb-4 sm:mb-6 flex items-center justify-center bg-gray-800 rounded-full overflow-hidden">
                    ${logoHtml}
                </div>
                <p class="text-lg sm:text-xl text-red-400">Erro ao carregar produtos.</p>
                <p class="text-sm sm:text-base text-gray-500">Por favor, tente novamente mais tarde.</p>
            </div>`;
        }
    }


    function updateProductGrid(products, config) {
        if (!productGrid) return;
        productGrid.innerHTML = '';

        const currencySymbol = config.currencySymbol || window.currencySymbol || '€';
        const baseUrl = config.baseUrl || window.eshopBaseUrl || '';

        if (products.length === 0) {

            const logoUrl = window.storeLogo || '';
            const logoHtml = logoUrl
                ? `<img src="${logoUrl}" alt="Store Logo" class="w-12 h-12 sm:w-16 sm:h-16 object-contain">`
                : `<i class="ri-store-2-line text-3xl sm:text-4xl text-gray-400"></i>`;

            productGrid.innerHTML = `<div class="col-span-full text-center py-12">
                <div class="w-20 h-20 sm:w-24 sm:h-24 mx-auto mb-4 sm:mb-6 flex items-center justify-center bg-gray-800 rounded-full overflow-hidden">
                    ${logoHtml}
                </div>
                <p class="text-lg sm:text-xl text-gray-400">Não foram encontrados produtos.</p>
                <p class="text-sm sm:text-base text-gray-500">Tente ajustar os filtros ou volte mais tarde.</p>
            </div>`;
            return;
        }

        products.forEach(product => {

            const productUrl = product.product_url || `${baseUrl}/index.php?page=product&slug=${product.slug}`;
            const imageUrl = product.display_image_url || '';
            const priceFormatted = product.price_formatted || `${currencySymbol}${Number(product.base_price || 0).toFixed(2).replace('.', ',')}`;
            const productName = product.name_pt || 'Product Name Missing';

            const productHtml = `
            <div class="product-card bg-gray-900 rounded-lg overflow-hidden group flex flex-col">
                <div class="relative overflow-hidden aspect-square">
                    <a href="${productUrl}" class="block w-full h-full">
                        ${imageUrl ? `<img src="${imageUrl}" alt="${productName}" class="w-full h-full object-cover product-image transition-transform duration-300 group-hover:scale-105">` : '<div class="w-full h-full bg-gray-800 flex items-center justify-center"><i class="ri-image-line text-4xl text-gray-500"></i></div>'}
                    </a>

                </div>
                <div class="p-4 flex flex-col flex-grow">
                    <h3 class="text-lg font-medium mb-1 truncate flex-grow">
                         <a href="${productUrl}" class="hover:text-primary transition" title="${productName}">
                            ${productName}
                         </a>
                    </h3>
                    <div class="flex items-center justify-between mt-2">
                        <span class="text-lg font-semibold">${priceFormatted}</span>
                        <button class="add-to-cart-btn p-2 bg-gray-800 rounded-full hover:bg-primary transition text-white"
                            data-product-id="${product.id}"
                            data-product-type="${product.product_type || 'regular'}"
                            aria-label="Adicionar ${productName} ao carrinho">
                            <div class="w-5 h-5 flex items-center justify-center">
                                <i class="ri-shopping-cart-line"></i>
                            </div>
                        </button>
                        <!-- Note: Missing data-variation-id if needed for direct add from grid -->
                    </div>
                </div>
            </div>`;
            productGrid.insertAdjacentHTML('beforeend', productHtml);
        });
    }


    if (categoryButtonsContainer) {
        categoryButtonsContainer.addEventListener('click', function(event) {
            const button = event.target.closest('.category-filter-btn');
            if (button && !button.classList.contains('active-category')) {
                const categoryId = button.dataset.categoryId;


                categoryButtonsContainer.querySelector('.active-category')?.classList.remove('active-category', 'bg-primary');
                categoryButtonsContainer.querySelector('.active-category')?.classList.add('bg-gray-800', 'hover:bg-gray-700');
                button.classList.add('active-category', 'bg-primary');
                button.classList.remove('bg-gray-800', 'hover:bg-gray-700');


                fetchAndUpdateProducts(categoryId, currentSortOrder);
            }
        });
    }


    if (sortSelect) {

        const urlParams = new URLSearchParams(window.location.search);
        currentSortOrder = urlParams.get('sort') || sortSelect.value || 'recent';
        sortSelect.value = currentSortOrder;

        sortSelect.addEventListener('change', function() {
            const selectedSort = this.value;

            fetchAndUpdateProducts(currentCategoryId, selectedSort);


            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('sort', selectedSort);

            if (currentCategoryId !== '0') {
                 currentUrl.searchParams.set('category_id', currentCategoryId);
            } else {
                 currentUrl.searchParams.delete('category_id');
            }
            history.pushState({}, '', currentUrl);
        });
    }



    const urlParams = new URLSearchParams(window.location.search);
    const initialSort = urlParams.get('sort');
    const initialCategory = urlParams.get('category_id');

    if (initialSort || initialCategory) {
        currentSortOrder = initialSort || currentSortOrder;
        currentCategoryId = initialCategory || currentCategoryId;
        sortSelect.value = currentSortOrder;


        const activeButton = categoryButtonsContainer?.querySelector(`.category-filter-btn[data-category-id="${currentCategoryId}"]`);
        if (activeButton) {
            categoryButtonsContainer.querySelector('.active-category')?.classList.remove('active-category', 'bg-primary');
             categoryButtonsContainer.querySelector('.active-category')?.classList.add('bg-gray-800', 'hover:bg-gray-700');
            activeButton.classList.add('active-category', 'bg-primary');
             activeButton.classList.remove('bg-gray-800', 'hover:bg-gray-700');
        }


    }

});
