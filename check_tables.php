<?php
// This script checks if the necessary tables exist and creates them if they don't

// Include necessary files
require_once __DIR__ . '/includes/init.php';

// Check if user is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die("Not authorized");
}

// Set content type to HTML
header('Content-Type: text/html; charset=utf-8');

// Function to check if a table exists
function table_exists($table_name) {
    $pdo = get_db_connection();
    if (!$pdo) return false;
    
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='$table_name'");
    return $stmt->fetch() !== false;
}

// Function to create the digital_file_type_associations table
function create_digital_file_type_associations_table() {
    $pdo = get_db_connection();
    if (!$pdo) return false;
    
    try {
        $sql = "CREATE TABLE IF NOT EXISTS digital_file_type_associations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            digital_file_id INTEGER NOT NULL,
            file_type_id INTEGER NOT NULL,
            created_at TEXT NOT NULL,
            UNIQUE(digital_file_id, file_type_id)
        )";
        
        $pdo->exec($sql);
        return true;
    } catch (Exception $e) {
        error_log("Error creating digital_file_type_associations table: " . $e->getMessage());
        return false;
    }
}

// Function to create the digital_product_file_types table
function create_digital_product_file_types_table() {
    $pdo = get_db_connection();
    if (!$pdo) return false;
    
    try {
        $sql = "CREATE TABLE IF NOT EXISTS digital_product_file_types (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            extension TEXT NOT NULL,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL
        )";
        
        $pdo->exec($sql);
        return true;
    } catch (Exception $e) {
        error_log("Error creating digital_product_file_types table: " . $e->getMessage());
        return false;
    }
}

// Function to create the digital_product_file_type_associations table
function create_digital_product_file_type_associations_table() {
    $pdo = get_db_connection();
    if (!$pdo) return false;
    
    try {
        $sql = "CREATE TABLE IF NOT EXISTS digital_product_file_type_associations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            digital_product_id INTEGER NOT NULL,
            file_type_id INTEGER NOT NULL,
            created_at TEXT NOT NULL,
            UNIQUE(digital_product_id, file_type_id)
        )";
        
        $pdo->exec($sql);
        return true;
    } catch (Exception $e) {
        error_log("Error creating digital_product_file_type_associations table: " . $e->getMessage());
        return false;
    }
}

// Function to add default file types if none exist
function add_default_file_types() {
    $pdo = get_db_connection();
    if (!$pdo) return false;
    
    try {
        // Check if any file types exist
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM digital_product_file_types");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result && (int)$result['count'] > 0) {
            return true; // File types already exist
        }
        
        // Add default file types
        $default_types = [
            ['name' => 'Adobe Affinity Designer', 'extension' => 'afdesign'],
            ['name' => 'Adobe Photoshop', 'extension' => 'psd'],
            ['name' => 'CorelDRAW', 'extension' => 'cdr'],
            ['name' => 'DOC/DOCX', 'extension' => 'docx'],
            ['name' => 'EPUB', 'extension' => 'epub'],
            ['name' => 'Encapsulated PostScript', 'extension' => 'eps'],
            ['name' => 'JPEG Image', 'extension' => 'jpg'],
            ['name' => 'JPG/JPEG', 'extension' => 'jpg'],
            ['name' => 'MP3', 'extension' => 'mp3'],
            ['name' => 'MP4', 'extension' => 'mp4'],
            ['name' => 'PDF', 'extension' => 'pdf'],
            ['name' => 'PNG', 'extension' => 'png'],
            ['name' => 'PNG Image', 'extension' => 'png'],
            ['name' => 'PPT/PPTX', 'extension' => 'pptx'],
            ['name' => 'Scalable Vector Graphics', 'extension' => 'svg'],
            ['name' => 'Serif Draw Plus', 'extension' => 'dpp'],
            ['name' => 'XLS/XLSX', 'extension' => 'xlsx'],
            ['name' => 'ZIP', 'extension' => 'zip']
        ];
        
        $pdo->beginTransaction();
        
        foreach ($default_types as $type) {
            $sql = "INSERT INTO digital_product_file_types (
                        name, extension, created_at, updated_at
                    ) VALUES (
                        :name, :extension, datetime('now', 'localtime'), datetime('now', 'localtime')
                    )";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                ':name' => $type['name'],
                ':extension' => $type['extension']
            ]);
        }
        
        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Error adding default file types: " . $e->getMessage());
        return false;
    }
}

// Check and create tables
$results = [
    'digital_file_type_associations' => [
        'exists' => table_exists('digital_file_type_associations'),
        'created' => false
    ],
    'digital_product_file_types' => [
        'exists' => table_exists('digital_product_file_types'),
        'created' => false
    ],
    'digital_product_file_type_associations' => [
        'exists' => table_exists('digital_product_file_type_associations'),
        'created' => false
    ],
    'default_file_types_added' => false
];

if (!$results['digital_file_type_associations']['exists']) {
    $results['digital_file_type_associations']['created'] = create_digital_file_type_associations_table();
}

if (!$results['digital_product_file_types']['exists']) {
    $results['digital_product_file_types']['created'] = create_digital_product_file_types_table();
}

if (!$results['digital_product_file_type_associations']['exists']) {
    $results['digital_product_file_type_associations']['created'] = create_digital_product_file_type_associations_table();
}

// Add default file types if the table was just created or if it's empty
if ($results['digital_product_file_types']['created'] || 
    ($results['digital_product_file_types']['exists'] && !$results['default_file_types_added'])) {
    $results['default_file_types_added'] = add_default_file_types();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Tables</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .results { margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Tables Check</h1>
        
        <div class="results">
            <h2>Results</h2>
            
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Table</th>
                        <th>Exists</th>
                        <th>Created</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>digital_file_type_associations</td>
                        <td><?= $results['digital_file_type_associations']['exists'] ? 'Yes' : 'No' ?></td>
                        <td><?= $results['digital_file_type_associations']['created'] ? 'Yes' : 'No' ?></td>
                    </tr>
                    <tr>
                        <td>digital_product_file_types</td>
                        <td><?= $results['digital_product_file_types']['exists'] ? 'Yes' : 'No' ?></td>
                        <td><?= $results['digital_product_file_types']['created'] ? 'Yes' : 'No' ?></td>
                    </tr>
                    <tr>
                        <td>digital_product_file_type_associations</td>
                        <td><?= $results['digital_product_file_type_associations']['exists'] ? 'Yes' : 'No' ?></td>
                        <td><?= $results['digital_product_file_type_associations']['created'] ? 'Yes' : 'No' ?></td>
                    </tr>
                </tbody>
            </table>
            
            <div class="alert <?= $results['default_file_types_added'] ? 'alert-success' : 'alert-info' ?>">
                Default file types: <?= $results['default_file_types_added'] ? 'Added' : 'Not needed' ?>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="fix_file_type_associations.php" class="btn btn-primary">Fix File Type Associations</a>
            <a href="debug_file_types.php" class="btn btn-secondary">View Debug Info</a>
            <a href="admin.php?section=digital_products" class="btn btn-primary">Back to Digital Products</a>
        </div>
    </div>
</body>
</html>
