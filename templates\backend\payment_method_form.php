<?php

$is_edit_mode = isset($payment_method) && !empty($payment_method);
$form_title = $is_edit_mode ? 'Editar Método de Pagamento' : 'Novo Método de Pagamento';

$title = $form_data['title'] ?? ($payment_method['title'] ?? '');
$instructions = $form_data['instructions'] ?? ($payment_method['instructions'] ?? '');
$is_active = $form_data['is_active'] ?? ($payment_method['is_active'] ?? 1);
$sort_order = $form_data['sort_order'] ?? ($payment_method['sort_order'] ?? 0);
?>

<h1><?= $form_title ?></h1>

<a href="admin.php?section=payment_methods&<?= get_session_id_param() ?>" class="btn btn-secondary mb-3">
    <i class="bi bi-arrow-left"></i> Voltar à Lista
</a>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?= $error_message ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-body">
        <form method="POST" action="admin.php">
            <?= csrf_input_field() ?>
            <input type="hidden" name="section" value="payment_methods">
            <input type="hidden" name="action" value="<?= $is_edit_mode ? 'update' : 'new' ?>">
            <?php if ($is_edit_mode): ?>
                <input type="hidden" name="id" value="<?= $payment_method['id'] ?>">
            <?php endif; ?>
            <input type="hidden" name="<?= get_session_id_param(false) ?>" value="<?= session_id() ?>">
            <div class="mb-3">
                <label for="title" class="form-label">Título *</label>
                <input type="text" class="form-control" id="title" name="title" value="<?= sanitize_input($title) ?>" required>
                <div class="form-text">Nome do método de pagamento (ex: Transferência Bancária, MBWay)</div>
                <?php if (isset($errors['title'])): ?>
                    <div class="text-danger"><?= $errors['title'] ?></div>
                <?php endif; ?>
            </div>

            <div class="mb-3">
                <label for="instructions" class="form-label">Instruções de Pagamento *</label>
                <textarea class="form-control" id="instructions" name="instructions" rows="6" required><?= sanitize_input($instructions) ?></textarea>
                <div class="form-text">Instruções detalhadas que serão enviadas ao cliente por email. Pode incluir detalhes bancários, números de telefone para MBWay, etc.</div>
                <?php if (isset($errors['instructions'])): ?>
                    <div class="text-danger"><?= $errors['instructions'] ?></div>
                <?php endif; ?>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="is_active" class="form-label">Estado</label>
                    <select class="form-select" id="is_active" name="is_active">
                        <option value="1" <?= $is_active == 1 ? 'selected' : '' ?>>Ativo</option>
                        <option value="0" <?= $is_active == 0 ? 'selected' : '' ?>>Inativo</option>
                    </select>
                </div>

                <div class="col-md-6">
                    <label for="sort_order" class="form-label">Ordem de Exibição</label>
                    <input type="number" class="form-control" id="sort_order" name="sort_order" value="<?= sanitize_input($sort_order) ?>">
                    <div class="form-text">Números menores aparecem primeiro</div>
                </div>
            </div>

            <div class="d-flex justify-content-between">
                <a href="admin.php?section=payment_methods&<?= get_session_id_param() ?>" class="btn btn-secondary">Cancelar</a>
                <button type="submit" class="btn btn-primary">Guardar</button>
            </div>
        </form>
    </div>
</div>
