

document.addEventListener('DOMContentLoaded', function() {

    
    const decreaseButtons = document.querySelectorAll('.decrease-qty-btn');
    const increaseButtons = document.querySelectorAll('.increase-qty-btn');
    const quantityInputs = document.querySelectorAll('.cart-item-qty');
    const removeButtons = document.querySelectorAll('.remove-item-btn');

    
    async function updateCartItemQuantity(cartKey, newQuantity) {

        
        const loadingToast = typeof showToast === 'function'
            ? showToast('Processando', 'Atualizando quantidade...', 'ri-loader-4-line animate-spin', 'border-blue-500')
            : null;

        try {
            const formData = new FormData();
            formData.append('cart_key', cartKey);
            formData.append('quantity', newQuantity);

            
            const response = await fetch(`${window.eshopBaseUrl || ''}/index.php?action=update_cart_item`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-Session-UUID': window.eshopSessionId || ''
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            

            
            if (loadingToast) {
                loadingToast.close();
            }

            if (data.success) {
                
                
                const input = document.querySelector(`.cart-item-qty[data-cart-key="${cartKey}"]`);
                if (input) {
                    input.value = newQuantity;
                }

                
                const totalItemElement = document.querySelector(`[data-cart-key="${cartKey}"] .item-total-price-display`);
                if (totalItemElement && data.item_price) {
                    totalItemElement.textContent = data.item_price;
                }

                
                updateCartSummary(data);

                
                updateCartBadge(data.item_count);
            } else {
                
                if (typeof showToast === 'function') {
                    showToast('Erro', data.error || 'Não foi possível atualizar a quantidade.', 'ri-error-warning-line', 'border-red-500');
                } else {
                    alert('Erro: ' + (data.error || 'Não foi possível atualizar a quantidade.'));
                }
            }
        } catch (error) {
            console.error('Error updating quantity:', error);

            
            if (loadingToast) {
                loadingToast.close();
            }

            
            if (typeof showToast === 'function') {
                showToast('Erro', 'Ocorreu um problema de rede.', 'ri-error-warning-line', 'border-red-500');
            } else {
                alert('Erro: Ocorreu um problema de rede.');
            }
        }
    }

    
    async function removeCartItem(cartKey) {

        
        const loadingToast = typeof showToast === 'function'
            ? showToast('Processando', 'Removendo item...', 'ri-loader-4-line animate-spin', 'border-blue-500')
            : null;

        try {
            const formData = new FormData();
            formData.append('cart_key', cartKey);
            formData.append('quantity', 0);

            
            const response = await fetch(`${window.eshopBaseUrl || ''}/index.php?action=update_cart_item`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-Session-UUID': window.eshopSessionId || ''
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            
            if (loadingToast) {
                loadingToast.close();
            }

            if (data.success) {
                
                const itemRow = document.querySelector(`.cart-item-row[data-cart-key="${cartKey}"]`);
                if (itemRow) {
                    itemRow.remove();
                }

                
                updateCartSummary(data);

                
                updateCartBadge(data.item_count);

                
                if (typeof showToast === 'function') {
                    showToast('Sucesso', 'Item removido do carrinho.', 'ri-check-line', 'border-green-500');
                }

                
                if (data.item_count === 0) {
                    document.getElementById('cartItemsContainer').classList.add('hidden');
                    document.getElementById('emptyCartMessage').classList.remove('hidden');
                    document.getElementById('orderSummary').classList.add('hidden');
                    document.getElementById('cartActionButtons').classList.add('hidden');
                }
            } else {
                
                if (typeof showToast === 'function') {
                    showToast('Erro', data.error || 'Não foi possível remover o item.', 'ri-error-warning-line', 'border-red-500');
                } else {
                    alert('Erro: ' + (data.error || 'Não foi possível remover o item.'));
                }
            }
        } catch (error) {
            console.error('Error removing item:', error);

            
            if (loadingToast) {
                loadingToast.close();
            }

            
            if (typeof showToast === 'function') {
                showToast('Erro', 'Ocorreu um problema de rede.', 'ri-error-warning-line', 'border-red-500');
            } else {
                alert('Erro: Ocorreu um problema de rede.');
            }
        }
    }

    
    function updateCartSummary(data) {
        
        const subtotalElement = document.getElementById('summary-subtotal');
        if (subtotalElement && data.subtotal_formatted) {
            subtotalElement.textContent = data.subtotal_formatted;
        }

        
        const taxElement = document.getElementById('summary-tax');
        if (taxElement && data.tax !== undefined) {
            taxElement.textContent = data.tax_formatted || formatPrice(data.tax);
        }

        
        const shippingElement = document.getElementById('summary-shipping');
        if (shippingElement) {
            shippingElement.textContent = data.shipping > 0 ? (data.shipping_formatted || formatPrice(data.shipping)) : 'Grátis';
        }

        
        const discountRow = document.getElementById('summary-discount-row');
        const discountElement = document.getElementById('summary-discount-amount');
        if (discountRow && discountElement) {
            if (data.discount > 0) {
                discountElement.textContent = '-' + (data.discount_formatted || formatPrice(data.discount));
                discountRow.classList.remove('hidden');
            } else {
                discountRow.classList.add('hidden');
            }
        }

        
        const totalElement = document.getElementById('summary-total');
        if (totalElement && data.total_formatted) {
            totalElement.textContent = data.total_formatted;
        }

        
        const cartCountElement = document.getElementById('cart-page-item-count');
        if (cartCountElement && data.item_count !== undefined) {
            cartCountElement.textContent = data.item_count;
        }

        
        if (data.min_order_value > 0) {
            const minOrderElement = document.querySelector('[data-min-order-indicator]');
            const minOrderProgressBar = document.querySelector('[data-min-order-progress]');
            const minOrderRemainingText = document.querySelector('[data-min-order-remaining]');

            if (minOrderElement && minOrderProgressBar && minOrderRemainingText) {
                const meetMinOrder = data.meets_min_order || (data.grand_total >= data.min_order_value);
                
                
                
                

                
                minOrderElement.className = meetMinOrder ? 'mt-3 text-green-500' : 'mt-3 text-yellow-500';

                
                const iconElementMinOrder = minOrderElement.querySelector('i'); 
                if (iconElementMinOrder) {
                    iconElementMinOrder.className = meetMinOrder ? 'ri-checkbox-circle-fill mr-1' : 'ri-error-warning-fill mr-1';
                }

                
                if (!meetMinOrder) {
                    const percentage = Math.min(100, (data.grand_total / data.min_order_value) * 100);
                    minOrderProgressBar.style.width = `${percentage}%`;
                    minOrderProgressBar.className = 'bg-yellow-500 h-1.5 rounded-full';

                    const remaining = data.min_order_value - data.grand_total;
                    
                    minOrderRemainingText.textContent = `Faltam ${formatPrice(remaining)} para atingir o valor mínimo`;
                } else {
                    minOrderProgressBar.style.width = '100%';
                    minOrderProgressBar.className = 'bg-green-500 h-1.5 rounded-full';
                    
                    minOrderRemainingText.textContent = 'Valor mínimo para checkout atingido!';
                }
            } else {
                
            }
        }

        
        if (data.free_shipping_threshold > 0) {
            const freeShippingElement = document.querySelector('[data-free-shipping-indicator]');
            const freeShippingProgressBar = document.querySelector('[data-free-shipping-progress]');
            const freeShippingRemainingText = document.querySelector('[data-free-shipping-remaining]');

            if (freeShippingElement && freeShippingProgressBar && freeShippingRemainingText) {
                const hasFreeShipping = data.has_free_shipping || (data.subtotal >= data.free_shipping_threshold);
                
                
                
                

                
                freeShippingElement.className = hasFreeShipping ? 'mt-3 text-green-500' : 'mt-3 text-gray-400';

                
                const iconElementFreeShipping = freeShippingElement.querySelector('i'); 
                if (iconElementFreeShipping) {
                    iconElementFreeShipping.className = hasFreeShipping ? 'ri-checkbox-circle-fill mr-1' : 'ri-truck-line mr-1';
                }

                
                if (!hasFreeShipping) {
                    const percentage = Math.min(100, (data.subtotal / data.free_shipping_threshold) * 100);
                    freeShippingProgressBar.style.width = `${percentage}%`;
                    freeShippingProgressBar.className = 'bg-primary h-1.5 rounded-full';

                    const remaining = data.free_shipping_threshold - data.subtotal;
                    
                    freeShippingRemainingText.textContent = `Faltam ${formatPrice(remaining)} para envio gratuito`;
                } else {
                    freeShippingProgressBar.style.width = '100%';
                    freeShippingProgressBar.className = 'bg-green-500 h-1.5 rounded-full';
                    
                    freeShippingRemainingText.textContent = 'Portes gratuitos desbloqueados!';
                }
            } else {
                
            }
        }
    }

    
    function formatPrice(amount, symbol = '€') {
        
        return symbol + parseFloat(amount).toFixed(2).replace('.', ',');
    }

    
    function updateCartBadge(count) {
        const badge = document.getElementById('cart-item-count');
        if (badge) {
            badge.textContent = count;
            badge.style.display = count > 0 ? 'flex' : 'none';
        }

        
        const cartPreviewCount = document.getElementById('cart-preview-count');
        if (cartPreviewCount) {
            cartPreviewCount.textContent = count;
        }

        
        localStorage.setItem('eshop_cart_count', count);
    }

    
    decreaseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const cartKey = this.getAttribute('data-cart-key');
            const input = document.querySelector(`.cart-item-qty[data-cart-key="${cartKey}"]`);
            if (input) {
                const currentValue = parseInt(input.value);
                if (currentValue > 1) {
                    updateCartItemQuantity(cartKey, currentValue - 1);
                }
            }
        });
    });

    
    increaseButtons.forEach(button => {
        button.addEventListener('click', function() {
            const cartKey = this.getAttribute('data-cart-key');
            const input = document.querySelector(`.cart-item-qty[data-cart-key="${cartKey}"]`);
            if (input) {
                
                const itemRow = document.querySelector(`.cart-item-row[data-cart-key="${cartKey}"]`);
                const isDigital = itemRow ? itemRow.querySelector('.bg-blue-900.text-blue-200') !== null : false;

                
                if (isDigital && parseInt(input.value) >= 1) {
                    if (typeof showToast === 'function') {
                        showToast('Aviso', 'Produtos digitais são limitados a 1 unidade por pedido.', 'ri-error-warning-line', 'border-yellow-500');
                    } else {
                        alert('Aviso: Produtos digitais são limitados a 1 unidade por pedido.');
                    }
                    return; 
                }

                const currentValue = parseInt(input.value);
                const maxValue = parseInt(input.getAttribute('max') || '9999');
                if (currentValue < maxValue) {
                    updateCartItemQuantity(cartKey, currentValue + 1);
                } else {
                    if (typeof showToast === 'function') {
                        showToast('Aviso', 'Quantidade máxima em stock atingida.', 'ri-error-warning-line', 'border-yellow-500');
                    } else {
                        alert('Aviso: Quantidade máxima em stock atingida.');
                    }
                }
            }
        });
    });

    
    quantityInputs.forEach(input => {
        input.addEventListener('change', function() {
            const cartKey = this.getAttribute('data-cart-key');
            const newValue = parseInt(this.value);
            const minValue = parseInt(this.getAttribute('min') || '1');
            const maxValue = parseInt(this.getAttribute('max') || '9999');

            
            if (isNaN(newValue) || newValue < minValue) {
                this.value = minValue;
                updateCartItemQuantity(cartKey, minValue);
            } else if (newValue > maxValue) {
                this.value = maxValue;
                updateCartItemQuantity(cartKey, maxValue);
                if (typeof showToast === 'function') {
                    showToast('Aviso', 'Quantidade máxima em stock atingida.', 'ri-error-warning-line', 'border-yellow-500');
                } else {
                    alert('Aviso: Quantidade máxima em stock atingida.');
                }
            } else {
                updateCartItemQuantity(cartKey, newValue);
            }
        });
    });

    
    removeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const cartKey = this.getAttribute('data-cart-key');

            
            const confirmModal = document.getElementById('confirmModal');
            const confirmRemoveBtn = document.getElementById('confirmRemove');
            const cancelRemoveBtn = document.getElementById('cancelRemove');

            if (confirmModal) {
                
                confirmModal.classList.remove('hidden');

                
                confirmRemoveBtn.onclick = function() {
                    confirmModal.classList.add('hidden');
                    removeCartItem(cartKey);
                };

                
                cancelRemoveBtn.onclick = function() {
                    confirmModal.classList.add('hidden');
                };
            } else {
                
                if (confirm('Tem a certeza que quer remover este item do carrinho?')) {
                    removeCartItem(cartKey);
                }
            }
        });
    });

    
    const clearCartBtn = document.getElementById('clearCartBtn');
    if (clearCartBtn) {
        clearCartBtn.addEventListener('click', function() {
            
            const clearCartModal = document.getElementById('clearCartModal');
            const confirmClearBtn = document.getElementById('confirmClear');
            const cancelClearBtn = document.getElementById('cancelClear');

            if (clearCartModal) {
                
                clearCartModal.classList.remove('hidden');

                
                confirmClearBtn.onclick = async function() {
                    clearCartModal.classList.add('hidden');

                    
                    const loadingToast = typeof showToast === 'function'
                        ? showToast('Processando', 'Limpando carrinho...', 'ri-loader-4-line animate-spin', 'border-blue-500')
                        : null;

                    try {
                        
                        const response = await fetch(`${window.eshopBaseUrl || ''}/index.php?action=clear_cart`, {
                            method: 'POST',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest',
                                'X-Session-UUID': window.eshopSessionId || ''
                            }
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const data = await response.json();

                        
                        if (loadingToast) {
                            loadingToast.close();
                        }

                        if (data.success) {
                            
                            document.getElementById('cartItemsContainer').innerHTML = '';

                            
                            document.getElementById('cartItemsContainer').classList.add('hidden');
                            document.getElementById('emptyCartMessage').classList.remove('hidden');
                            document.getElementById('orderSummary').classList.add('hidden');
                            document.getElementById('cartActionButtons').classList.add('hidden');

                            
                            updateCartBadge(0);

                            
                            if (typeof showToast === 'function') {
                                showToast('Sucesso', 'Carrinho limpo com sucesso.', 'ri-check-line', 'border-green-500');
                            } else {
                                alert('Carrinho limpo com sucesso.');
                            }
                        } else {
                            
                            if (typeof showToast === 'function') {
                                showToast('Erro', data.error || 'Não foi possível limpar o carrinho.', 'ri-error-warning-line', 'border-red-500');
                            } else {
                                alert('Erro: ' + (data.error || 'Não foi possível limpar o carrinho.'));
                            }
                        }
                    } catch (error) {
                        console.error('Error clearing cart:', error);

                        
                        if (loadingToast) {
                            loadingToast.close();
                        }

                        
                        if (typeof showToast === 'function') {
                            showToast('Erro', 'Ocorreu um problema de rede.', 'ri-error-warning-line', 'border-red-500');
                        } else {
                            alert('Erro: Ocorreu um problema de rede.');
                        }
                    }
                };

                
                cancelClearBtn.onclick = function() {
                    clearCartModal.classList.add('hidden');
                };
            } else {
                
                if (confirm('Tem a certeza que quer limpar o carrinho?')) {
                    
                }
            }
        });
    }

    
    const applyPromoBtn = document.getElementById('applyPromoBtn');
    const promoInput = document.getElementById('promoCodeInput');
    const promoMessage = document.getElementById('promoMessage');

    if (applyPromoBtn && promoInput) {
        applyPromoBtn.addEventListener('click', async function() {
            const code = promoInput.value.trim();
            if (!code) {
                if (promoMessage) {
                    promoMessage.textContent = 'Por favor, insira um código.';
                    promoMessage.className = 'mt-2 text-sm text-yellow-400';
                }
                return;
            }

            
            const loadingToast = typeof showToast === 'function'
                ? showToast('Processando', 'Aplicando código promocional...', 'ri-loader-4-line animate-spin', 'border-blue-500')
                : null;

            try {
                const formData = new FormData();
                formData.append('promo_code', code);

                
                const response = await fetch(`${window.eshopBaseUrl || ''}/index.php?action=apply_promo_code`, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-Session-UUID': window.eshopSessionId || ''
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                
                if (loadingToast) {
                    loadingToast.close();
                }

                if (data.success) {
                    
                    if (promoMessage) {
                        promoMessage.textContent = data.message || 'Código promocional aplicado!';
                        promoMessage.className = 'mt-2 text-sm text-green-400';
                    }

                    
                    if (typeof showToast === 'function') {
                        showToast('Sucesso', 'Código promocional aplicado! Atualizando página...', 'ri-check-line', 'border-green-500');
                    }

                    
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    
                    if (promoMessage) {
                        promoMessage.textContent = data.error || 'Código promocional inválido ou expirado.';
                        promoMessage.className = 'mt-2 text-sm text-red-400';
                    }

                    
                    if (typeof showToast === 'function') {
                        showToast('Erro', data.error || 'Código promocional inválido ou expirado.', 'ri-error-warning-line', 'border-red-500');
                    }
                }
            } catch (error) {
                console.error('Error applying promo code:', error);

                
                if (loadingToast) {
                    loadingToast.close();
                }

                
                if (promoMessage) {
                    promoMessage.textContent = 'Erro de rede ao aplicar código.';
                    promoMessage.className = 'mt-2 text-sm text-red-400';
                }

                
                if (typeof showToast === 'function') {
                    showToast('Erro', 'Ocorreu um problema de rede.', 'ri-error-warning-line', 'border-red-500');
                } else {
                    alert('Erro: Ocorreu um problema de rede.');
                }
            }
        });
    }
});
