<?php

/**
 * Migration to create the digital_file_type_associations table
 * This table will store associations between digital files and file types
 */
function migrate_digital_file_type_associations()
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        // Check if foreign keys are enabled
        $fk_enabled_before = $pdo->query("PRAGMA foreign_keys")->fetchColumn();
        
        // Temporarily disable foreign keys for the migration
        $pdo->exec("PRAGMA foreign_keys = OFF");
        
        $pdo->beginTransaction();

        // Create the digital_file_type_associations table
        $pdo->exec("CREATE TABLE IF NOT EXISTS digital_file_type_associations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            digital_file_id INTEGER NOT NULL,
            file_type_id INTEGER NOT NULL,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            FOREIGN KEY (digital_file_id) REFERENCES digital_files(id) ON DELETE CASCADE,
            <PERSON>OR<PERSON><PERSON><PERSON> KEY (file_type_id) REFERENCES digital_product_file_types(id) ON DELETE CASCADE,
            UNIQUE (digital_file_id, file_type_id)
        )");

        // Restore foreign key setting
        if ($fk_enabled_before) {
            $pdo->exec("PRAGMA foreign_keys = ON");
        }

        $pdo->commit();
        return true;
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Migration error (digital_file_type_associations): " . $e->getMessage());
        return false;
    }
}
