<?php

$ini_file = 'C:/xampp/php/php.ini';

$ini_content = file_get_contents($ini_file);
if ($ini_content === false) {
    die("Could not read php.ini file at: $ini_file");
}

$ini_content = preg_replace('/post_max_size\s*=\s*\d+M/i', 'post_max_size = 100M', $ini_content);
$ini_content = preg_replace('/upload_max_filesize\s*=\s*\d+M/i', 'upload_max_filesize = 100M', $ini_content);

if (file_put_contents($ini_file, $ini_content) === false) {
    die("Could not write to php.ini file. Make sure you have permission to modify it.");
}

echo "PHP configuration updated successfully. Please restart your web server for changes to take effect.\n";
?>
