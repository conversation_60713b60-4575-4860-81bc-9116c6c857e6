<?php

defined('ADMIN_CONTEXT') or die('Direct access is not allowed.');

$categories = getAllCategories();
?>

<h1>Categorias de Produtos</h1>
<a href="admin.php?section=categories&action=new&<?= get_session_id_param() ?>" class="btn btn-success mb-3">
    <i class="bi bi-plus-circle"></i> Adicionar Nova Categoria
</a>

<?php display_flash_messages(); ?>

<div class="card">
    <div class="card-body">
        <h5 class="card-title">Lista de Categorias</h5>

        <?php if (empty($categories)): ?>
            <div class="alert alert-info">Nenhuma categoria encontrada.</div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Nome</th>
                            <th>Criado em</th>
                            <th>A<PERSON><PERSON><PERSON></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($categories as $category): ?>
                            <tr>
                                <td><?= htmlspecialchars($category['id']); ?></td>
                                <td><?= htmlspecialchars($category['name']); ?></td>
                                <td><?= htmlspecialchars(format_date($category['created_at'])); ?></td>
                                <td>
                                    <a href="admin.php?section=categories&action=edit&id=<?= $category['id']; ?>&<?= get_session_id_param() ?>" class="btn btn-sm btn-outline-primary" title="Editar">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <a href="admin.php?section=categories&action=delete&id=<?= $category['id']; ?>&<?= get_session_id_param() ?>"
                                       class="btn btn-sm btn-outline-danger"
                                       title="Eliminar"
                                       onclick="return confirm('Tem a certeza que pretende eliminar esta categoria?');">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>