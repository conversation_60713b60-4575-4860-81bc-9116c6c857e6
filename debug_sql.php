<?php

require_once 'includes/db.php';

$pdo = get_db_connection();

echo "Debugging SQL queries:" . PHP_EOL;

$stmt = $pdo->query("SELECT * FROM products WHERE product_type = 'digital'");
$products = $stmt->fetchAll();
echo "Digital products in products table: " . count($products) . PHP_EOL;
if (count($products) > 0) {
    echo "First digital product: " . print_r($products[0], true) . PHP_EOL;
}

$stmt = $pdo->query("SELECT * FROM digital_products");
$digital_products = $stmt->fetchAll();
echo "Records in digital_products table: " . count($digital_products) . PHP_EOL;
if (count($digital_products) > 0) {
    echo "First digital product record: " . print_r($digital_products[0], true) . PHP_EOL;
}

$stmt = $pdo->query("SELECT * FROM digital_files");
$digital_files = $stmt->fetchAll();
echo "Records in digital_files table: " . count($digital_files) . PHP_EOL;
if (count($digital_files) > 0) {
    echo "First digital file record: " . print_r($digital_files[0], true) . PHP_EOL;
}

$sql = "SELECT p.*, dp.id as digital_product_id, dp.expiry_days, dp.download_limit, dp.digital_file_id,
               df.file_path, df.original_filename AS df_original_filename, df.display_name AS df_display_name
        FROM products p
        JOIN digital_products dp ON p.id = dp.product_id
        LEFT JOIN digital_files df ON dp.digital_file_id = df.id
        WHERE p.product_type = 'digital'
        ORDER BY p.name_pt ASC";

$stmt = $pdo->query($sql);
$results = $stmt->fetchAll();
echo "Direct SQL query results: " . count($results) . PHP_EOL;
if (count($results) > 0) {
    echo "First result: " . print_r($results[0], true) . PHP_EOL;
}

$sql = "SELECT dp.* 
        FROM digital_products dp 
        LEFT JOIN products p ON dp.product_id = p.id 
        WHERE p.id IS NULL";
$stmt = $pdo->query($sql);
$orphaned = $stmt->fetchAll();
echo "Orphaned digital_products records: " . count($orphaned) . PHP_EOL;
if (count($orphaned) > 0) {
    echo "First orphaned record: " . print_r($orphaned[0], true) . PHP_EOL;
}

$sql = "SELECT p.* 
        FROM products p 
        LEFT JOIN digital_products dp ON p.id = dp.product_id 
        WHERE p.product_type = 'digital' AND dp.id IS NULL";
$stmt = $pdo->query($sql);
$missing = $stmt->fetchAll();
echo "Digital products without digital_products record: " . count($missing) . PHP_EOL;
if (count($missing) > 0) {
    echo "First missing record: " . print_r($missing[0], true) . PHP_EOL;
}
