<?php
// <PERSON>ript to remove all error_log and console.log statements from all PHP files

// Files with error_log statements
$error_log_files = [
    'config.php',
    'includes/blog_functions.php',
    'includes/coupon_functions.php',
    'includes/custom_field_cleanup.php',
    'includes/custom_field_fonts_handler.php',
    'includes/custom_field_functions.php',
    'includes/custom_field_handler.php',
    'includes/db_maintenance.php',
    'includes/db_migrations/digital_products_tables.php',
    'includes/digital_order_functions.php',
    'includes/digital_product_functions.php',
    'includes/order_functions.php',
    'includes/payment_methods.php',
    'includes/product_functions.php',
    'includes/product_info_fields.php',
    'includes/security.php',
    'includes/session.php',
    'includes/settings_handler.php',
    'includes/sitemap_functions.php',
    'includes/vat_functions.php',
    'phpmailer_lib/PHPMailer.php',
    'phpmailer_lib/SMTP.php',
    'templates/backend/attribute_values_form.php',
    'templates/backend/order_detail.php',
    'templates/backend/payment_methods_list.php',
    'templates/backend/products_list.php',
    'templates/frontend/checkout.php',
    'templates/frontend/download.php',
    'templates/frontend/download_file.php',
    'templates/frontend/order_success.php',
    'templates/frontend/product_detail.php'
];

// Files with console.log statements
$console_log_files = [
    'templates/backend/blog_post_form.php',
    'templates/backend/messages.php',
    'templates/backend/order_detail.php',
    'templates/backend/payment_methods_list.php',
    'templates/backend/product_form.php',
    'templates/backend/sitemaps_list.php',
    'templates/backend/sitemap_form.php',
    'templates/frontend/checkout.php',
    'templates/frontend/order_success.php',
    'templates/frontend/partials/footer.php',
    'templates/frontend/product_detail.php',
    'templates/frontend/search_results.php'
];

// Combine unique files
$all_files = array_unique(array_merge($error_log_files, $console_log_files));

// Remove our own script files
$all_files = array_diff($all_files, ['remove_all_logs.php', 'remove_all_logs_comprehensive.php']);

// Process each file
$total_error_logs_removed = 0;
$total_console_logs_removed = 0;
$processed_files = 0;
$failed_files = 0;

echo "Starting log removal process...\n";

foreach ($all_files as $file) {
    echo "Processing file: $file\n";
    
    // Skip if file doesn't exist
    if (!file_exists($file)) {
        echo "  Warning: File not found: $file\n";
        $failed_files++;
        continue;
    }
    
    // Read the file
    $content = file_get_contents($file);
    if ($content === false) {
        echo "  Error: Could not read file: $file\n";
        $failed_files++;
        continue;
    }
    
    // Count original error_log statements
    preg_match_all('/error_log\(.*?\);/', $content, $error_matches);
    $original_error_count = count($error_matches[0]);
    
    // Count original console.log statements
    preg_match_all('/console\.log\(.*?\);/', $content, $console_matches);
    $original_console_count = count($console_matches[0]);
    
    // Replace error_log statements
    $content = preg_replace('/\s*error_log\(.*?\);/', '', $content);
    
    // Replace console.log statements
    $content = preg_replace('/\s*console\.log\(.*?\);/', '', $content);
    
    // Write the file back
    if (file_put_contents($file, $content) === false) {
        echo "  Error: Could not write to file: $file\n";
        $failed_files++;
        continue;
    }
    
    // Count remaining error_log statements
    preg_match_all('/error_log\(.*?\);/', $content, $error_matches);
    $remaining_error_count = count($error_matches[0]);
    
    // Count remaining console.log statements
    preg_match_all('/console\.log\(.*?\);/', $content, $console_matches);
    $remaining_console_count = count($console_matches[0]);
    
    $error_logs_removed = $original_error_count - $remaining_error_count;
    $console_logs_removed = $original_console_count - $remaining_console_count;
    
    echo "  Removed $error_logs_removed error_log statements\n";
    echo "  Removed $console_logs_removed console.log statements\n";
    
    $total_error_logs_removed += $error_logs_removed;
    $total_console_logs_removed += $console_logs_removed;
    $processed_files++;
}

echo "\nSummary:\n";
echo "Files processed: $processed_files\n";
echo "Files failed: $failed_files\n";
echo "Total error_log statements removed: $total_error_logs_removed\n";
echo "Total console.log statements removed: $total_console_logs_removed\n";
echo "Total logs removed: " . ($total_error_logs_removed + $total_console_logs_removed) . "\n";
