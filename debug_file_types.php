<?php
// This is a debug script to check the database tables related to file types

// Include necessary files
require_once __DIR__ . '/includes/init.php';
require_once __DIR__ . '/includes/digital_files_functions.php';

// Check if user is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    die("Not authorized");
}

// Get file ID from query string
$file_id = isset($_GET['file_id']) ? (int)$_GET['file_id'] : 0;

// Set content type to HTML
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Type Debug</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .debug-section { margin-bottom: 30px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>File Type Debug</h1>
        
        <div class="debug-section">
            <h2>Database Tables</h2>
            
            <h3>digital_product_file_types</h3>
            <?php
            $file_types = db_query("SELECT * FROM digital_product_file_types", [], false, true);
            if (is_array($file_types) && !empty($file_types)) {
                echo '<pre>' . print_r($file_types, true) . '</pre>';
            } else {
                echo '<div class="alert alert-warning">No file types found</div>';
            }
            ?>
            
            <h3>digital_file_type_associations</h3>
            <?php
            $file_type_assocs = db_query("SELECT * FROM digital_file_type_associations", [], false, true);
            if (is_array($file_type_assocs) && !empty($file_type_assocs)) {
                echo '<pre>' . print_r($file_type_assocs, true) . '</pre>';
            } else {
                echo '<div class="alert alert-warning">No file type associations found</div>';
            }
            ?>
            
            <h3>digital_product_file_type_associations</h3>
            <?php
            $product_file_type_assocs = db_query("SELECT * FROM digital_product_file_type_associations", [], false, true);
            if (is_array($product_file_type_assocs) && !empty($product_file_type_assocs)) {
                echo '<pre>' . print_r($product_file_type_assocs, true) . '</pre>';
            } else {
                echo '<div class="alert alert-warning">No product file type associations found</div>';
            }
            ?>
        </div>
        
        <?php if ($file_id > 0): ?>
        <div class="debug-section">
            <h2>File ID: <?= $file_id ?></h2>
            
            <?php
            $file = get_digital_file_by_id($file_id);
            if ($file) {
                echo '<h3>File Details</h3>';
                echo '<pre>' . print_r($file, true) . '</pre>';
                
                $file_type_ids = get_digital_file_file_type_ids($file_id);
                echo '<h3>File Type IDs</h3>';
                echo '<pre>' . print_r($file_type_ids, true) . '</pre>';
                
                $products = db_query(
                    "SELECT p.id, p.name_pt FROM products p
                     JOIN digital_products dp ON p.id = dp.product_id
                     WHERE dp.digital_file_id = :file_id",
                    [':file_id' => $file_id],
                    false,
                    true
                );
                
                echo '<h3>Products Using This File</h3>';
                if (is_array($products) && !empty($products)) {
                    echo '<pre>' . print_r($products, true) . '</pre>';
                    
                    foreach ($products as $product) {
                        $product_id = (int)$product['id'];
                        $digital_product = db_query(
                            "SELECT id FROM digital_products WHERE product_id = :product_id",
                            [':product_id' => $product_id],
                            true
                        );
                        
                        if ($digital_product) {
                            $digital_product_id = (int)$digital_product['id'];
                            $product_file_types = get_digital_product_file_type_ids($digital_product_id);
                            
                            echo '<h4>Product ID: ' . $product_id . ' (Digital Product ID: ' . $digital_product_id . ')</h4>';
                            echo '<pre>' . print_r($product_file_types, true) . '</pre>';
                        }
                    }
                } else {
                    echo '<div class="alert alert-warning">No products using this file</div>';
                }
            } else {
                echo '<div class="alert alert-danger">File not found</div>';
            }
            ?>
        </div>
        <?php endif; ?>
        
        <div class="debug-section">
            <h2>Check Another File</h2>
            <form method="get" action="debug_file_types.php">
                <div class="mb-3">
                    <label for="file_id" class="form-label">File ID</label>
                    <input type="number" class="form-control" id="file_id" name="file_id" value="<?= $file_id ?>">
                </div>
                <button type="submit" class="btn btn-primary">Check</button>
            </form>
        </div>
    </div>
</body>
</html>
