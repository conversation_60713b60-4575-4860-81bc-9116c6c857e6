

document.addEventListener('DOMContentLoaded', function() {
    
    const imagesContainer = document.getElementById('existing-images-container');
    if (!imagesContainer) return;

    
    const urlParams = new URLSearchParams(window.location.search);
    const productId = urlParams.get('id');
    const isEditing = urlParams.get('action') === 'edit' && productId;

    if (!isEditing) return;

    
    let isDragging = false;
    let draggedElement = null;
    let initialPosition = 0;
    let currentPosition = 0;

    
    const imageItems = imagesContainer.querySelectorAll('.existing-image-item');
    imageItems.forEach(item => {
        item.setAttribute('draggable', 'true');
        item.classList.add('draggable-image');

        
        const cardBody = item.querySelector('.card-body');
        if (cardBody) {
            const dragHandle = document.createElement('div');
            dragHandle.className = 'drag-handle';
            dragHandle.innerHTML = '<i class="bi bi-grip-vertical"></i>';
            dragHandle.style.cursor = 'move';
            dragHandle.style.position = 'absolute';
            dragHandle.style.top = '5px';
            dragHandle.style.right = '5px';
            cardBody.style.position = 'relative';
            cardBody.appendChild(dragHandle);
        }
    });

    
    imageItems.forEach((item, index) => {
        item.addEventListener('dragstart', function(e) {
            isDragging = true;
            draggedElement = this;
            initialPosition = index;

            
            setTimeout(() => {
                this.classList.add('dragging');
            }, 0);

            
            e.dataTransfer.effectAllowed = 'move';
            e.dataTransfer.setData('text/plain', this.id);
        });

        item.addEventListener('dragend', function() {
            isDragging = false;
            this.classList.remove('dragging');

            
            if (initialPosition !== currentPosition) {
                updateImageOrder();
            }
        });

        item.addEventListener('dragover', function(e) {
            if (isDragging && this !== draggedElement) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
            }
        });

        item.addEventListener('dragenter', function(e) {
            if (isDragging && this !== draggedElement) {
                e.preventDefault();
                this.classList.add('drag-over');
            }
        });

        item.addEventListener('dragleave', function() {
            this.classList.remove('drag-over');
        });

        item.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('drag-over');

            if (isDragging && this !== draggedElement) {
                
                const dropIndex = Array.from(imagesContainer.children).indexOf(this);
                currentPosition = dropIndex;

                
                if (initialPosition < dropIndex) {
                    this.parentNode.insertBefore(draggedElement, this.nextSibling);
                } else {
                    this.parentNode.insertBefore(draggedElement, this);
                }
            }
        });
    });

    
    function updateImageOrder() {
        
        const imageOrder = Array.from(imagesContainer.children).map(item => {
            return item.id.replace('image-', '');
        });

        
        const formData = new FormData();
        formData.append('section', 'products'); 
        formData.append('action', 'update_image_order');
        formData.append('product_id', productId);
        formData.append('image_order', JSON.stringify(imageOrder));
        formData.append('csrf_token', document.querySelector('input[name="csrf_token"]').value);

        
        async function adminAjaxRequest(url, options = {}) {
            const defaultOptions = {
                method: 'POST',
                headers: { 'X-Requested-With': 'XMLHttpRequest' },
            };
            const mergedOptions = { ...defaultOptions, ...options };

            
            let fullUrl = `admin.php?section=ajax&${new URLSearchParams(window.location.search).get('sid') ? 'sid=' + new URLSearchParams(window.location.search).get('sid') : ''}`;

            try {
                const response = await fetch(fullUrl, mergedOptions);
                if (!response.ok) throw new Error(`HTTP error ${response.status}`);
                return await response.json();
            } catch (error) {
                console.error('AJAX Error:', error);
                return { success: false, message: error.message };
            }
        }

        
        adminAjaxRequest('', {
            method: 'POST',
            body: formData
        }).then(response => {
            if (response.success) {
                
                const flashContainer = document.querySelector('.flash-messages');
                if (flashContainer) {
                    const alert = document.createElement('div');
                    alert.className = 'alert alert-success alert-dismissible fade show';
                    alert.innerHTML = `
                        <strong>Sucesso!</strong> Ordem das imagens atualizada.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;
                    flashContainer.appendChild(alert);

                    
                    setTimeout(() => {
                        alert.classList.remove('show');
                        setTimeout(() => alert.remove(), 150);
                    }, 3000);
                }

                
                const firstImageId = imageOrder[0];
                const defaultRadio = document.querySelector(`#default_img_${firstImageId}`);

                
                if (defaultRadio && !defaultRadio.checked) {
                    defaultRadio.click();
                }

                
                document.querySelectorAll('.default-image-indicator').forEach(indicator => {
                    indicator.remove();
                });

                
                const firstImageCard = document.querySelector(`#image-${firstImageId} .card`);
                if (firstImageCard) {
                    const indicator = document.createElement('div');
                    indicator.className = 'default-image-indicator';
                    indicator.textContent = 'Padrão';
                    firstImageCard.prepend(indicator);
                }
            } else {
                console.error('Failed to update image order:', response.message);
                alert('Erro ao atualizar a ordem das imagens. Por favor, tente novamente.');
            }
        });
    }

    
    const defaultRadios = document.querySelectorAll('input[name="default_image_id"]');
    defaultRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                
                document.querySelectorAll('.default-image-indicator').forEach(indicator => {
                    indicator.remove();
                });

                
                const imageId = this.value;
                const imageCard = document.querySelector(`#image-${imageId} .card`);
                if (imageCard) {
                    const indicator = document.createElement('div');
                    indicator.className = 'default-image-indicator';
                    indicator.textContent = 'Padrão';
                    imageCard.prepend(indicator);
                }
            }
        });
    });
});
