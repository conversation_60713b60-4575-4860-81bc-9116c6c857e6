<?php

require_once 'includes/db.php';

try {
    $pdo = get_db_connection();
    
    
    $stmt = $pdo->query("PRAGMA table_info(blog_posts)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $column_exists = false;
    
    foreach ($columns as $column) {
        if ($column['name'] === 'image_description') {
            $column_exists = true;
            break;
        }
    }
    
    if (!$column_exists) {
        
        $pdo->exec("ALTER TABLE blog_posts ADD COLUMN image_description TEXT");
        echo "Column 'image_description' added successfully to blog_posts table.";
    } else {
        echo "Column 'image_description' already exists in blog_posts table.";
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
