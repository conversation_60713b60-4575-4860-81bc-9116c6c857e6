<?php
require_once 'includes/db.php';

// Get database connection
$pdo = get_db_connection();

// Function to check if a table exists
function table_exists($pdo, $table_name) {
    $stmt = $pdo->prepare("SELECT name FROM sqlite_master WHERE type='table' AND name=:table_name");
    $stmt->execute([':table_name' => $table_name]);
    return $stmt->fetch() !== false;
}

// Check if digital_file_type_associations table exists
$table_name = 'digital_file_type_associations';
if (table_exists($pdo, $table_name)) {
    echo "<p>Table '$table_name' exists.</p>";
    
    // Check table structure
    $stmt = $pdo->query("PRAGMA table_info($table_name)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Table Structure:</h3>";
    echo "<pre>";
    print_r($columns);
    echo "</pre>";
    
    // Check table content
    $stmt = $pdo->query("SELECT * FROM $table_name");
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Table Content:</h3>";
    echo "<pre>";
    print_r($rows);
    echo "</pre>";
} else {
    echo "<p>Table '$table_name' does not exist.</p>";
    
    // Create the table
    echo "<p>Creating table '$table_name'...</p>";
    try {
        $pdo->exec("CREATE TABLE $table_name (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            digital_file_id INTEGER NOT NULL,
            file_type_id INTEGER NOT NULL,
            created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
            FOREIGN KEY (digital_file_id) REFERENCES digital_files(id) ON DELETE CASCADE,
            FOREIGN KEY (file_type_id) REFERENCES digital_files_file_types(id) ON DELETE CASCADE,
            UNIQUE(digital_file_id, file_type_id)
        )");
        echo "<p>Table created successfully.</p>";
    } catch (Exception $e) {
        echo "<p>Error creating table: " . $e->getMessage() . "</p>";
    }
}

// Check if digital_files_file_types table exists
$table_name = 'digital_files_file_types';
if (table_exists($pdo, $table_name)) {
    echo "<p>Table '$table_name' exists.</p>";
    
    // Check table structure
    $stmt = $pdo->query("PRAGMA table_info($table_name)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Table Structure:</h3>";
    echo "<pre>";
    print_r($columns);
    echo "</pre>";
    
    // Check table content
    $stmt = $pdo->query("SELECT * FROM $table_name");
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Table Content:</h3>";
    echo "<pre>";
    print_r($rows);
    echo "</pre>";
} else {
    echo "<p>Table '$table_name' does not exist.</p>";
}

// Check if digital_files table exists
$table_name = 'digital_files';
if (table_exists($pdo, $table_name)) {
    echo "<p>Table '$table_name' exists.</p>";
    
    // Check table structure
    $stmt = $pdo->query("PRAGMA table_info($table_name)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Table Structure:</h3>";
    echo "<pre>";
    print_r($columns);
    echo "</pre>";
    
    // Check table content
    $stmt = $pdo->query("SELECT * FROM $table_name LIMIT 5");
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Table Content (first 5 rows):</h3>";
    echo "<pre>";
    print_r($rows);
    echo "</pre>";
} else {
    echo "<p>Table '$table_name' does not exist.</p>";
}

// Check if digital_products table exists
$table_name = 'digital_products';
if (table_exists($pdo, $table_name)) {
    echo "<p>Table '$table_name' exists.</p>";
    
    // Check table structure
    $stmt = $pdo->query("PRAGMA table_info($table_name)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Table Structure:</h3>";
    echo "<pre>";
    print_r($columns);
    echo "</pre>";
    
    // Check table content
    $stmt = $pdo->query("SELECT * FROM $table_name LIMIT 5");
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Table Content (first 5 rows):</h3>";
    echo "<pre>";
    print_r($rows);
    echo "</pre>";
} else {
    echo "<p>Table '$table_name' does not exist.</p>";
}
