<?php
require_once 'includes/db.php';
require_once 'includes/digital_files_functions.php';

// Get database connection
$pdo = get_db_connection();

// Check digital_file_type_associations table
echo "<h2>Digital File Type Associations</h2>";
try {
    $stmt = $pdo->query("SELECT * FROM digital_file_type_associations");
    $associations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<pre>";
    print_r($associations);
    echo "</pre>";
} catch (Exception $e) {
    echo "Error querying digital_file_type_associations: " . $e->getMessage();
}

// Check digital_files table
echo "<h2>Digital Files</h2>";
try {
    $stmt = $pdo->query("SELECT * FROM digital_files");
    $files = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<pre>";
    print_r($files);
    echo "</pre>";
} catch (Exception $e) {
    echo "Error querying digital_files: " . $e->getMessage();
}

// Check digital_products table
echo "<h2>Digital Products</h2>";
try {
    $stmt = $pdo->query("SELECT * FROM digital_products");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<pre>";
    print_r($products);
    echo "</pre>";
} catch (Exception $e) {
    echo "Error querying digital_products: " . $e->getMessage();
}

// Check digital_files_file_types table
echo "<h2>Digital Files File Types</h2>";
try {
    $stmt = $pdo->query("SELECT * FROM digital_files_file_types");
    $file_types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<pre>";
    print_r($file_types);
    echo "</pre>";
} catch (Exception $e) {
    echo "Error querying digital_files_file_types: " . $e->getMessage();
}

// Test the get_digital_file_file_types function
echo "<h2>Testing get_digital_file_file_types Function</h2>";
try {
    // Get all digital files
    $stmt = $pdo->query("SELECT id FROM digital_files");
    $file_ids = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($file_ids as $file_id) {
        echo "<h3>File Types for Digital File ID: $file_id</h3>";
        $file_types = get_digital_file_file_types($file_id);
        echo "<pre>";
        print_r($file_types);
        echo "</pre>";
    }
} catch (Exception $e) {
    echo "Error testing get_digital_file_file_types: " . $e->getMessage();
}
