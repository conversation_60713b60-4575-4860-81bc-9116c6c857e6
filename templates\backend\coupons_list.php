<?php

$coupons = get_all_coupons();
?>

<h1><PERSON><PERSON><PERSON></h1>
<a href="admin.php?section=coupons&action=new&<?= get_session_id_param() ?>" class="btn btn-success mb-3">
    <i class="bi bi-plus-circle"></i> Criar Novo Cupão
</a>

<?php display_flash_messages(); ?>

<div class="card">
    <div class="card-body">
        <h5 class="card-title">Lista de Cupões</h5>
        
        <?php if (empty($coupons)): ?>
            <div class="alert alert-info">
                Não existem cupões. Clique no botão acima para criar um novo cupão.
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Código</th>
                            <th>Tipo</th>
                            <th>Valor</th>
                            <th>Valor <PERSON></th>
                            <th>Limite de Uso</th>
                            <th>Usado</th>
                            <th>Estado</th>
                            <th>Validade</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($coupons as $coupon): ?>
                            <tr>
                                <td><?= $coupon['id'] ?></td>
                                <td><strong><?= htmlspecialchars($coupon['code']) ?></strong></td>
                                <td>
                                    <?php if ($coupon['discount_type'] === 'percentage'): ?>
                                        <span class="badge bg-info">Percentagem</span>
                                    <?php else: ?>
                                        <span class="badge bg-primary">Valor Fixo</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($coupon['discount_type'] === 'percentage'): ?>
                                        <?= number_format($coupon['discount_value'], 0) ?>%
                                    <?php else: ?>
                                        <?= format_price($coupon['discount_value']) ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?= $coupon['min_order_value'] > 0 ? format_price($coupon['min_order_value']) : '-' ?>
                                </td>
                                <td>
                                    <?= $coupon['usage_limit'] !== null ? $coupon['usage_limit'] : 'Ilimitado' ?>
                                </td>
                                <td><?= $coupon['usage_count'] ?></td>
                                <td>
                                    <?php if ($coupon['is_active']): ?>
                                        <span class="badge bg-success">Ativo</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Inativo</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $start_date = !empty($coupon['start_date']) ? date('d/m/Y', strtotime($coupon['start_date'])) : '-';
                                    $end_date = !empty($coupon['end_date']) ? date('d/m/Y', strtotime($coupon['end_date'])) : '-';
                                    echo $start_date . ' a ' . $end_date;
                                    ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="admin.php?section=coupons&action=edit&id=<?= $coupon['id'] ?>&<?= get_session_id_param() ?>" class="btn btn-primary" title="Editar">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger delete-coupon" data-id="<?= $coupon['id'] ?>" data-code="<?= htmlspecialchars($coupon['code']) ?>" title="Eliminar">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteCouponModal" tabindex="-1" aria-labelledby="deleteCouponModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteCouponModalLabel">Confirmar Eliminação</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Tem a certeza que deseja eliminar o cupão <strong id="couponCodeToDelete"></strong>?</p>
                <p class="text-danger">Esta ação não pode ser desfeita.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <form id="deleteCouponForm" method="POST" action="admin.php?section=coupons&action=delete&<?= get_session_id_param() ?>">
                    <?= csrf_input_field() ?>
                    <input type="hidden" name="coupon_id" id="couponIdToDelete" value="">
                    <button type="submit" class="btn btn-danger">Eliminar</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle delete button clicks
    const deleteButtons = document.querySelectorAll('.delete-coupon');
    const couponIdToDelete = document.getElementById('couponIdToDelete');
    const couponCodeToDelete = document.getElementById('couponCodeToDelete');
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteCouponModal'));
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const couponId = this.getAttribute('data-id');
            const couponCode = this.getAttribute('data-code');
            
            couponIdToDelete.value = couponId;
            couponCodeToDelete.textContent = couponCode;
            
            deleteModal.show();
        });
    });
});
</script>
